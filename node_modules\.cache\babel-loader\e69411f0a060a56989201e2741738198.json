{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js!E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\scene\\EditScene.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\scene\\EditScene.vue", "mtime": 1754036632809}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapState", "mapActions", "name", "data", "activeStep", "loading", "form", "scene_id", "linked_project_team_name", "linked_task_type_code", "scene_name", "scene_description", "input_platforms", "output_platforms", "input_platforms_data", "output_platforms_data", "input_data_options", "output_data_options", "input_effect_params", "input_effect_params_config", "updated_prompt", "scene_running_frequency", "hour", "modality", "platform_modalities", "platform_publish_forms", "day", "weeks", "stored_strategy_refresh_days", "explore_strategy_trigger_days", "scene_business_type", "baseline_data_start_days_ago", "baseline_data_exclude_recent_days", "min_baseline_sample_count", "baseline_refresh_frequency_days", "frequencyValue", "frequencyUnit", "rules", "required", "message", "trigger", "validator", "rule", "value", "callback", "console", "log", "Error", "type", "min", "max", "projectTeams", "taskTypes", "modalityOptions", "selectedInputPlatforms", "selectedOutputPlatforms", "sceneInputAccountsData", "sceneOutputAccountsData", "effectParamsData", "addOptionDialogVisible", "newOptionForm", "option_name", "option_key", "platform_id", "optionRules", "addingOption", "addProjectDialogVisible", "addTaskTypeDialogVisible", "newProjectForm", "project_name", "newTaskTypeForm", "task_type_name", "task_type_description", "recommended_effect_param_codes", "effect_param_relationships_note", "is_content_from_external", "is_bilateral_pref_consideration_needed", "linked_platform_ids", "task_type_status", "task_type_owner", "selectedPlatformIds", "selectedEffectParams", "availableEffectParamsForTaskType", "projectRules", "taskTypeRules", "addingProject", "addingTaskType", "sceneTypes", "loadingInputPlatforms", "loadingOutputPlatforms", "tempModalitySelection", "modalityTypes", "publishFormTypes", "computed", "availablePlatforms", "platforms", "selectedTaskType", "find", "taskType", "task_type_code", "linkedPlatformIds", "split", "map", "id", "parseInt", "trim", "filter", "platform", "includes", "availableEffectParams", "params", "JSON", "parse", "Array", "isArray", "error", "isPlatformConfigLoading", "methods", "getAvailablePublishForms", "platform_name", "toUpperCase", "handleModalitySelect", "platformId", "selectedModality", "$set", "push", "removeModality", "index", "indexOf", "splice", "removePublishForm", "fetchPlatformOptions", "response", "$http", "post", "api", "param", "fetchPlatformEffectParams", "getAvailableEffectParamsForPlatform", "recommendedParams", "p", "platformParams", "effectParams", "info", "length", "effect_param_name", "getFieldComponent", "componentMap", "getFieldProps", "field", "props", "placeholder", "label", "field_type", "rows", "multiple", "options", "handleInputPlatformChange", "platformIds", "Object", "keys", "for<PERSON>ach", "key", "startsWith", "endsWith", "$delete", "platformsWithDetails", "detailResponse", "$message", "additional_Information", "platformWithDetails", "fields", "fieldProp", "field_name", "accountData", "undefined", "adding_data_types", "selectedOptions", "item", "platformEffectParams", "values", "selectedPara<PERSON>", "effect_param_code", "configured_evaluation_days", "default_baseline_mean", "default_baseline_stddev", "handleOutputPlatformChange", "nextStep", "warning", "fieldsToValidate", "paramCode", "configPrefix", "validateFields", "valid", "validationPromises", "Promise", "resolve", "$refs", "validateField", "errorMessage", "all", "then", "results", "<PERSON><PERSON><PERSON><PERSON>", "some", "result", "prevStep", "submitForm", "validate", "accounts", "platformData", "dataOptions", "operate_type", "create_time", "Date", "toLocaleString", "replace", "join", "paramConfig", "output_effect_params", "output_effect_params_config", "submitData", "update_time", "$route", "effect_params", "success", "$router", "fetchSceneDetail", "sceneData", "account", "inputPlatforms", "outputPlatforms", "parseFrequencyFromMinutes", "calculateTotalMinutes", "$nextTick", "showAddOptionDialog", "optionForm", "clearValidate", "addNewOption", "openAIOptimize", "url", "window", "open", "handleDeleteScene", "$confirm", "confirmButtonText", "cancelButtonText", "fetchModalityOptions", "dict_type", "fetchModalityTypes", "fetchPublishFormTypes", "fetchProjectTeams", "fetchTaskTypes", "fetchSceneTypes", "handleProjectTeamChange", "handleTaskTypeChange", "handleEffectParamsChange", "effectParam", "pattern", "getEffectParamsTableData", "config", "ret", "updateEffectParamConfig", "paramName", "getMinValue", "getMaxValue", "getStep", "totalMinutes", "minutes", "handleFrequencyValueChange", "handleFrequencyUnitChange", "currentMinutes", "showAddProjectDialog", "projectForm", "addNewProject", "newProject", "project", "showAddTaskTypeDialog", "taskTypeForm", "handlePlatformSelectChange", "selectedIds", "fetchEffectParamsForTaskType", "platformParamsArrays", "allParams", "seenCodes", "Set", "has", "add", "handleEffectParamsSelectChange", "selectedCodes", "stringify", "addNewTaskType", "created", "fetchPlatforms", "page", "pageSize"], "sources": ["src/views/scene/EditScene.vue"], "sourcesContent": ["<template>\n    <div class=\"edit-scene\">\n        <el-breadcrumb separator=\"/\" class=\"edit-breadcrumb\" style=\"margin-bottom: 18px;\">\n            <el-breadcrumb-item :to=\"{ path: '/scene/manage' }\">场景管理</el-breadcrumb-item>\n            <el-breadcrumb-item :to=\"{ path: '/scene/edit/'+ form.scene_id }\">{{ form.scene_name }}</el-breadcrumb-item>\n            <el-breadcrumb-item>场景编辑</el-breadcrumb-item>\n        </el-breadcrumb>\n        <div class=\"edit-actions\">\n            <el-button type=\"primary\" size=\"small\" @click=\"openAIOptimize\" :disabled=\"!form.scene_id\">AI优化提示词</el-button>\n            <el-button type=\"danger\" size=\"small\" @click=\"handleDeleteScene\" :disabled=\"!form.scene_id\">删除场景</el-button>\n        </div>\n        <el-steps :active=\"activeStep\" finish-status=\"success\" simple>\n            <el-step title=\"基本信息\" />\n            <el-step title=\"计算基准线配置\" />\n            <el-step title=\"数据输入平台\" />\n            <el-step title=\"数据输出平台\" />\n            <el-step title=\"其他设置\" />\n        </el-steps>\n\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"150px\" class=\"mt-20\" v-loading=\"loading\">\n            <div v-show=\"activeStep === 0\">\n                <el-form-item label=\"场景名称\" prop=\"scene_name\">\n                    <el-input v-model=\"form.scene_name\" placeholder=\"请输入场景名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型\" prop=\"linked_task_type_code\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_task_type_code\" placeholder=\"请选择任务类型\" style=\"flex: 1;\" @change=\"handleTaskTypeChange\">\n                            <el-option\n                                v-for=\"taskType in taskTypes\"\n                                :key=\"taskType.task_type_code\"\n                                :label=\"taskType.task_type_name\"\n                                :value=\"taskType.task_type_code\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddTaskTypeDialog\">\n                            新增任务类型\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景类型\" prop=\"scene_business_type\">\n                    <el-select \n                        v-model=\"form.scene_business_type\" \n                        placeholder=\"请选择或输入场景类型\" \n                        filterable \n                        allow-create \n                        default-first-option\n                        style=\"width: 100%\">\n                        <el-option\n                            v-for=\"sceneType in sceneTypes\"\n                            :key=\"sceneType.scene_type_id || sceneType.scene_type_code\"\n                            :label=\"sceneType.scene_type_name\"\n                            :value=\"sceneType.scene_type_name\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"项目组\" prop=\"linked_project_team_name\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_project_team_name\" placeholder=\"请选择项目组\" style=\"flex: 1;\" @change=\"handleProjectTeamChange\">\n                            <el-option\n                                v-for=\"project in projectTeams\"\n                                :key=\"project.project_id\"\n                                :label=\"project.project_name\"\n                                :value=\"project.project_name\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddProjectDialog\">\n                            新增项目组\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景描述\" prop=\"scene_description\">\n                    <el-input type=\"textarea\" v-model=\"form.scene_description\" placeholder=\"请输入场景描述\"\n                        :rows=\"4\"></el-input>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 1\">\n                <el-form-item label=\"使用数据的起始天数\" prop=\"baseline_data_start_days_ago\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_start_days_ago\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线使用数据的起始天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"排除最近的数据天数\" prop=\"baseline_data_exclude_recent_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_exclude_recent_days\"\n                            :min=\"0\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线排除最近的数据天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <!-- <el-form-item label=\"样本总数\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input \n                            value=\"将根据上述配置自动计算\" \n                            disabled \n                            style=\"width: 300px;\">\n                        </el-input>\n                        <span style=\"color: #909399; font-size: 12px;\">基于起始天数和排除天数范围内的数据量</span>\n                    </div>\n                </el-form-item> -->\n                <el-form-item label=\"样本量最小阈值\" prop=\"min_baseline_sample_count\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.min_baseline_sample_count\"\n                            :min=\"1\"\n                            :max=\"10000\"\n                            :step=\"1\"\n                            placeholder=\"请输入样本量\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">个</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"一个场景在一次计算基准线时，所需要的最小样本量\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"基准线更新频率\" prop=\"baseline_refresh_frequency_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_refresh_frequency_days\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入频率\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"评估效果的基准线更新的频率\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 2\">\n                <el-form-item label=\"选择输入平台\" prop=\"input_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.input_platforms\" @change=\"handleInputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingInputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedInputPlatforms && selectedInputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingInputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输入平台配置</h3>\n                    <el-card v-for=\"platform in selectedInputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <component :is=\"getFieldComponent(field.field_type)\"\n                                v-model=\"form.input_platforms_data[platform.platform_id][field.field_name]\"\n                                v-bind=\"getFieldProps(field)\"></component>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据类型\" :prop=\"'input_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.input_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\"\n                                    @click=\"showAddOptionDialog(platform.platform_id)\">\n                                    新增数据类型\n                                </el-button>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"效果参数配置\">\n                            <div class=\"effect-params-container\">\n                                <div v-if=\"getAvailableEffectParamsForPlatform(platform.platform_id).length === 0\" class=\"no-effect-params-tip\">\n                                    <el-alert\n                                        title=\"该平台暂无可用的效果参数\"\n                                        type=\"info\"\n                                        :closable=\"false\"\n                                        show-icon>\n                                    </el-alert>\n                                </div>\n                                <el-checkbox-group v-else v-model=\"form.input_effect_params[platform.platform_id]\" @change=\"handleEffectParamsChange(platform.platform_id)\">\n                                    <el-checkbox v-for=\"param in getAvailableEffectParamsForPlatform(platform.platform_id)\" :key=\"param.effect_param_code\" :label=\"param.effect_param_code\" class=\"effect-param-checkbox\">\n                                        {{ param.effect_param_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                \n                                <div v-if=\"form.input_effect_params[platform.platform_id] && form.input_effect_params[platform.platform_id].length > 0\" class=\"effect-params-table\">\n                                    <h4>参数配置详情</h4>\n                                    <el-table :data=\"getEffectParamsTableData(platform.platform_id)\" border>\n                                        <el-table-column prop=\"effect_param_name\" label=\"参数名称\" width=\"120\"></el-table-column>\n                                        <el-table-column prop=\"configured_evaluation_days\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"系统会获取发布时间在'T-基线'范围内，且已满足各参数的Tij 值的样本总数量。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *效果实现天数\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.configured_evaluation_days`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.configured_evaluation_days\" \n                                                        placeholder=\"如：3,5,10\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'configured_evaluation_days', scope.row.configured_evaluation_days)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_mean\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"μi是选取的样本量的效果数据里，该参数的平均值。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算μi，而是会使用您输入的缺省状态下的μi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *平均值\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_mean`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input\n                                                        v-model=\"scope.row.default_baseline_mean\" \n                                                        placeholder=\"如：0\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_mean', scope.row.default_baseline_mean)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_stddev\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"σi是选取的样本量的效果数据里，该参数的标准差。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算σi，而是会使用您输入的缺省状态下的σi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *标准差\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_stddev`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.default_baseline_stddev\" \n                                                        placeholder=\"如：1\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_stddev', scope.row.default_baseline_stddev)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                    </el-table>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.input_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n            </div>\n\n            <div v-show=\"activeStep === 3\">\n                <el-form-item label=\"选择输出平台\" prop=\"output_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.output_platforms\" @change=\"handleOutputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingOutputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedOutputPlatforms && selectedOutputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingOutputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输出平台配置</h3>\n                    <el-card v-for=\"platform in selectedOutputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <component :is=\"getFieldComponent(field.field_type)\"\n                                v-model=\"form.output_platforms_data[platform.platform_id][field.field_name]\"\n                                v-bind=\"getFieldProps(field)\"></component>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据内容\" :prop=\"'output_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.output_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\"\n                                    @click=\"showAddOptionDialog(platform.platform_id)\">\n                                    新增数据内容\n                                </el-button>\n                            </div>\n                        </el-form-item>\n\n\n\n                        <el-form-item label=\"多模态内容\" :prop=\"'platform_modalities.' + platform.platform_id\">\n                            <div class=\"modality-selection-container\">\n                                <div class=\"selection-row\">\n                                    <el-select\n                                        v-model=\"tempModalitySelection[platform.platform_id]\"\n                                        placeholder=\"请选择模态类型\"\n                                        style=\"width: 300px;\"\n                                        @change=\"handleModalitySelect(platform.platform_id, $event)\">\n                                        <el-option\n                                            v-for=\"modality in modalityTypes\"\n                                            :key=\"modality.dict_name\"\n                                            :label=\"modality.dict_name\"\n                                            :value=\"modality.dict_name\"\n                                            :disabled=\"form.platform_modalities[platform.platform_id] && form.platform_modalities[platform.platform_id].includes(modality.dict_name)\">\n                                        </el-option>\n                                    </el-select>\n                                    <div class=\"selected-tags\">\n                                        <el-tag\n                                            v-for=\"modality in form.platform_modalities[platform.platform_id] || []\"\n                                            :key=\"modality\"\n                                            closable\n                                            @close=\"removeModality(platform.platform_id, modality)\"\n                                            style=\"margin-right: 8px; margin-bottom: 8px;\">\n                                            {{ modality }}\n                                        </el-tag>\n                                    </div>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"发布形态\" :prop=\"'platform_publish_forms.' + platform.platform_id\">\n                            <div class=\"publish-form-selection-container\">\n                                <div class=\"selection-row\">\n                                    <el-select\n                                        v-model=\"form.platform_publish_forms[platform.platform_id]\"\n                                        placeholder=\"请选择发布形态\"\n                                        style=\"width: 300px;\">\n                                        <el-option\n                                            v-for=\"publishForm in getAvailablePublishForms(platform)\"\n                                            :key=\"publishForm.dict_name\"\n                                            :label=\"publishForm.dict_name\"\n                                            :value=\"publishForm.dict_name\">\n                                        </el-option>\n                                    </el-select>\n                                    <div class=\"selected-tags\">\n                                        <el-tag\n                                            v-if=\"form.platform_publish_forms[platform.platform_id]\"\n                                            closable\n                                            @close=\"removePublishForm(platform.platform_id)\"\n                                            style=\"margin-left: 8px;\">\n                                            {{ form.platform_publish_forms[platform.platform_id] }}\n                                        </el-tag>\n                                    </div>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.output_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n\n                <!-- <el-form-item label=\"模态\" prop=\"modality\">\n                    <el-select v-model=\"form.modality\" placeholder=\"请选择模态\">\n                        <el-option\n                            v-for=\"item in modalityOptions\"\n                            :key=\"item.dict_name\"\n                            :label=\"item.dict_name\"\n                            :value=\"item.dict_name\">\n                        </el-option>\n                    </el-select>\n                </el-form-item> -->\n            </div>\n\n            <div v-show=\"activeStep === 4\">\n                <el-form-item label=\"运行频率\" prop=\"scene_running_frequency\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-input-number\n                            v-model=\"frequencyValue\"\n                            :min=\"getMinValue()\"\n                            :max=\"getMaxValue()\"\n                            :step=\"getStep()\"\n                            placeholder=\"请输入数值\"\n                            style=\"width: 150px\"\n                            @change=\"handleFrequencyValueChange\">\n                        </el-input-number>\n                        <el-select \n                            v-model=\"frequencyUnit\" \n                            placeholder=\"请选择单位\"\n                            style=\"width: 120px\"\n                            @change=\"handleFrequencyUnitChange\">\n                            <el-option label=\"分钟\" value=\"minutes\"></el-option>\n                            <el-option label=\"小时\" value=\"hours\"></el-option>\n                            <el-option label=\"天\" value=\"days\"></el-option>\n                        </el-select>\n                        <span style=\"color: #909399; font-size: 12px;\">\n                            (最小间隔30分钟)\n                        </span>\n                    </div>\n                </el-form-item>\n                <!-- <el-form-item label=\"运行时间\" prop=\"time_config\">\n                    <el-row>\n                        <el-col :span=\"8\">\n                            <el-time-picker\n                                v-model=\"form.hour\"\n                                format=\"HH:00\"\n                                :picker-options=\"{\n                                    selectableRange: '00:00:00 - 23:00:00',\n                                    format: 'HH:00'\n                                }\"\n                                placeholder=\"请选择运行时间\"\n                                style=\"width: 100%\">\n                            </el-time-picker>\n                        </el-col>\n                        <el-col :span=\"8\" v-if=\"form.scene_running_frequency === '每月一次'\">\n                            <el-select v-model=\"form.day\" placeholder=\"请选择运行日期\" style=\"width: 100%\">\n                                <el-option\n                                    v-for=\"day in 31\"\n                                    :key=\"day\"\n                                    :label=\"`${day}日`\"\n                                    :value=\"day\">\n                                </el-option>\n                            </el-select>\n                        </el-col>\n                        <el-col :span=\"8\" v-if=\"['每周一次', '每两周一次'].includes(form.scene_running_frequency)\">\n                            <el-select v-model=\"form.weeks\" placeholder=\"请选择运行星期\" style=\"width: 100%\">\n                                <el-option label=\"星期一\" value=\"1\"></el-option>\n                                <el-option label=\"星期二\" value=\"2\"></el-option>\n                                <el-option label=\"星期三\" value=\"3\"></el-option>\n                                <el-option label=\"星期四\" value=\"4\"></el-option>\n                                <el-option label=\"星期五\" value=\"5\"></el-option>\n                                <el-option label=\"星期六\" value=\"6\"></el-option>\n                                <el-option label=\"星期日\" value=\"0\"></el-option>\n                            </el-select>\n                        </el-col>\n                    </el-row>\n                </el-form-item> -->\n                <el-form-item label=\"个性化进化更新频率\" prop=\"stored_strategy_refresh_days\">\n                    <el-input-number\n                        v-model=\"form.stored_strategy_refresh_days\"\n                        :min=\"0\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">建议您设为0</span>\n                </el-form-item>\n                <el-form-item label=\"AI自行探索频率\" prop=\"explore_strategy_trigger_days\">\n                    <el-input-number\n                        v-model=\"form.explore_strategy_trigger_days\"\n                        :min=\"1\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">目前我们的探索模式暂未上线，建议您先将Z值设为365天</span>\n                </el-form-item>\n                <el-form-item label=\"AI提示词\" prop=\"updated_prompt\">\n                    <el-input type=\"textarea\" v-model=\"form.updated_prompt\" placeholder=\"请输入AI提示词\" :rows=\"10\"></el-input>\n                </el-form-item>\n            </div>\n\n            <el-form-item class=\"navigation-buttons\">\n                <el-button v-if=\"activeStep > 0\" @click=\"prevStep\">上一步</el-button>\n                <el-button \n                    v-if=\"activeStep < 4\" \n                    type=\"primary\" \n                    @click=\"nextStep\"\n                    :disabled=\"isPlatformConfigLoading\"\n                    :loading=\"isPlatformConfigLoading\">\n                    {{ isPlatformConfigLoading ? '配置加载中...' : '下一步' }}\n                </el-button>\n                <el-button v-if=\"activeStep === 4\" type=\"primary\" @click=\"submitForm\">保存</el-button>\n            </el-form-item>\n        </el-form>\n\n        <el-dialog title=\"新增数据内容\" :visible.sync=\"addOptionDialogVisible\" width=\"500px\">\n            <el-form ref=\"optionForm\" :model=\"newOptionForm\" :rules=\"optionRules\" label-width=\"120px\">\n                <el-form-item label=\"内容名称\" prop=\"option_name\">\n                    <el-input v-model=\"newOptionForm.option_name\" placeholder=\"请输入内容名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"内容标识\" prop=\"option_key\">\n                    <el-input v-model=\"newOptionForm.option_key\" placeholder=\"请输入内容标识（英文）\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addOptionDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewOption\" :loading=\"addingOption\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增项目组\" :visible.sync=\"addProjectDialogVisible\" width=\"500px\">\n            <el-form ref=\"projectForm\" :model=\"newProjectForm\" :rules=\"projectRules\" label-width=\"120px\">\n                <el-form-item label=\"项目名称\" prop=\"project_name\">\n                    <el-input v-model=\"newProjectForm.project_name\" placeholder=\"请输入项目名称\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addProjectDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewProject\" :loading=\"addingProject\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增任务类型\" :visible.sync=\"addTaskTypeDialogVisible\" width=\"600px\">\n            <el-form ref=\"taskTypeForm\" :model=\"newTaskTypeForm\" :rules=\"taskTypeRules\" label-width=\"140px\">\n                <el-form-item label=\"任务类型名称\" prop=\"task_type_name\">\n                    <el-input v-model=\"newTaskTypeForm.task_type_name\" placeholder=\"请输入任务类型名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型描述\" prop=\"task_type_description\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.task_type_description\" placeholder=\"请输入任务类型描述\" :rows=\"3\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"关联平台\" prop=\"linked_platform_ids\">\n                    <el-select \n                        v-model=\"selectedPlatformIds\" \n                        multiple \n                        placeholder=\"请选择关联平台\"\n                        style=\"width: 100%\"\n                        @change=\"handlePlatformSelectChange\">\n                        <el-option\n                            v-for=\"platform in platforms\"\n                            :key=\"platform.platform_id\"\n                            :label=\"platform.platform_name\"\n                            :value=\"platform.platform_id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"推荐效果参数\" prop=\"recommended_effect_param_codes\">\n                    <el-select \n                        v-model=\"selectedEffectParams\" \n                        multiple \n                        placeholder=\"请选择推荐效果参数\"\n                        style=\"width: 100%\"\n                        @change=\"handleEffectParamsSelectChange\">\n                        <el-option\n                            v-for=\"param in availableEffectParamsForTaskType\"\n                            :key=\"param.effect_param_code\"\n                            :label=\"`${param.effect_param_name} (${param.effect_param_code})`\"\n                            :value=\"param.effect_param_name\">\n                        </el-option>\n                    </el-select>\n                    <div v-if=\"availableEffectParamsForTaskType.length === 0 && selectedPlatformIds.length > 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        所选平台暂无可用的效果参数\n                    </div>\n                    <div v-if=\"selectedPlatformIds.length === 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        请先选择关联平台\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"参数关系说明\" prop=\"effect_param_relationships_note\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.effect_param_relationships_note\" placeholder=\"请输入各推荐参数之间的逻辑关系说明\" :rows=\"3\"></el-input>\n                </el-form-item>\n                <!-- <el-form-item label=\"是否需要外部内容\" prop=\"is_content_from_external\">\n                    <el-select v-model=\"newTaskTypeForm.is_content_from_external\" placeholder=\"请选择\">\n                        <el-option label=\"是\" value=\"1\"></el-option>\n                        <el-option label=\"否\" value=\"0\"></el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"是否需要双边偏好\" prop=\"is_bilateral_pref_consideration_needed\">\n                    <el-select v-model=\"newTaskTypeForm.is_bilateral_pref_consideration_needed\" placeholder=\"请选择\">\n                        <el-option label=\"是\" value=\"1\"></el-option>\n                        <el-option label=\"否\" value=\"0\"></el-option>\n                    </el-select>\n                </el-form-item> -->\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addTaskTypeDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewTaskType\" :loading=\"addingTaskType\">确定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\n\nexport default {\n    name: 'EditScene',\n    data() {\n        return {\n            activeStep: 0,\n            loading: false,\n            form: {\n                scene_id: null,\n                linked_project_team_name: null,\n                linked_task_type_code: null,\n                scene_name: '',\n                scene_description: '',\n                input_platforms: [],\n                output_platforms: [],\n                input_platforms_data: {},\n                output_platforms_data: {},\n                input_data_options: {},\n                output_data_options: {},\n                input_effect_params: {},\n                input_effect_params_config: {},\n                updated_prompt: '',\n                scene_running_frequency: '',\n                hour: '',\n                modality: '',\n                platform_modalities: {}, // 新增：平台多模态配置\n                platform_publish_forms: {}, // 新增：平台发布形态配置\n                day: '',\n                weeks: '',\n                stored_strategy_refresh_days: 0,\n                explore_strategy_trigger_days: 365,\n                scene_business_type: '',\n                baseline_data_start_days_ago: 30,\n                baseline_data_exclude_recent_days: 3,\n                min_baseline_sample_count: 3,\n                baseline_refresh_frequency_days: 7\n            },\n            frequencyValue: 30,\n            frequencyUnit: 'minutes',\n            rules: {\n                linked_project_team_name: [\n                    { \n                        required: true, \n                        message: '请选择项目组', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            console.log('验证项目组:', value)\n                            if (!value) {\n                                callback(new Error('请选择项目组'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                linked_task_type_code: [\n                    { \n                        required: true, \n                        message: '请选择任务类型', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            console.log('验证任务类型:', value)\n                            if (!value) {\n                                callback(new Error('请选择任务类型'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                scene_name: [\n                    { required: true, message: '请输入场景名称', trigger: 'blur' }\n                ],\n                scene_description: [\n                    { required: false, message: '请输入场景描述', trigger: 'blur' }\n                ],\n                input_platforms: [\n                    { required: true, message: '请选择输入平台', trigger: 'change' }\n                ],\n                output_platforms: [\n                    { required: true, message: '请选择输出平台', trigger: 'change' }\n                ],\n                updated_prompt: [\n                    { required: true, message: '请输入AI提示词', trigger: 'blur' }\n                ],\n                scene_running_frequency: [\n                    { required: true, message: '请设置运行频率', trigger: 'change' },\n                    { \n                        type: 'number', \n                        min: 30, \n                        message: '运行频率最小间隔为30分钟', \n                        trigger: 'change' \n                    }\n                ],\n                stored_strategy_refresh_days: [\n                    { required: true, message: '请输入个性化进化策略更新频率', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-365之间的天数', trigger: 'blur' }\n                ],\n                explore_strategy_trigger_days: [\n                    { required: true, message: '请输入AI自行探索频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                scene_business_type: [\n                    { required: true, message: '请选择场景类型', trigger: 'change' }\n                ],\n                baseline_data_start_days_ago: [\n                    { required: true, message: '请输入使用数据的起始天数', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                baseline_data_exclude_recent_days: [\n                    { required: true, message: '请输入排除最近的数据天数', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-30之间的天数', trigger: 'blur' }\n                ],\n                min_baseline_sample_count: [\n                    { required: true, message: '请输入样本量最小阈值', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 10000, message: '请输入1-10000之间的数值', trigger: 'blur' }\n                ],\n                baseline_refresh_frequency_days: [\n                    { required: true, message: '请输入基准线更新频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ]\n            },\n            projectTeams: [], \n            taskTypes: [], \n            modalityOptions: [], \n            selectedInputPlatforms: [],\n            selectedOutputPlatforms: [],\n            sceneInputAccountsData: {}, \n            sceneOutputAccountsData: {}, \n            effectParamsData: {},\n            addOptionDialogVisible: false,\n            newOptionForm: {\n                option_name: '',\n                option_key: '',\n                platform_id: ''\n            },\n            optionRules: {\n                option_name: [\n                    { required: true, message: '请输入内容名称', trigger: 'blur' }\n                ],\n                option_key: [\n                    { required: true, message: '请输入内容标识（英文）', trigger: 'blur' }\n                ]\n            },\n            addingOption: false,\n            addProjectDialogVisible: false,\n            addTaskTypeDialogVisible: false,\n            newProjectForm: {\n                project_name: ''\n            },\n            newTaskTypeForm: {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            },\n            selectedPlatformIds: [], \n            selectedEffectParams: [], \n            availableEffectParamsForTaskType: [], \n            projectRules: {\n                project_name: [\n                    { required: true, message: '请输入项目名称', trigger: 'blur' }\n                ]\n            },\n            taskTypeRules: {\n                task_type_name: [\n                    { required: true, message: '请输入任务类型名称', trigger: 'blur' }\n                ],\n                task_type_description: [\n                    { required: true, message: '请输入任务类型描述', trigger: 'blur' }\n                ],\n                recommended_effect_param_codes: [\n                    { required: true, message: '请输入推荐效果参数编码', trigger: 'blur' }\n                ],\n                effect_param_relationships_note: [\n                    { required: true, message: '请输入各推荐参数之间的逻辑关系说明', trigger: 'blur' }\n                ]\n            },\n            addingProject: false,\n            addingTaskType: false,\n            sceneTypes: [],\n            loadingInputPlatforms: false,\n            loadingOutputPlatforms: false,\n            tempModalitySelection: {}, // 临时存储模态选择\n            modalityTypes: [], // 从API获取的模态类型\n            publishFormTypes: [] // 从API获取的发布形态类型\n        }\n    },\n    computed: {\n        ...mapState(['platforms']),\n        availablePlatforms() {\n            if (!this.platforms || !this.form.linked_task_type_code) {\n                return this.platforms\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.linked_platform_ids) {\n                return this.platforms\n            }\n            \n            const linkedPlatformIds = selectedTaskType.linked_platform_ids.split(',').map(id => parseInt(id.trim()))\n            return this.platforms.filter(platform => linkedPlatformIds.includes(platform.platform_id))\n        },\n        availableEffectParams() {\n            if (!this.form.linked_task_type_code|| !this.taskTypes) {\n                return []\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.recommended_effect_param_codes) {\n                return []\n            }\n            \n            try {\n                const params = JSON.parse(selectedTaskType.recommended_effect_param_codes)\n                return Array.isArray(params) ? params : []\n            } catch (error) {\n                console.error('解析效果参数失败:', error)\n                return []\n            }\n        },\n        isPlatformConfigLoading() {\n            if (this.activeStep === 2 && this.loadingInputPlatforms) {\n                return true\n            }\n            if (this.activeStep === 3 && this.loadingOutputPlatforms) {\n                return true\n            }\n            return false\n        }\n    },\n    methods: {\n        ...mapActions(['fetchPlatforms']),\n        // 获取平台可用的发布形态\n        getAvailablePublishForms(platform) {\n            // ECL平台可以选择所有类型\n            if (platform.platform_name && platform.platform_name.toUpperCase().includes('ECL')) {\n                return this.publishFormTypes\n            }\n            // 其他平台根据具体需求返回特定类型，这里暂时返回所有类型\n            // 后续可以根据平台类型进行更精细的控制\n            return this.publishFormTypes\n        },\n        // 处理模态选择\n        handleModalitySelect(platformId, selectedModality) {\n            if (!selectedModality) return\n\n            if (!this.form.platform_modalities[platformId]) {\n                this.$set(this.form.platform_modalities, platformId, [])\n            }\n\n            if (!this.form.platform_modalities[platformId].includes(selectedModality)) {\n                this.form.platform_modalities[platformId].push(selectedModality)\n            }\n\n            // 清空临时选择\n            this.$set(this.tempModalitySelection, platformId, '')\n        },\n        // 移除模态\n        removeModality(platformId, modality) {\n            if (this.form.platform_modalities[platformId]) {\n                const index = this.form.platform_modalities[platformId].indexOf(modality)\n                if (index > -1) {\n                    this.form.platform_modalities[platformId].splice(index, 1)\n                }\n            }\n        },\n        // 移除发布形态\n        removePublishForm(platformId) {\n            this.$set(this.form.platform_publish_forms, platformId, '')\n        },\n        async fetchPlatformOptions(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/option/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform options:', error)\n                return []\n            }\n        },\n        async fetchPlatformEffectParams(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/effectParamCategory/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform effect params:', error)\n                return []\n            }\n        },\n        getAvailableEffectParamsForPlatform(platformId) {\n            const recommendedParams = this.availableEffectParams || []\n            \n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n\n            console.info(platformParams)\n\n            if (platformParams.length === 0) {\n                return []\n            }\n            \n            // const platformParamCodes = platformParams.map(param => param.effect_param_name || param.effect_param_code)\n            \n            return platformParams.filter(param => recommendedParams.includes(param.effect_param_name))\n        },\n        getFieldComponent(type) {\n            const componentMap = {\n                'string': 'el-input',\n                'password': 'el-input',\n                'select': 'el-select',\n                'multiselect': 'el-select',\n                'number': 'el-input-number',\n                'bool': 'el-switch',\n                'textarea': 'el-input'\n            }\n            return componentMap[type] || 'el-input'\n        },\n        getFieldProps(field) {\n            const props = {\n                placeholder: `请输入${field.label}`\n            }\n            if (field.field_type === 'password') {\n                props.type = 'password'\n            }\n            if (field.field_type === 'textarea') {\n                props.type = 'textarea'\n                props.rows = 3\n            }\n            if (field.field_type === 'select' || field.field_type === 'multiselect') {\n                props.multiple = field.field_type === 'multiselect'\n                props.options = field.options || []\n            }\n            return props\n        },\n        async handleInputPlatformChange(platformIds) {\n            if (this.loadingInputPlatforms) {\n                return\n            }\n            this.loadingInputPlatforms = true\n            \n            try {\n                this.selectedInputPlatforms = []\n\n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('input_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n\n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n                        \n                        const effectParams = await this.fetchPlatformEffectParams(platformId)\n\n                        console.info(\"effectParams\", effectParams);\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options,\n                            effectParams: effectParams\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedInputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n\n                        if (!this.form.input_platforms_data[platformId]) {\n                            this.$set(this.form.input_platforms_data, platformId, {\n                                additional_Information: ''\n                            })\n                        }\n\n                        if (!this.form.input_data_options[platformId]) {\n                            this.$set(this.form.input_data_options, platformId, [])\n                        }\n\n                        if (!this.form.input_effect_params[platformId]) {\n                            this.$set(this.form.input_effect_params, platformId, [])\n                        }\n\n                        const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n                        if (platformWithDetails && platformWithDetails.fields) {\n                            platformWithDetails.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `input_platforms_data.${platformId}.${field.field_name}`\n                                    this.$set(this.rules, fieldProp, [\n                                        { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                    ])\n                                }\n                            })\n                        }\n\n                        if (this.sceneInputAccountsData[platformId]) {\n                            const accountData = this.sceneInputAccountsData[platformId]\n                            const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n\n                            for (const field of (platformWithDetails && platformWithDetails.fields) || []) {\n                                if (accountData[field.field_name] !== undefined) {\n                                    this.$set(this.form.input_platforms_data[platformId], field.field_name, accountData[field.field_name])\n                                }\n                            }\n\n                            if (accountData.additional_Information !== undefined) {\n                                this.$set(this.form.input_platforms_data[platformId], 'additional_Information', accountData.additional_Information)\n                            }\n\n                            if (accountData.adding_data_types && accountData.adding_data_types !== '无') {\n                                const selectedOptions = accountData.adding_data_types.split(',').filter(item => item.trim())\n                                this.$set(this.form.input_data_options, platformId, selectedOptions)\n                            }\n                        }\n\n                        if (this.effectParamsData) {\n                            const platformEffectParams = Object.values(this.effectParamsData).filter(param => param.platform_id === platformId)\n                            \n                            if (platformEffectParams.length > 0) {\n                                if (!this.form.input_effect_params[platformId]) {\n                                    this.$set(this.form.input_effect_params, platformId, [])\n                                }\n                                \n                                if (!this.form.input_effect_params_config[platformId]) {\n                                    this.$set(this.form.input_effect_params_config, platformId, {})\n                                }\n                                \n                                const selectedParams = []\n                                platformEffectParams.forEach(param => {\n                                    selectedParams.push(param.effect_param_code)\n                                    this.$set(this.form.input_effect_params_config[platformId], param.effect_param_code, {\n                                        effect_param_code: param.effect_param_code,\n                                        effect_param_name: param.effect_param_name,\n                                        configured_evaluation_days: param.configured_evaluation_days || '',\n                                        default_baseline_mean: param.default_baseline_mean || '',\n                                        default_baseline_stddev: param.default_baseline_stddev || ''\n                                    })\n                                })\n                                \n                                this.$set(this.form.input_effect_params, platformId, selectedParams)\n                            }\n                        }\n                }\n            } catch (error) {\n                console.error('Error in handleInputPlatformChange:', error)\n                this.$message.error('处理输入平台变化时出错')\n            } finally {\n                this.loadingInputPlatforms = false\n            }\n        },\n        async handleOutputPlatformChange(platformIds) {\n            if (this.loadingOutputPlatforms) {\n                return\n            }\n            this.loadingOutputPlatforms = true\n            \n            try {\n                this.selectedOutputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('output_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedOutputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n\n                        if (!this.form.output_platforms_data[platformId]) {\n                            this.$set(this.form.output_platforms_data, platformId, {\n                                additional_Information: ''\n                            })\n                        }\n\n                        if (!this.form.output_data_options[platformId]) {\n                            this.$set(this.form.output_data_options, platformId, [])\n                        }\n\n                        // 初始化新的模态和发布形态字段\n                        if (!this.form.platform_modalities[platformId]) {\n                            this.$set(this.form.platform_modalities, platformId, [])\n                        }\n\n                        if (!this.form.platform_publish_forms[platformId]) {\n                            this.$set(this.form.platform_publish_forms, platformId, '')\n                        }\n\n                        const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)\n                        if (platformWithDetails && platformWithDetails.fields) {\n                            platformWithDetails.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `output_platforms_data.${platformId}.${field.field_name}`\n                                    this.$set(this.rules, fieldProp, [\n                                        { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                    ])\n                                }\n                            })\n                        }\n\n                        if (this.sceneOutputAccountsData[platformId]) {\n                            const accountData = this.sceneOutputAccountsData[platformId]\n                            const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)\n\n                            for (const field of (platformWithDetails && platformWithDetails.fields) || []) {\n                                if (accountData[field.field_name] !== undefined) {\n                                    this.$set(this.form.output_platforms_data[platformId], field.field_name, accountData[field.field_name])\n                                }\n                            }\n\n                            if (accountData.additional_Information !== undefined) {\n                                this.$set(this.form.output_platforms_data[platformId], 'additional_Information', accountData.additional_Information)\n                            }\n\n                            if (accountData.adding_data_types && accountData.adding_data_types !== '无') {\n                                const selectedOptions = accountData.adding_data_types.split(',').filter(item => item.trim())\n                                this.$set(this.form.output_data_options, platformId, selectedOptions)\n                            }\n                        }\n                }\n            } catch (error) {\n                console.error('Error in handleOutputPlatformChange:', error)\n                this.$message.error('处理输出平台变化时出错')\n            } finally {\n                this.loadingOutputPlatforms = false\n            }\n        },\n        nextStep() {\n            if (this.isPlatformConfigLoading) {\n                this.$message.warning('平台配置正在加载中，请稍候...')\n                return\n            }\n\n            let fieldsToValidate = []\n\n            switch (this.activeStep) {\n                case 0:\n                    fieldsToValidate = ['linked_project_team_name', 'linked_task_type_code', 'scene_business_type', 'scene_name', 'scene_description']\n                    break\n                case 1:\n                    fieldsToValidate = ['baseline_data_start_days_ago', 'baseline_data_exclude_recent_days', 'min_baseline_sample_count', 'baseline_refresh_frequency_days']\n                    break\n                case 2:\n                    fieldsToValidate = ['input_platforms']\n                    \n                    this.selectedInputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `input_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n                        \n                        const selectedParams = this.form.input_effect_params[platform.platform_id] || []\n                        selectedParams.forEach(paramCode => {\n                            const configPrefix = `input_effect_params_config.${platform.platform_id}.${paramCode}`\n                            fieldsToValidate.push(`${configPrefix}.configured_evaluation_days`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_mean`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_stddev`)\n                        })\n                    })\n                    break\n                case 3:\n                    fieldsToValidate = ['output_platforms']\n\n                    this.selectedOutputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `output_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n\n                        // 验证模态选择\n                        if (!this.form.platform_modalities[platform.platform_id] ||\n                            this.form.platform_modalities[platform.platform_id].length === 0) {\n                            this.$message.error(`请为平台\"${platform.platform_name}\"选择至少一种模态类型`)\n                            return false\n                        }\n\n                        // 验证发布形态选择\n                        if (!this.form.platform_publish_forms[platform.platform_id]) {\n                            this.$message.error(`请为平台\"${platform.platform_name}\"选择发布形态`)\n                            return false\n                        }\n                    })\n                    break\n                case 4:\n                    fieldsToValidate = ['updated_prompt', 'scene_running_frequency', 'stored_strategy_refresh_days', 'explore_strategy_trigger_days']\n                    break\n                default:\n                    break\n            }\n\n            this.validateFields(fieldsToValidate, (valid) => {\n                if (valid) {\n                    this.activeStep++\n                }\n            })\n        },\n        validateFields(fields, callback) {\n            if (fields.length === 0) {\n                callback(true)\n                return\n            }\n\n            const validationPromises = fields.map(field => {\n                return new Promise((resolve) => {\n                    this.$refs.form.validateField(field, (errorMessage) => {\n                        console.log(`字段 ${field} 验证结果:`, errorMessage)\n                        resolve({ field, errorMessage })\n                    })\n                })\n            })\n\n            Promise.all(validationPromises).then(results => {\n                const hasError = results.some(result => result.errorMessage)\n                console.log('验证结果:', results, '是否有错误:', hasError)\n                callback(!hasError)\n            })\n        },\n        prevStep() {\n            this.activeStep--\n        },\n        async submitForm() {\n            this.$refs.form.validate(async valid => {\n                if (valid) {\n                    try {\n                        const accounts = []\n                        const effectParams = []\n\n                        for (const platformId of this.form.input_platforms) {\n                            const platformData = this.form.input_platforms_data[platformId] || {}\n                            const dataOptions = this.form.input_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 1,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n\n                            if (this.form.input_effect_params_config[platformId]) {\n                                Object.values(this.form.input_effect_params_config[platformId]).forEach(paramConfig => {\n                                    effectParams.push({\n                                        platform_id: platformId,\n                                        ...paramConfig\n                                    })\n                                })\n                            }\n                        }\n\n                        for (const platformId of this.form.output_platforms) {\n                            const platformData = this.form.output_platforms_data[platformId] || {}\n                            const dataOptions = this.form.output_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 2,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n                        }\n\n                        const { input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, platform_modalities, platform_publish_forms, ...submitData } = this.form;\n                        console.info(input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, platform_modalities, platform_publish_forms)\n\n                        submitData.update_time = new Date().toLocaleString('sv-SE').replace('T', ' ');\n\n                        await this.$http.post('', {\n                            api: '/api/scene/update',\n                            data: {\n                                ...submitData,\n                                scene_id: this.$route.params.id\n                            },\n                            accounts: accounts,\n                            effect_params: effectParams,\n                            platform_modalities: platform_modalities,\n                            platform_publish_forms: platform_publish_forms\n                        })\n                        this.$message.success('场景更新成功')\n                        this.$router.push('/')\n                    } catch (error) {\n                        this.$message.error('场景更新失败')\n                        console.error('Error updating scene:', error)\n                    }\n                }\n            })\n        },\n        async fetchSceneDetail() {\n            if (!this.$route.params.id) {\n                return\n            }\n\n            this.loading = true\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/scene/getDetail',\n                    param: { scene_id: this.$route.params.id }\n                })\n                const sceneData = response.data.data\n                const accounts = response.data.accounts || []\n                const effectParams = response.data.effect_params || []\n\n                this.sceneInputAccountsData = {}\n                this.sceneOutputAccountsData = {}\n                accounts.forEach(account => {\n                    if (account.operate_type === 1) {\n                        this.sceneInputAccountsData[account.platform_id] = account\n                    } else if (account.operate_type === 2) {\n                        this.sceneOutputAccountsData[account.platform_id] = account\n                    }\n                })\n                const inputPlatforms = accounts\n                    .filter(platform => platform.operate_type === 1)\n                    .map(platform => platform.platform_id)\n\n                const outputPlatforms = accounts\n                    .filter(platform => platform.operate_type === 2)\n                    .map(platform => platform.platform_id)\n\n                this.effectParamsData = {}\n                effectParams.forEach(param => {\n                    this.effectParamsData[param.effect_param_code] = param\n                })\n\n                console.info(\"effectParamsData\", this.effectParamsData);\n\n                this.form = {\n                    ...this.form,\n                    ...sceneData,\n                    linked_task_type_code: parseInt(sceneData.linked_task_type_code),\n                    input_platforms: inputPlatforms,\n                    output_platforms: outputPlatforms\n                }\n\n                if (sceneData.scene_running_frequency) {\n                    this.parseFrequencyFromMinutes(parseInt(sceneData.scene_running_frequency))\n                } else {\n                    this.frequencyValue = 30\n                    this.frequencyUnit = 'minutes'\n                }\n                this.calculateTotalMinutes()\n\n                await this.$nextTick()\n                await this.handleInputPlatformChange(this.form.input_platforms)\n                await this.handleOutputPlatformChange(this.form.output_platforms)\n\n            } catch (error) {\n                this.$message.error('获取场景详情失败')\n                console.error('Error fetching scene detail:', error)\n            } finally {\n                this.loading = false\n            }\n        },\n        showAddOptionDialog(platformId) {\n            this.newOptionForm = {\n                option_name: '',\n                option_key: '',\n                platform_id: platformId\n            }\n            this.addOptionDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.optionForm && this.$refs.optionForm.clearValidate()\n            })\n        },\n        async addNewOption() {\n            this.$refs.optionForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingOption = true\n                        await this.$http.post('', {\n                            api: '/api/option/add',\n                            data: {\n                                ...this.newOptionForm,\n                                platform_id: this.newOptionForm.platform_id\n                            }\n                        })\n                        this.$message.success('新增数据类型成功')\n                        this.addOptionDialogVisible = false\n                        this.newOptionForm = {\n                            option_name: '',\n                            option_key: '',\n                            platform_id: ''\n                        }\n                        await this.handleInputPlatformChange(this.form.input_platforms)\n                        await this.handleOutputPlatformChange(this.form.output_platforms)\n                    } catch (error) {\n                        this.$message.error('新增数据类型失败')\n                        console.error('Error adding new option:', error)\n                    } finally {\n                        this.addingOption = false\n                    }\n                }\n            })\n        },\n        openAIOptimize() {\n            if (!this.form.scene_id) return\n            const url = `https://acpfbbeg.manus.space/?scene_id=${this.form.scene_id}`\n            window.open(url, '_blank')\n        },\n        async handleDeleteScene() {\n            if (!this.form.scene_id) return\n            try {\n                await this.$confirm('确认删除该场景吗？', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                })\n                await this.$http.post('', {\n                    api: '/api/scene/delete',\n                    param: { scene_id: this.form.scene_id }\n                })\n                this.$message.success('删除成功')\n                this.$router.push('/scene/manage')\n            } catch (error) {\n                if (error !== 'cancel') {\n                    this.$message.error('删除失败')\n                    console.error('Error deleting scene:', error)\n                }\n            }\n        },\n        async fetchModalityOptions() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'modality'\n                    }\n                })\n                this.modalityOptions = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching modality options:', error)\n                this.$message.error('获取模态列表失败')\n            }\n        },\n        // 获取多模态类型列表\n        async fetchModalityTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'modality_types'\n                    }\n                })\n                this.modalityTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching modality types:', error)\n                this.$message.error('获取多模态类型列表失败')\n            }\n        },\n        // 获取发布形态类型列表\n        async fetchPublishFormTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'publish_form_types'\n                    }\n                })\n                this.publishFormTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching publish form types:', error)\n                this.$message.error('获取发布形态类型列表失败')\n            }\n        },\n        async fetchProjectTeams() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/projectTeam/getList'\n                })\n                this.projectTeams = response.data.data || []\n                console.log('项目组数据:', this.projectTeams)\n            } catch (error) {\n                console.error('Error fetching project teams:', error)\n                this.$message.error('获取项目组列表失败')\n            }\n        },\n        async fetchTaskTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/taskType/getList'\n                })\n                this.taskTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching task types:', error)\n                this.$message.error('获取任务类型列表失败')\n            }\n        },\n        async fetchSceneTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/sceneType/getList'\n                })\n                this.sceneTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching scene types:', error)\n                this.$message.error('获取场景类型列表失败')\n            }\n        },\n        handleProjectTeamChange() {\n            console.log('项目组改变:', this.form.linked_project_team_name)\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_project_team_name')\n            })\n        },\n        handleTaskTypeChange() {\n            this.form.input_platforms = []\n            this.form.output_platforms = []\n            this.selectedInputPlatforms = []\n            this.selectedOutputPlatforms = []\n            this.form.input_platforms_data = {}\n            this.form.output_platforms_data = {}\n            this.form.input_data_options = {}\n            this.form.output_data_options = {}\n            this.form.input_effect_params = {}\n            this.form.input_effect_params_config = {}\n            this.form.platform_modalities = {}\n            this.form.platform_publish_forms = {}\n            this.tempModalitySelection = {}\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_task_type_code')\n            })\n        },\n        handleEffectParamsChange(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n\n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n\n            if (platformParams.length == 0) {\n                return;\n            }\n\n            Object.keys(this.rules).forEach(key => {\n                if (key.startsWith(`input_effect_params_config.${platformId}.`)) {\n                    this.$delete(this.rules, key)\n                }\n            })\n\n            selectedParams.forEach(param => {\n                if (!this.form.input_effect_params_config[platformId][param]) {\n\n                    const effectParam = platformParams.find(p => p.effect_param_code === param);\n\n                    this.$set(this.form.input_effect_params_config[platformId], param, {\n                        effect_param_code: effectParam.effect_param_code,\n                        effect_param_name: effectParam.effect_param_name,\n                        configured_evaluation_days: '',\n                        default_baseline_mean: '',\n                        default_baseline_stddev: ''\n                    })\n                }\n\n                const configPrefix = `input_effect_params_config.${platformId}.${param}`\n                this.$set(this.rules, `${configPrefix}.configured_evaluation_days`, [\n                    { required: true, message: '请输入效果实现天数', trigger: 'blur' },\n                    { pattern: /^[\\d,\\s]+$/, message: '请输入有效的天数格式，如：3,5,10', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_mean`, [\n                    { required: true, message: '请输入平均值', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '平均值必须是数字', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_stddev`, [\n                    { required: true, message: '请输入标准差', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '标准差必须是数字', trigger: 'blur' }\n                ])\n            })\n            \n            Object.keys(this.form.input_effect_params_config[platformId]).forEach(param => {\n                if (!selectedParams.includes(param)) {\n                    this.$delete(this.form.input_effect_params_config[platformId], param)\n                }\n            })\n        },\n        getEffectParamsTableData(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n            const config = this.form.input_effect_params_config[platformId] || {}\n            \n            const ret = selectedParams.map(paramCode => ({\n                effect_param_code: paramCode,\n                effect_param_name: (config[paramCode] && config[paramCode].effect_param_name) || '',\n                configured_evaluation_days: (config[paramCode] && config[paramCode].configured_evaluation_days) || '',\n                default_baseline_mean: (config[paramCode] && config[paramCode].default_baseline_mean) || 0,\n                default_baseline_stddev: (config[paramCode] && config[paramCode].default_baseline_stddev) || 1\n            }))\n\n            console.info(\"ret\", ret);\n            return ret;\n        },\n        updateEffectParamConfig(platformId, paramName, field, value) {\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n            if (!this.form.input_effect_params_config[platformId][paramName]) {\n                this.$set(this.form.input_effect_params_config[platformId], paramName, {})\n            }\n            this.$set(this.form.input_effect_params_config[platformId][paramName], field, value)\n        },\n        getMinValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30 \n                case 'hours':\n                    return 1 \n                case 'days':\n                    return 1 \n                default:\n                    return 1\n            }\n        },\n        getMaxValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 1440\n                case 'hours':\n                    return 24 \n                case 'days':\n                    return 365 \n                default:\n                    return 1\n            }\n        },\n        getStep() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30 \n                case 'hours':\n                    return 1 \n                case 'days':\n                    return 1 \n                default:\n                    return 1\n            }\n        },\n        calculateTotalMinutes() {\n            let totalMinutes = 0\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    totalMinutes = this.frequencyValue\n                    break\n                case 'hours':\n                    totalMinutes = this.frequencyValue * 60\n                    break\n                case 'days':\n                    totalMinutes = this.frequencyValue * 24 * 60\n                    break\n            }\n            \n            if (totalMinutes < 30) {\n                totalMinutes = 30\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n            }\n            \n            this.form.scene_running_frequency = totalMinutes\n            return totalMinutes\n        },\n        parseFrequencyFromMinutes(minutes) {\n            if (!minutes || minutes < 30) {\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n                return\n            }\n            \n            if (minutes >= 1440 && minutes % 1440 === 0) {\n                this.frequencyValue = minutes / 1440\n                this.frequencyUnit = 'days'\n            } else if (minutes >= 60 && minutes % 60 === 0) {\n                this.frequencyValue = minutes / 60\n                this.frequencyUnit = 'hours'\n            } else {\n                this.frequencyValue = minutes\n                this.frequencyUnit = 'minutes'\n            }\n        },\n        handleFrequencyValueChange() {\n            this.calculateTotalMinutes()\n        },\n        handleFrequencyUnitChange() {\n            const currentMinutes = this.calculateTotalMinutes()\n            this.parseFrequencyFromMinutes(currentMinutes)\n            this.calculateTotalMinutes()\n        },\n        showAddProjectDialog() {\n            this.newProjectForm = {\n                project_name: ''\n            }\n            this.addProjectDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.projectForm && this.$refs.projectForm.clearValidate()\n            })\n        },\n        async addNewProject() {\n            this.$refs.projectForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingProject = true\n                        await this.$http.post('', {\n                            api: '/api/projectTeam/add',\n                            data: this.newProjectForm\n                        })\n                        this.$message.success('新增项目组成功')\n                        this.addProjectDialogVisible = false\n                        await this.fetchProjectTeams()\n                        if (this.projectTeams.length > 0) {\n                            const newProject = this.projectTeams.find(project => project.project_name === this.newProjectForm.project_name)\n                            if (newProject) {\n                                this.form.linked_project_team_name = newProject.project_name\n                            }\n                        }\n                    } catch (error) {\n                        this.$message.error('新增项目组失败')\n                        console.error('Error adding new project:', error)\n                    } finally {\n                        this.addingProject = false\n                    }\n                }\n            })\n        },\n        showAddTaskTypeDialog() {\n            this.newTaskTypeForm = {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            }\n            this.selectedPlatformIds = [] \n            this.selectedEffectParams = [] \n            this.availableEffectParamsForTaskType = []\n            this.addTaskTypeDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.taskTypeForm && this.$refs.taskTypeForm.clearValidate()\n            })\n        },\n        async handlePlatformSelectChange(selectedIds) {\n            this.newTaskTypeForm.linked_platform_ids = selectedIds.join(',')\n            \n            await this.fetchEffectParamsForTaskType(selectedIds)\n        },\n        async fetchEffectParamsForTaskType(platformIds) {\n            if (platformIds.length === 0) {\n                this.availableEffectParamsForTaskType = []\n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                return\n            }\n            \n            try {\n                const platformParamsArrays = []\n                for (const platformId of platformIds) {\n                    const params = await this.fetchPlatformEffectParams(platformId)\n                    platformParamsArrays.push(params)\n                }\n                \n                const allParams = []\n                const seenCodes = new Set()\n                \n                platformParamsArrays.forEach(platformParams => {\n                    platformParams.forEach(param => {\n                        if (!seenCodes.has(param.effect_param_code)) {\n                            seenCodes.add(param.effect_param_code)\n                            allParams.push(param)\n                        }\n                    })\n                })\n                \n                this.availableEffectParamsForTaskType = allParams\n                \n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                \n            } catch (error) {\n                console.error('Error fetching effect params for task type:', error)\n                this.$message.error('获取效果参数失败')\n                this.availableEffectParamsForTaskType = []\n            }\n        },\n        handleEffectParamsSelectChange(selectedCodes) {\n            this.newTaskTypeForm.recommended_effect_param_codes = JSON.stringify(selectedCodes)\n        },\n        async addNewTaskType() {\n            this.$refs.taskTypeForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingTaskType = true\n                        await this.$http.post('', {\n                            api: '/api/taskType/add',\n                            data: this.newTaskTypeForm\n                        })\n                        this.$message.success('新增任务类型成功')\n                        this.addTaskTypeDialogVisible = false\n                        await this.fetchTaskTypes()\n                    } catch (error) {\n                        this.$message.error('新增任务类型失败')\n                        console.error('Error adding new task type:', error)\n                    } finally {\n                        this.addingTaskType = false\n                    }\n                }\n            })\n        }\n    },\n    async created() {\n        await this.fetchPlatforms({ page: 1, pageSize: 100 })\n        await this.fetchProjectTeams()\n        await this.fetchTaskTypes()\n        await this.fetchSceneTypes()\n        await this.fetchSceneDetail()\n        await this.fetchModalityOptions()\n        await this.fetchModalityTypes()\n        await this.fetchPublishFormTypes()\n    }\n}\n</script>\n\n<style scoped>\n.edit-scene {\n    padding: 20px;\n}\n\n.mt-20 {\n    margin-top: 20px;\n}\n\n.el-steps {\n    margin-bottom: 30px;\n}\n\n.platform-configs {\n    margin-top: 20px;\n    margin-bottom: 20px;\n}\n\n.platform-configs h3 {\n    margin-bottom: 16px;\n    color: #303133;\n    font-size: 16px;\n}\n\n.platform-card {\n    margin-bottom: 16px;\n}\n\n.platform-card:last-child {\n    margin-bottom: 0;\n}\n\n.data-options-container {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n}\n\n.checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.platform-selection-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.edit-breadcrumb {\n    font-size: 14px;\n    margin-bottom: 18px;\n}\n\n.edit-actions {\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n    margin-bottom: 18px;\n}\n\n.navigation-buttons {\n    margin-top: 30px;\n}\n\n.platform-selection-tip {\n    margin-bottom: 16px;\n}\n\n.no-platforms-tip {\n    margin-top: 16px;\n}\n\n.loading-tip {\n    margin-top: 16px;\n}\n\n.effect-params-container {\n    margin-top: 16px;\n}\n\n.effect-param-checkbox {\n    margin-right: 16px;\n    margin-bottom: 8px;\n}\n\n.effect-params-table {\n    margin-top: 16px;\n}\n\n.effect-params-table h4 {\n    margin-bottom: 12px;\n    color: #303133;\n    font-size: 14px;\n}\n\n\n\n.table-header-with-tooltip {\n    cursor: help;\n    display: flex;\n    align-items: center;\n    gap: 4px;\n}\n\n.table-header-with-tooltip .el-icon-question {\n    color: #609399;\n    font-size: 14px;\n}\n\n/* 新增样式：多模态和发布形态选择 */\n.modality-selection-container,\n.publish-form-selection-container {\n    width: 100%;\n}\n\n.selection-row {\n    display: flex;\n    align-items: flex-start;\n    gap: 15px;\n    flex-wrap: wrap;\n}\n\n.selected-tags {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 8px;\n    min-height: 32px;\n    align-items: center;\n}\n</style>"], "mappings": "AA6pBA,SAAAA,QAAA,EAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,UAAA;MACAC,OAAA;MACAC,IAAA;QACAC,QAAA;QACAC,wBAAA;QACAC,qBAAA;QACAC,UAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,0BAAA;QACAC,cAAA;QACAC,uBAAA;QACAC,IAAA;QACAC,QAAA;QACAC,mBAAA;QAAA;QACAC,sBAAA;QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,4BAAA;QACAC,6BAAA;QACAC,mBAAA;QACAC,4BAAA;QACAC,iCAAA;QACAC,yBAAA;QACAC,+BAAA;MACA;MACAC,cAAA;MACAC,aAAA;MACAC,KAAA;QACA7B,wBAAA,GACA;UACA8B,QAAA;UACAC,OAAA;UACAC,OAAA;UACAC,SAAA,EAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACAC,OAAA,CAAAC,GAAA,WAAAH,KAAA;YACA,KAAAA,KAAA;cACAC,QAAA,KAAAG,KAAA;YACA;cACAH,QAAA;YACA;UACA;QACA,EACA;QACAnC,qBAAA,GACA;UACA6B,QAAA;UACAC,OAAA;UACAC,OAAA;UACAC,SAAA,EAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACAC,OAAA,CAAAC,GAAA,YAAAH,KAAA;YACA,KAAAA,KAAA;cACAC,QAAA,KAAAG,KAAA;YACA;cACAH,QAAA;YACA;UACA;QACA,EACA;QACAlC,UAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA7B,iBAAA,GACA;UAAA2B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA5B,eAAA,GACA;UAAA0B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA3B,gBAAA,GACA;UAAAyB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACApB,cAAA,GACA;UAAAkB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,uBAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAQ,IAAA;UACAC,GAAA;UACAV,OAAA;UACAC,OAAA;QACA,EACA;QACAZ,4BAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAX,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,6BAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAX,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,mBAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,4BAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAX,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,iCAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAX,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,yBAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAX,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,+BAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAX,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAW,YAAA;MACAC,SAAA;MACAC,eAAA;MACAC,sBAAA;MACAC,uBAAA;MACAC,sBAAA;MACAC,uBAAA;MACAC,gBAAA;MACAC,sBAAA;MACAC,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,WAAA;MACA;MACAC,WAAA;QACAH,WAAA,GACA;UAAAvB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAsB,UAAA,GACA;UAAAxB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAyB,YAAA;MACAC,uBAAA;MACAC,wBAAA;MACAC,cAAA;QACAC,YAAA;MACA;MACAC,eAAA;QACAC,cAAA;QACAC,qBAAA;QACAC,8BAAA;QACAC,+BAAA;QACAC,wBAAA;QACAC,sCAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,eAAA;MACA;MACAC,mBAAA;MACAC,oBAAA;MACAC,gCAAA;MACAC,YAAA;QACAd,YAAA,GACA;UAAA/B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA4C,aAAA;QACAb,cAAA,GACA;UAAAjC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAgC,qBAAA,GACA;UAAAlC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAiC,8BAAA,GACA;UAAAnC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAkC,+BAAA,GACA;UAAApC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA6C,aAAA;MACAC,cAAA;MACAC,UAAA;MACAC,qBAAA;MACAC,sBAAA;MACAC,qBAAA;MAAA;MACAC,aAAA;MAAA;MACAC,gBAAA;IACA;EACA;EACAC,QAAA;IACA,GAAA7F,QAAA;IACA8F,mBAAA;MACA,UAAAC,SAAA,UAAAzF,IAAA,CAAAG,qBAAA;QACA,YAAAsF,SAAA;MACA;MAEA,MAAAC,gBAAA,QAAA5C,SAAA,CAAA6C,IAAA,CAAAC,QAAA,IAAAA,QAAA,CAAAC,cAAA,UAAA7F,IAAA,CAAAG,qBAAA;MACA,KAAAuF,gBAAA,KAAAA,gBAAA,CAAAnB,mBAAA;QACA,YAAAkB,SAAA;MACA;MAEA,MAAAK,iBAAA,GAAAJ,gBAAA,CAAAnB,mBAAA,CAAAwB,KAAA,MAAAC,GAAA,CAAAC,EAAA,IAAAC,QAAA,CAAAD,EAAA,CAAAE,IAAA;MACA,YAAAV,SAAA,CAAAW,MAAA,CAAAC,QAAA,IAAAP,iBAAA,CAAAQ,QAAA,CAAAD,QAAA,CAAA5C,WAAA;IACA;IACA8C,sBAAA;MACA,UAAAvG,IAAA,CAAAG,qBAAA,UAAA2C,SAAA;QACA;MACA;MAEA,MAAA4C,gBAAA,QAAA5C,SAAA,CAAA6C,IAAA,CAAAC,QAAA,IAAAA,QAAA,CAAAC,cAAA,UAAA7F,IAAA,CAAAG,qBAAA;MACA,KAAAuF,gBAAA,KAAAA,gBAAA,CAAAvB,8BAAA;QACA;MACA;MAEA;QACA,MAAAqC,MAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAhB,gBAAA,CAAAvB,8BAAA;QACA,OAAAwC,KAAA,CAAAC,OAAA,CAAAJ,MAAA,IAAAA,MAAA;MACA,SAAAK,KAAA;QACAtE,OAAA,CAAAsE,KAAA,cAAAA,KAAA;QACA;MACA;IACA;IACAC,wBAAA;MACA,SAAAhH,UAAA,eAAAoF,qBAAA;QACA;MACA;MACA,SAAApF,UAAA,eAAAqF,sBAAA;QACA;MACA;MACA;IACA;EACA;EACA4B,OAAA;IACA,GAAApH,UAAA;IACA;IACAqH,yBAAAX,QAAA;MACA;MACA,IAAAA,QAAA,CAAAY,aAAA,IAAAZ,QAAA,CAAAY,aAAA,CAAAC,WAAA,GAAAZ,QAAA;QACA,YAAAhB,gBAAA;MACA;MACA;MACA;MACA,YAAAA,gBAAA;IACA;IACA;IACA6B,qBAAAC,UAAA,EAAAC,gBAAA;MACA,KAAAA,gBAAA;MAEA,UAAArH,IAAA,CAAAkB,mBAAA,CAAAkG,UAAA;QACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAkB,mBAAA,EAAAkG,UAAA;MACA;MAEA,UAAApH,IAAA,CAAAkB,mBAAA,CAAAkG,UAAA,EAAAd,QAAA,CAAAe,gBAAA;QACA,KAAArH,IAAA,CAAAkB,mBAAA,CAAAkG,UAAA,EAAAG,IAAA,CAAAF,gBAAA;MACA;;MAEA;MACA,KAAAC,IAAA,MAAAlC,qBAAA,EAAAgC,UAAA;IACA;IACA;IACAI,eAAAJ,UAAA,EAAAnG,QAAA;MACA,SAAAjB,IAAA,CAAAkB,mBAAA,CAAAkG,UAAA;QACA,MAAAK,KAAA,QAAAzH,IAAA,CAAAkB,mBAAA,CAAAkG,UAAA,EAAAM,OAAA,CAAAzG,QAAA;QACA,IAAAwG,KAAA;UACA,KAAAzH,IAAA,CAAAkB,mBAAA,CAAAkG,UAAA,EAAAO,MAAA,CAAAF,KAAA;QACA;MACA;IACA;IACA;IACAG,kBAAAR,UAAA;MACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAmB,sBAAA,EAAAiG,UAAA;IACA;IACA,MAAAS,qBAAAT,UAAA;MACA;QACA,MAAAU,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACAzE,WAAA,EAAA2D;UACA;QACA;QACA,OAAAU,QAAA,CAAAjI,IAAA,CAAAA,IAAA;MACA,SAAAgH,KAAA;QACAtE,OAAA,CAAAsE,KAAA,qCAAAA,KAAA;QACA;MACA;IACA;IACA,MAAAsB,0BAAAf,UAAA;MACA;QACA,MAAAU,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACAzE,WAAA,EAAA2D;UACA;QACA;QACA,OAAAU,QAAA,CAAAjI,IAAA,CAAAA,IAAA;MACA,SAAAgH,KAAA;QACAtE,OAAA,CAAAsE,KAAA,2CAAAA,KAAA;QACA;MACA;IACA;IACAuB,oCAAAhB,UAAA;MACA,MAAAiB,iBAAA,QAAA9B,qBAAA;MAEA,MAAAF,QAAA,QAAArD,sBAAA,CAAA2C,IAAA,CAAA2C,CAAA,IAAAA,CAAA,CAAA7E,WAAA,KAAA2D,UAAA;MACA,MAAAmB,cAAA,GAAAlC,QAAA,GAAAA,QAAA,CAAAmC,YAAA;MAEAjG,OAAA,CAAAkG,IAAA,CAAAF,cAAA;MAEA,IAAAA,cAAA,CAAAG,MAAA;QACA;MACA;;MAEA;;MAEA,OAAAH,cAAA,CAAAnC,MAAA,CAAA8B,KAAA,IAAAG,iBAAA,CAAA/B,QAAA,CAAA4B,KAAA,CAAAS,iBAAA;IACA;IACAC,kBAAAlG,IAAA;MACA,MAAAmG,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,YAAA,CAAAnG,IAAA;IACA;IACAoG,cAAAC,KAAA;MACA,MAAAC,KAAA;QACAC,WAAA,QAAAF,KAAA,CAAAG,KAAA;MACA;MACA,IAAAH,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAtG,IAAA;MACA;MACA,IAAAqG,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAtG,IAAA;QACAsG,KAAA,CAAAI,IAAA;MACA;MACA,IAAAL,KAAA,CAAAI,UAAA,iBAAAJ,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAK,QAAA,GAAAN,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAM,OAAA,GAAAP,KAAA,CAAAO,OAAA;MACA;MACA,OAAAN,KAAA;IACA;IACA,MAAAO,0BAAAC,WAAA;MACA,SAAAtE,qBAAA;QACA;MACA;MACA,KAAAA,qBAAA;MAEA;QACA,KAAAlC,sBAAA;QAEAyG,MAAA,CAAAC,IAAA,MAAA3H,KAAA,EAAA4H,OAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,UAAA,8BAAAD,GAAA,CAAAE,QAAA;YACA,KAAAC,OAAA,MAAAhI,KAAA,EAAA6H,GAAA;UACA;QACA;QAEA,MAAAI,oBAAA;QACA,WAAA5C,UAAA,IAAAoC,WAAA;UACA,MAAAnD,QAAA,QAAAZ,SAAA,CAAAE,IAAA,CAAA2C,CAAA,IAAAA,CAAA,CAAA7E,WAAA,KAAA2D,UAAA;UACA,KAAAf,QAAA;UAEA;YACA,MAAA4D,cAAA,cAAAlC,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAC,KAAA;gBAAAzE,WAAA,EAAA2D;cAAA;YACA;YAEA,MAAAkC,OAAA,cAAAzB,oBAAA,CAAAT,UAAA;YAEA,MAAAoB,YAAA,cAAAL,yBAAA,CAAAf,UAAA;YAEA7E,OAAA,CAAAkG,IAAA,iBAAAD,YAAA;YAEAwB,oBAAA,CAAAzC,IAAA;cACA,GAAA0C,cAAA,CAAApK,IAAA,CAAAA,IAAA;cACAyJ,OAAA,EAAAA,OAAA;cACAd,YAAA,EAAAA;YACA;UACA,SAAA3B,KAAA;YACAtE,OAAA,CAAAsE,KAAA,oCAAAA,KAAA;YACA,KAAAqD,QAAA,CAAArD,KAAA,QAAAR,QAAA,CAAAY,aAAA;UACA;QACA;QAEA,KAAAjE,sBAAA,GAAAgH,oBAAA;QAEA,WAAA5C,UAAA,IAAAoC,WAAA;UAEA,UAAAxJ,IAAA,CAAAQ,oBAAA,CAAA4G,UAAA;YACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAQ,oBAAA,EAAA4G,UAAA;cACA+C,sBAAA;YACA;UACA;UAEA,UAAAnK,IAAA,CAAAU,kBAAA,CAAA0G,UAAA;YACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAU,kBAAA,EAAA0G,UAAA;UACA;UAEA,UAAApH,IAAA,CAAAY,mBAAA,CAAAwG,UAAA;YACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAY,mBAAA,EAAAwG,UAAA;UACA;UAEA,MAAAgD,mBAAA,QAAApH,sBAAA,CAAA2C,IAAA,CAAA2C,CAAA,IAAAA,CAAA,CAAA7E,WAAA,KAAA2D,UAAA;UACA,IAAAgD,mBAAA,IAAAA,mBAAA,CAAAC,MAAA;YACAD,mBAAA,CAAAC,MAAA,CAAAV,OAAA,CAAAZ,KAAA;cACA,IAAAA,KAAA,CAAA/G,QAAA;gBACA,MAAAsI,SAAA,2BAAAlD,UAAA,IAAA2B,KAAA,CAAAwB,UAAA;gBACA,KAAAjD,IAAA,MAAAvF,KAAA,EAAAuI,SAAA,GACA;kBAAAtI,QAAA;kBAAAC,OAAA,QAAA8G,KAAA,CAAAG,KAAA;kBAAAhH,OAAA;gBAAA,EACA;cACA;YACA;UACA;UAEA,SAAAgB,sBAAA,CAAAkE,UAAA;YACA,MAAAoD,WAAA,QAAAtH,sBAAA,CAAAkE,UAAA;YACA,MAAAgD,mBAAA,QAAApH,sBAAA,CAAA2C,IAAA,CAAA2C,CAAA,IAAAA,CAAA,CAAA7E,WAAA,KAAA2D,UAAA;YAEA,WAAA2B,KAAA,IAAAqB,mBAAA,IAAAA,mBAAA,CAAAC,MAAA;cACA,IAAAG,WAAA,CAAAzB,KAAA,CAAAwB,UAAA,MAAAE,SAAA;gBACA,KAAAnD,IAAA,MAAAtH,IAAA,CAAAQ,oBAAA,CAAA4G,UAAA,GAAA2B,KAAA,CAAAwB,UAAA,EAAAC,WAAA,CAAAzB,KAAA,CAAAwB,UAAA;cACA;YACA;YAEA,IAAAC,WAAA,CAAAL,sBAAA,KAAAM,SAAA;cACA,KAAAnD,IAAA,MAAAtH,IAAA,CAAAQ,oBAAA,CAAA4G,UAAA,6BAAAoD,WAAA,CAAAL,sBAAA;YACA;YAEA,IAAAK,WAAA,CAAAE,iBAAA,IAAAF,WAAA,CAAAE,iBAAA;cACA,MAAAC,eAAA,GAAAH,WAAA,CAAAE,iBAAA,CAAA3E,KAAA,MAAAK,MAAA,CAAAwE,IAAA,IAAAA,IAAA,CAAAzE,IAAA;cACA,KAAAmB,IAAA,MAAAtH,IAAA,CAAAU,kBAAA,EAAA0G,UAAA,EAAAuD,eAAA;YACA;UACA;UAEA,SAAAvH,gBAAA;YACA,MAAAyH,oBAAA,GAAApB,MAAA,CAAAqB,MAAA,MAAA1H,gBAAA,EAAAgD,MAAA,CAAA8B,KAAA,IAAAA,KAAA,CAAAzE,WAAA,KAAA2D,UAAA;YAEA,IAAAyD,oBAAA,CAAAnC,MAAA;cACA,UAAA1I,IAAA,CAAAY,mBAAA,CAAAwG,UAAA;gBACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAY,mBAAA,EAAAwG,UAAA;cACA;cAEA,UAAApH,IAAA,CAAAa,0BAAA,CAAAuG,UAAA;gBACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAa,0BAAA,EAAAuG,UAAA;cACA;cAEA,MAAA2D,cAAA;cACAF,oBAAA,CAAAlB,OAAA,CAAAzB,KAAA;gBACA6C,cAAA,CAAAxD,IAAA,CAAAW,KAAA,CAAA8C,iBAAA;gBACA,KAAA1D,IAAA,MAAAtH,IAAA,CAAAa,0BAAA,CAAAuG,UAAA,GAAAc,KAAA,CAAA8C,iBAAA;kBACAA,iBAAA,EAAA9C,KAAA,CAAA8C,iBAAA;kBACArC,iBAAA,EAAAT,KAAA,CAAAS,iBAAA;kBACAsC,0BAAA,EAAA/C,KAAA,CAAA+C,0BAAA;kBACAC,qBAAA,EAAAhD,KAAA,CAAAgD,qBAAA;kBACAC,uBAAA,EAAAjD,KAAA,CAAAiD,uBAAA;gBACA;cACA;cAEA,KAAA7D,IAAA,MAAAtH,IAAA,CAAAY,mBAAA,EAAAwG,UAAA,EAAA2D,cAAA;YACA;UACA;QACA;MACA,SAAAlE,KAAA;QACAtE,OAAA,CAAAsE,KAAA,wCAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;QACA,KAAA3B,qBAAA;MACA;IACA;IACA,MAAAkG,2BAAA5B,WAAA;MACA,SAAArE,sBAAA;QACA;MACA;MACA,KAAAA,sBAAA;MAEA;QACA,KAAAlC,uBAAA;QAEAwG,MAAA,CAAAC,IAAA,MAAA3H,KAAA,EAAA4H,OAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,UAAA,+BAAAD,GAAA,CAAAE,QAAA;YACA,KAAAC,OAAA,MAAAhI,KAAA,EAAA6H,GAAA;UACA;QACA;QAEA,MAAAI,oBAAA;QACA,WAAA5C,UAAA,IAAAoC,WAAA;UACA,MAAAnD,QAAA,QAAAZ,SAAA,CAAAE,IAAA,CAAA2C,CAAA,IAAAA,CAAA,CAAA7E,WAAA,KAAA2D,UAAA;UACA,KAAAf,QAAA;UAEA;YACA,MAAA4D,cAAA,cAAAlC,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAC,KAAA;gBAAAzE,WAAA,EAAA2D;cAAA;YACA;YAEA,MAAAkC,OAAA,cAAAzB,oBAAA,CAAAT,UAAA;YAEA4C,oBAAA,CAAAzC,IAAA;cACA,GAAA0C,cAAA,CAAApK,IAAA,CAAAA,IAAA;cACAyJ,OAAA,EAAAA;YACA;UACA,SAAAzC,KAAA;YACAtE,OAAA,CAAAsE,KAAA,oCAAAA,KAAA;YACA,KAAAqD,QAAA,CAAArD,KAAA,QAAAR,QAAA,CAAAY,aAAA;UACA;QACA;QAEA,KAAAhE,uBAAA,GAAA+G,oBAAA;QAEA,WAAA5C,UAAA,IAAAoC,WAAA;UAEA,UAAAxJ,IAAA,CAAAS,qBAAA,CAAA2G,UAAA;YACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAS,qBAAA,EAAA2G,UAAA;cACA+C,sBAAA;YACA;UACA;UAEA,UAAAnK,IAAA,CAAAW,mBAAA,CAAAyG,UAAA;YACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAW,mBAAA,EAAAyG,UAAA;UACA;;UAEA;UACA,UAAApH,IAAA,CAAAkB,mBAAA,CAAAkG,UAAA;YACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAkB,mBAAA,EAAAkG,UAAA;UACA;UAEA,UAAApH,IAAA,CAAAmB,sBAAA,CAAAiG,UAAA;YACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAmB,sBAAA,EAAAiG,UAAA;UACA;UAEA,MAAAgD,mBAAA,QAAAnH,uBAAA,CAAA0C,IAAA,CAAA2C,CAAA,IAAAA,CAAA,CAAA7E,WAAA,KAAA2D,UAAA;UACA,IAAAgD,mBAAA,IAAAA,mBAAA,CAAAC,MAAA;YACAD,mBAAA,CAAAC,MAAA,CAAAV,OAAA,CAAAZ,KAAA;cACA,IAAAA,KAAA,CAAA/G,QAAA;gBACA,MAAAsI,SAAA,4BAAAlD,UAAA,IAAA2B,KAAA,CAAAwB,UAAA;gBACA,KAAAjD,IAAA,MAAAvF,KAAA,EAAAuI,SAAA,GACA;kBAAAtI,QAAA;kBAAAC,OAAA,QAAA8G,KAAA,CAAAG,KAAA;kBAAAhH,OAAA;gBAAA,EACA;cACA;YACA;UACA;UAEA,SAAAiB,uBAAA,CAAAiE,UAAA;YACA,MAAAoD,WAAA,QAAArH,uBAAA,CAAAiE,UAAA;YACA,MAAAgD,mBAAA,QAAAnH,uBAAA,CAAA0C,IAAA,CAAA2C,CAAA,IAAAA,CAAA,CAAA7E,WAAA,KAAA2D,UAAA;YAEA,WAAA2B,KAAA,IAAAqB,mBAAA,IAAAA,mBAAA,CAAAC,MAAA;cACA,IAAAG,WAAA,CAAAzB,KAAA,CAAAwB,UAAA,MAAAE,SAAA;gBACA,KAAAnD,IAAA,MAAAtH,IAAA,CAAAS,qBAAA,CAAA2G,UAAA,GAAA2B,KAAA,CAAAwB,UAAA,EAAAC,WAAA,CAAAzB,KAAA,CAAAwB,UAAA;cACA;YACA;YAEA,IAAAC,WAAA,CAAAL,sBAAA,KAAAM,SAAA;cACA,KAAAnD,IAAA,MAAAtH,IAAA,CAAAS,qBAAA,CAAA2G,UAAA,6BAAAoD,WAAA,CAAAL,sBAAA;YACA;YAEA,IAAAK,WAAA,CAAAE,iBAAA,IAAAF,WAAA,CAAAE,iBAAA;cACA,MAAAC,eAAA,GAAAH,WAAA,CAAAE,iBAAA,CAAA3E,KAAA,MAAAK,MAAA,CAAAwE,IAAA,IAAAA,IAAA,CAAAzE,IAAA;cACA,KAAAmB,IAAA,MAAAtH,IAAA,CAAAW,mBAAA,EAAAyG,UAAA,EAAAuD,eAAA;YACA;UACA;QACA;MACA,SAAA9D,KAAA;QACAtE,OAAA,CAAAsE,KAAA,yCAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;QACA,KAAA1B,sBAAA;MACA;IACA;IACAkG,SAAA;MACA,SAAAvE,uBAAA;QACA,KAAAoD,QAAA,CAAAoB,OAAA;QACA;MACA;MAEA,IAAAC,gBAAA;MAEA,aAAAzL,UAAA;QACA;UACAyL,gBAAA;UACA;QACA;UACAA,gBAAA;UACA;QACA;UACAA,gBAAA;UAEA,KAAAvI,sBAAA,CAAA2G,OAAA,CAAAtD,QAAA;YACA,IAAAA,QAAA,CAAAgE,MAAA;cACAhE,QAAA,CAAAgE,MAAA,CAAAV,OAAA,CAAAZ,KAAA;gBACA,IAAAA,KAAA,CAAA/G,QAAA;kBACA,MAAAsI,SAAA,2BAAAjE,QAAA,CAAA5C,WAAA,IAAAsF,KAAA,CAAAwB,UAAA;kBACAgB,gBAAA,CAAAhE,IAAA,CAAA+C,SAAA;gBACA;cACA;YACA;YAEA,MAAAS,cAAA,QAAA/K,IAAA,CAAAY,mBAAA,CAAAyF,QAAA,CAAA5C,WAAA;YACAsH,cAAA,CAAApB,OAAA,CAAA6B,SAAA;cACA,MAAAC,YAAA,iCAAApF,QAAA,CAAA5C,WAAA,IAAA+H,SAAA;cACAD,gBAAA,CAAAhE,IAAA,IAAAkE,YAAA;cACAF,gBAAA,CAAAhE,IAAA,IAAAkE,YAAA;cACAF,gBAAA,CAAAhE,IAAA,IAAAkE,YAAA;YACA;UACA;UACA;QACA;UACAF,gBAAA;UAEA,KAAAtI,uBAAA,CAAA0G,OAAA,CAAAtD,QAAA;YACA,IAAAA,QAAA,CAAAgE,MAAA;cACAhE,QAAA,CAAAgE,MAAA,CAAAV,OAAA,CAAAZ,KAAA;gBACA,IAAAA,KAAA,CAAA/G,QAAA;kBACA,MAAAsI,SAAA,4BAAAjE,QAAA,CAAA5C,WAAA,IAAAsF,KAAA,CAAAwB,UAAA;kBACAgB,gBAAA,CAAAhE,IAAA,CAAA+C,SAAA;gBACA;cACA;YACA;;YAEA;YACA,UAAAtK,IAAA,CAAAkB,mBAAA,CAAAmF,QAAA,CAAA5C,WAAA,KACA,KAAAzD,IAAA,CAAAkB,mBAAA,CAAAmF,QAAA,CAAA5C,WAAA,EAAAiF,MAAA;cACA,KAAAwB,QAAA,CAAArD,KAAA,SAAAR,QAAA,CAAAY,aAAA;cACA;YACA;;YAEA;YACA,UAAAjH,IAAA,CAAAmB,sBAAA,CAAAkF,QAAA,CAAA5C,WAAA;cACA,KAAAyG,QAAA,CAAArD,KAAA,SAAAR,QAAA,CAAAY,aAAA;cACA;YACA;UACA;UACA;QACA;UACAsE,gBAAA;UACA;QACA;UACA;MACA;MAEA,KAAAG,cAAA,CAAAH,gBAAA,EAAAI,KAAA;QACA,IAAAA,KAAA;UACA,KAAA7L,UAAA;QACA;MACA;IACA;IACA4L,eAAArB,MAAA,EAAA/H,QAAA;MACA,IAAA+H,MAAA,CAAA3B,MAAA;QACApG,QAAA;QACA;MACA;MAEA,MAAAsJ,kBAAA,GAAAvB,MAAA,CAAArE,GAAA,CAAA+C,KAAA;QACA,WAAA8C,OAAA,CAAAC,OAAA;UACA,KAAAC,KAAA,CAAA/L,IAAA,CAAAgM,aAAA,CAAAjD,KAAA,EAAAkD,YAAA;YACA1J,OAAA,CAAAC,GAAA,OAAAuG,KAAA,UAAAkD,YAAA;YACAH,OAAA;cAAA/C,KAAA;cAAAkD;YAAA;UACA;QACA;MACA;MAEAJ,OAAA,CAAAK,GAAA,CAAAN,kBAAA,EAAAO,IAAA,CAAAC,OAAA;QACA,MAAAC,QAAA,GAAAD,OAAA,CAAAE,IAAA,CAAAC,MAAA,IAAAA,MAAA,CAAAN,YAAA;QACA1J,OAAA,CAAAC,GAAA,UAAA4J,OAAA,YAAAC,QAAA;QACA/J,QAAA,EAAA+J,QAAA;MACA;IACA;IACAG,SAAA;MACA,KAAA1M,UAAA;IACA;IACA,MAAA2M,WAAA;MACA,KAAAV,KAAA,CAAA/L,IAAA,CAAA0M,QAAA,OAAAf,KAAA;QACA,IAAAA,KAAA;UACA;YACA,MAAAgB,QAAA;YACA,MAAAnE,YAAA;YAEA,WAAApB,UAAA,SAAApH,IAAA,CAAAM,eAAA;cACA,MAAAsM,YAAA,QAAA5M,IAAA,CAAAQ,oBAAA,CAAA4G,UAAA;cACA,MAAAyF,WAAA,QAAA7M,IAAA,CAAAU,kBAAA,CAAA0G,UAAA;cAEAuF,QAAA,CAAApF,IAAA;gBACAuF,YAAA;gBACArJ,WAAA,EAAA2D,UAAA;gBACA2F,WAAA,MAAAC,IAAA,GAAAC,cAAA,UAAAC,OAAA;gBACAxC,iBAAA,EAAAmC,WAAA,CAAAnE,MAAA,OAAAmE,WAAA,CAAAM,IAAA;gBACA,GAAAP;cACA;cAEA,SAAA5M,IAAA,CAAAa,0BAAA,CAAAuG,UAAA;gBACAqC,MAAA,CAAAqB,MAAA,MAAA9K,IAAA,CAAAa,0BAAA,CAAAuG,UAAA,GAAAuC,OAAA,CAAAyD,WAAA;kBACA5E,YAAA,CAAAjB,IAAA;oBACA9D,WAAA,EAAA2D,UAAA;oBACA,GAAAgG;kBACA;gBACA;cACA;YACA;YAEA,WAAAhG,UAAA,SAAApH,IAAA,CAAAO,gBAAA;cACA,MAAAqM,YAAA,QAAA5M,IAAA,CAAAS,qBAAA,CAAA2G,UAAA;cACA,MAAAyF,WAAA,QAAA7M,IAAA,CAAAW,mBAAA,CAAAyG,UAAA;cAEAuF,QAAA,CAAApF,IAAA;gBACAuF,YAAA;gBACArJ,WAAA,EAAA2D,UAAA;gBACA2F,WAAA,MAAAC,IAAA,GAAAC,cAAA,UAAAC,OAAA;gBACAxC,iBAAA,EAAAmC,WAAA,CAAAnE,MAAA,OAAAmE,WAAA,CAAAM,IAAA;gBACA,GAAAP;cACA;YACA;YAEA;cAAAtM,eAAA;cAAAI,kBAAA;cAAAF,oBAAA;cAAAG,mBAAA;cAAAJ,gBAAA;cAAAE,qBAAA;cAAAG,mBAAA;cAAAyM,oBAAA;cAAAxM,0BAAA;cAAAyM,2BAAA;cAAApM,mBAAA;cAAAC,sBAAA;cAAA,GAAAoM;YAAA,SAAAvN,IAAA;YACAuC,OAAA,CAAAkG,IAAA,CAAAnI,eAAA,EAAAI,kBAAA,EAAAF,oBAAA,EAAAG,mBAAA,EAAAJ,gBAAA,EAAAE,qBAAA,EAAAG,mBAAA,EAAAyM,oBAAA,EAAAxM,0BAAA,EAAAyM,2BAAA,EAAApM,mBAAA,EAAAC,sBAAA;YAEAoM,UAAA,CAAAC,WAAA,OAAAR,IAAA,GAAAC,cAAA,UAAAC,OAAA;YAEA,WAAAnF,KAAA,CAAAC,IAAA;cACAC,GAAA;cACApI,IAAA;gBACA,GAAA0N,UAAA;gBACAtN,QAAA,OAAAwN,MAAA,CAAAjH,MAAA,CAAAP;cACA;cACA0G,QAAA,EAAAA,QAAA;cACAe,aAAA,EAAAlF,YAAA;cACAtH,mBAAA,EAAAA,mBAAA;cACAC,sBAAA,EAAAA;YACA;YACA,KAAA+I,QAAA,CAAAyD,OAAA;YACA,KAAAC,OAAA,CAAArG,IAAA;UACA,SAAAV,KAAA;YACA,KAAAqD,QAAA,CAAArD,KAAA;YACAtE,OAAA,CAAAsE,KAAA,0BAAAA,KAAA;UACA;QACA;MACA;IACA;IACA,MAAAgH,iBAAA;MACA,UAAAJ,MAAA,CAAAjH,MAAA,CAAAP,EAAA;QACA;MACA;MAEA,KAAAlG,OAAA;MACA;QACA,MAAA+H,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YAAAjI,QAAA,OAAAwN,MAAA,CAAAjH,MAAA,CAAAP;UAAA;QACA;QACA,MAAA6H,SAAA,GAAAhG,QAAA,CAAAjI,IAAA,CAAAA,IAAA;QACA,MAAA8M,QAAA,GAAA7E,QAAA,CAAAjI,IAAA,CAAA8M,QAAA;QACA,MAAAnE,YAAA,GAAAV,QAAA,CAAAjI,IAAA,CAAA6N,aAAA;QAEA,KAAAxK,sBAAA;QACA,KAAAC,uBAAA;QACAwJ,QAAA,CAAAhD,OAAA,CAAAoE,OAAA;UACA,IAAAA,OAAA,CAAAjB,YAAA;YACA,KAAA5J,sBAAA,CAAA6K,OAAA,CAAAtK,WAAA,IAAAsK,OAAA;UACA,WAAAA,OAAA,CAAAjB,YAAA;YACA,KAAA3J,uBAAA,CAAA4K,OAAA,CAAAtK,WAAA,IAAAsK,OAAA;UACA;QACA;QACA,MAAAC,cAAA,GAAArB,QAAA,CACAvG,MAAA,CAAAC,QAAA,IAAAA,QAAA,CAAAyG,YAAA,QACA9G,GAAA,CAAAK,QAAA,IAAAA,QAAA,CAAA5C,WAAA;QAEA,MAAAwK,eAAA,GAAAtB,QAAA,CACAvG,MAAA,CAAAC,QAAA,IAAAA,QAAA,CAAAyG,YAAA,QACA9G,GAAA,CAAAK,QAAA,IAAAA,QAAA,CAAA5C,WAAA;QAEA,KAAAL,gBAAA;QACAoF,YAAA,CAAAmB,OAAA,CAAAzB,KAAA;UACA,KAAA9E,gBAAA,CAAA8E,KAAA,CAAA8C,iBAAA,IAAA9C,KAAA;QACA;QAEA3F,OAAA,CAAAkG,IAAA,0BAAArF,gBAAA;QAEA,KAAApD,IAAA;UACA,QAAAA,IAAA;UACA,GAAA8N,SAAA;UACA3N,qBAAA,EAAA+F,QAAA,CAAA4H,SAAA,CAAA3N,qBAAA;UACAG,eAAA,EAAA0N,cAAA;UACAzN,gBAAA,EAAA0N;QACA;QAEA,IAAAH,SAAA,CAAA/M,uBAAA;UACA,KAAAmN,yBAAA,CAAAhI,QAAA,CAAA4H,SAAA,CAAA/M,uBAAA;QACA;UACA,KAAAc,cAAA;UACA,KAAAC,aAAA;QACA;QACA,KAAAqM,qBAAA;QAEA,WAAAC,SAAA;QACA,WAAA7E,yBAAA,MAAAvJ,IAAA,CAAAM,eAAA;QACA,WAAA8K,0BAAA,MAAApL,IAAA,CAAAO,gBAAA;MAEA,SAAAsG,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;QACAtE,OAAA,CAAAsE,KAAA,iCAAAA,KAAA;MACA;QACA,KAAA9G,OAAA;MACA;IACA;IACAsO,oBAAAjH,UAAA;MACA,KAAA9D,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,WAAA,EAAA2D;MACA;MACA,KAAA/D,sBAAA;MACA,KAAA+K,SAAA;QACA,KAAArC,KAAA,CAAAuC,UAAA,SAAAvC,KAAA,CAAAuC,UAAA,CAAAC,aAAA;MACA;IACA;IACA,MAAAC,aAAA;MACA,KAAAzC,KAAA,CAAAuC,UAAA,CAAA5B,QAAA,OAAAf,KAAA;QACA,IAAAA,KAAA;UACA;YACA,KAAAhI,YAAA;YACA,WAAAoE,KAAA,CAAAC,IAAA;cACAC,GAAA;cACApI,IAAA;gBACA,QAAAyD,aAAA;gBACAG,WAAA,OAAAH,aAAA,CAAAG;cACA;YACA;YACA,KAAAyG,QAAA,CAAAyD,OAAA;YACA,KAAAtK,sBAAA;YACA,KAAAC,aAAA;cACAC,WAAA;cACAC,UAAA;cACAC,WAAA;YACA;YACA,WAAA8F,yBAAA,MAAAvJ,IAAA,CAAAM,eAAA;YACA,WAAA8K,0BAAA,MAAApL,IAAA,CAAAO,gBAAA;UACA,SAAAsG,KAAA;YACA,KAAAqD,QAAA,CAAArD,KAAA;YACAtE,OAAA,CAAAsE,KAAA,6BAAAA,KAAA;UACA;YACA,KAAAlD,YAAA;UACA;QACA;MACA;IACA;IACA8K,eAAA;MACA,UAAAzO,IAAA,CAAAC,QAAA;MACA,MAAAyO,GAAA,kDAAA1O,IAAA,CAAAC,QAAA;MACA0O,MAAA,CAAAC,IAAA,CAAAF,GAAA;IACA;IACA,MAAAG,kBAAA;MACA,UAAA7O,IAAA,CAAAC,QAAA;MACA;QACA,WAAA6O,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAtM,IAAA;QACA;QACA,WAAAqF,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YAAAjI,QAAA,OAAAD,IAAA,CAAAC;UAAA;QACA;QACA,KAAAiK,QAAA,CAAAyD,OAAA;QACA,KAAAC,OAAA,CAAArG,IAAA;MACA,SAAAV,KAAA;QACA,IAAAA,KAAA;UACA,KAAAqD,QAAA,CAAArD,KAAA;UACAtE,OAAA,CAAAsE,KAAA,0BAAAA,KAAA;QACA;MACA;IACA;IACA,MAAAoI,qBAAA;MACA;QACA,MAAAnH,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACAgH,SAAA;UACA;QACA;QACA,KAAAnM,eAAA,GAAA+E,QAAA,CAAAjI,IAAA,CAAAA,IAAA;MACA,SAAAgH,KAAA;QACAtE,OAAA,CAAAsE,KAAA,qCAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;IACA;IACA;IACA,MAAAsI,mBAAA;MACA;QACA,MAAArH,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACAgH,SAAA;UACA;QACA;QACA,KAAA7J,aAAA,GAAAyC,QAAA,CAAAjI,IAAA,CAAAA,IAAA;MACA,SAAAgH,KAAA;QACAtE,OAAA,CAAAsE,KAAA,mCAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;IACA;IACA;IACA,MAAAuI,sBAAA;MACA;QACA,MAAAtH,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACAgH,SAAA;UACA;QACA;QACA,KAAA5J,gBAAA,GAAAwC,QAAA,CAAAjI,IAAA,CAAAA,IAAA;MACA,SAAAgH,KAAA;QACAtE,OAAA,CAAAsE,KAAA,uCAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;IACA;IACA,MAAAwI,kBAAA;MACA;QACA,MAAAvH,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;QACA;QACA,KAAApF,YAAA,GAAAiF,QAAA,CAAAjI,IAAA,CAAAA,IAAA;QACA0C,OAAA,CAAAC,GAAA,gBAAAK,YAAA;MACA,SAAAgE,KAAA;QACAtE,OAAA,CAAAsE,KAAA,kCAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;IACA;IACA,MAAAyI,eAAA;MACA;QACA,MAAAxH,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;QACA;QACA,KAAAnF,SAAA,GAAAgF,QAAA,CAAAjI,IAAA,CAAAA,IAAA;MACA,SAAAgH,KAAA;QACAtE,OAAA,CAAAsE,KAAA,+BAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;IACA;IACA,MAAA0I,gBAAA;MACA;QACA,MAAAzH,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;QACA;QACA,KAAAhD,UAAA,GAAA6C,QAAA,CAAAjI,IAAA,CAAAA,IAAA;MACA,SAAAgH,KAAA;QACAtE,OAAA,CAAAsE,KAAA,gCAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;IACA;IACA2I,wBAAA;MACAjN,OAAA,CAAAC,GAAA,gBAAAxC,IAAA,CAAAE,wBAAA;MACA,KAAAkO,SAAA;QACA,KAAArC,KAAA,CAAA/L,IAAA,CAAAuO,aAAA;MACA;IACA;IACAkB,qBAAA;MACA,KAAAzP,IAAA,CAAAM,eAAA;MACA,KAAAN,IAAA,CAAAO,gBAAA;MACA,KAAAyC,sBAAA;MACA,KAAAC,uBAAA;MACA,KAAAjD,IAAA,CAAAQ,oBAAA;MACA,KAAAR,IAAA,CAAAS,qBAAA;MACA,KAAAT,IAAA,CAAAU,kBAAA;MACA,KAAAV,IAAA,CAAAW,mBAAA;MACA,KAAAX,IAAA,CAAAY,mBAAA;MACA,KAAAZ,IAAA,CAAAa,0BAAA;MACA,KAAAb,IAAA,CAAAkB,mBAAA;MACA,KAAAlB,IAAA,CAAAmB,sBAAA;MACA,KAAAiE,qBAAA;MACA,KAAAgJ,SAAA;QACA,KAAArC,KAAA,CAAA/L,IAAA,CAAAuO,aAAA;MACA;IACA;IACAmB,yBAAAtI,UAAA;MACA,MAAA2D,cAAA,QAAA/K,IAAA,CAAAY,mBAAA,CAAAwG,UAAA;MAEA,UAAApH,IAAA,CAAAa,0BAAA,CAAAuG,UAAA;QACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAa,0BAAA,EAAAuG,UAAA;MACA;MAEA,MAAAf,QAAA,QAAArD,sBAAA,CAAA2C,IAAA,CAAA2C,CAAA,IAAAA,CAAA,CAAA7E,WAAA,KAAA2D,UAAA;MACA,MAAAmB,cAAA,GAAAlC,QAAA,GAAAA,QAAA,CAAAmC,YAAA;MAEA,IAAAD,cAAA,CAAAG,MAAA;QACA;MACA;MAEAe,MAAA,CAAAC,IAAA,MAAA3H,KAAA,EAAA4H,OAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,UAAA,+BAAAzC,UAAA;UACA,KAAA2C,OAAA,MAAAhI,KAAA,EAAA6H,GAAA;QACA;MACA;MAEAmB,cAAA,CAAApB,OAAA,CAAAzB,KAAA;QACA,UAAAlI,IAAA,CAAAa,0BAAA,CAAAuG,UAAA,EAAAc,KAAA;UAEA,MAAAyH,WAAA,GAAApH,cAAA,CAAA5C,IAAA,CAAA2C,CAAA,IAAAA,CAAA,CAAA0C,iBAAA,KAAA9C,KAAA;UAEA,KAAAZ,IAAA,MAAAtH,IAAA,CAAAa,0BAAA,CAAAuG,UAAA,GAAAc,KAAA;YACA8C,iBAAA,EAAA2E,WAAA,CAAA3E,iBAAA;YACArC,iBAAA,EAAAgH,WAAA,CAAAhH,iBAAA;YACAsC,0BAAA;YACAC,qBAAA;YACAC,uBAAA;UACA;QACA;QAEA,MAAAM,YAAA,iCAAArE,UAAA,IAAAc,KAAA;QACA,KAAAZ,IAAA,MAAAvF,KAAA,KAAA0J,YAAA,gCACA;UAAAzJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAA0N,OAAA;UAAA3N,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,KAAAoF,IAAA,MAAAvF,KAAA,KAAA0J,YAAA,2BACA;UAAAzJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAA0N,OAAA;UAAA3N,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,KAAAoF,IAAA,MAAAvF,KAAA,KAAA0J,YAAA,6BACA;UAAAzJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAA0N,OAAA;UAAA3N,OAAA;UAAAC,OAAA;QAAA,EACA;MACA;MAEAuH,MAAA,CAAAC,IAAA,MAAA1J,IAAA,CAAAa,0BAAA,CAAAuG,UAAA,GAAAuC,OAAA,CAAAzB,KAAA;QACA,KAAA6C,cAAA,CAAAzE,QAAA,CAAA4B,KAAA;UACA,KAAA6B,OAAA,MAAA/J,IAAA,CAAAa,0BAAA,CAAAuG,UAAA,GAAAc,KAAA;QACA;MACA;IACA;IACA2H,yBAAAzI,UAAA;MACA,MAAA2D,cAAA,QAAA/K,IAAA,CAAAY,mBAAA,CAAAwG,UAAA;MACA,MAAA0I,MAAA,QAAA9P,IAAA,CAAAa,0BAAA,CAAAuG,UAAA;MAEA,MAAA2I,GAAA,GAAAhF,cAAA,CAAA/E,GAAA,CAAAwF,SAAA;QACAR,iBAAA,EAAAQ,SAAA;QACA7C,iBAAA,EAAAmH,MAAA,CAAAtE,SAAA,KAAAsE,MAAA,CAAAtE,SAAA,EAAA7C,iBAAA;QACAsC,0BAAA,EAAA6E,MAAA,CAAAtE,SAAA,KAAAsE,MAAA,CAAAtE,SAAA,EAAAP,0BAAA;QACAC,qBAAA,EAAA4E,MAAA,CAAAtE,SAAA,KAAAsE,MAAA,CAAAtE,SAAA,EAAAN,qBAAA;QACAC,uBAAA,EAAA2E,MAAA,CAAAtE,SAAA,KAAAsE,MAAA,CAAAtE,SAAA,EAAAL,uBAAA;MACA;MAEA5I,OAAA,CAAAkG,IAAA,QAAAsH,GAAA;MACA,OAAAA,GAAA;IACA;IACAC,wBAAA5I,UAAA,EAAA6I,SAAA,EAAAlH,KAAA,EAAA1G,KAAA;MACA,UAAArC,IAAA,CAAAa,0BAAA,CAAAuG,UAAA;QACA,KAAAE,IAAA,MAAAtH,IAAA,CAAAa,0BAAA,EAAAuG,UAAA;MACA;MACA,UAAApH,IAAA,CAAAa,0BAAA,CAAAuG,UAAA,EAAA6I,SAAA;QACA,KAAA3I,IAAA,MAAAtH,IAAA,CAAAa,0BAAA,CAAAuG,UAAA,GAAA6I,SAAA;MACA;MACA,KAAA3I,IAAA,MAAAtH,IAAA,CAAAa,0BAAA,CAAAuG,UAAA,EAAA6I,SAAA,GAAAlH,KAAA,EAAA1G,KAAA;IACA;IACA6N,YAAA;MACA,aAAApO,aAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAqO,YAAA;MACA,aAAArO,aAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAsO,QAAA;MACA,aAAAtO,aAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAqM,sBAAA;MACA,IAAAkC,YAAA;MACA,aAAAvO,aAAA;QACA;UACAuO,YAAA,QAAAxO,cAAA;UACA;QACA;UACAwO,YAAA,QAAAxO,cAAA;UACA;QACA;UACAwO,YAAA,QAAAxO,cAAA;UACA;MACA;MAEA,IAAAwO,YAAA;QACAA,YAAA;QACA,KAAAxO,cAAA;QACA,KAAAC,aAAA;MACA;MAEA,KAAA9B,IAAA,CAAAe,uBAAA,GAAAsP,YAAA;MACA,OAAAA,YAAA;IACA;IACAnC,0BAAAoC,OAAA;MACA,KAAAA,OAAA,IAAAA,OAAA;QACA,KAAAzO,cAAA;QACA,KAAAC,aAAA;QACA;MACA;MAEA,IAAAwO,OAAA,YAAAA,OAAA;QACA,KAAAzO,cAAA,GAAAyO,OAAA;QACA,KAAAxO,aAAA;MACA,WAAAwO,OAAA,UAAAA,OAAA;QACA,KAAAzO,cAAA,GAAAyO,OAAA;QACA,KAAAxO,aAAA;MACA;QACA,KAAAD,cAAA,GAAAyO,OAAA;QACA,KAAAxO,aAAA;MACA;IACA;IACAyO,2BAAA;MACA,KAAApC,qBAAA;IACA;IACAqC,0BAAA;MACA,MAAAC,cAAA,QAAAtC,qBAAA;MACA,KAAAD,yBAAA,CAAAuC,cAAA;MACA,KAAAtC,qBAAA;IACA;IACAuC,qBAAA;MACA,KAAA5M,cAAA;QACAC,YAAA;MACA;MACA,KAAAH,uBAAA;MACA,KAAAwK,SAAA;QACA,KAAArC,KAAA,CAAA4E,WAAA,SAAA5E,KAAA,CAAA4E,WAAA,CAAApC,aAAA;MACA;IACA;IACA,MAAAqC,cAAA;MACA,KAAA7E,KAAA,CAAA4E,WAAA,CAAAjE,QAAA,OAAAf,KAAA;QACA,IAAAA,KAAA;UACA;YACA,KAAA5G,aAAA;YACA,WAAAgD,KAAA,CAAAC,IAAA;cACAC,GAAA;cACApI,IAAA,OAAAiE;YACA;YACA,KAAAoG,QAAA,CAAAyD,OAAA;YACA,KAAA/J,uBAAA;YACA,WAAAyL,iBAAA;YACA,SAAAxM,YAAA,CAAA6F,MAAA;cACA,MAAAmI,UAAA,QAAAhO,YAAA,CAAA8C,IAAA,CAAAmL,OAAA,IAAAA,OAAA,CAAA/M,YAAA,UAAAD,cAAA,CAAAC,YAAA;cACA,IAAA8M,UAAA;gBACA,KAAA7Q,IAAA,CAAAE,wBAAA,GAAA2Q,UAAA,CAAA9M,YAAA;cACA;YACA;UACA,SAAA8C,KAAA;YACA,KAAAqD,QAAA,CAAArD,KAAA;YACAtE,OAAA,CAAAsE,KAAA,8BAAAA,KAAA;UACA;YACA,KAAA9B,aAAA;UACA;QACA;MACA;IACA;IACAgM,sBAAA;MACA,KAAA/M,eAAA;QACAC,cAAA;QACAC,qBAAA;QACAC,8BAAA;QACAC,+BAAA;QACAC,wBAAA;QACAC,sCAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,eAAA;MACA;MACA,KAAAC,mBAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,gCAAA;MACA,KAAAf,wBAAA;MACA,KAAAuK,SAAA;QACA,KAAArC,KAAA,CAAAiF,YAAA,SAAAjF,KAAA,CAAAiF,YAAA,CAAAzC,aAAA;MACA;IACA;IACA,MAAA0C,2BAAAC,WAAA;MACA,KAAAlN,eAAA,CAAAO,mBAAA,GAAA2M,WAAA,CAAA/D,IAAA;MAEA,WAAAgE,4BAAA,CAAAD,WAAA;IACA;IACA,MAAAC,6BAAA3H,WAAA;MACA,IAAAA,WAAA,CAAAd,MAAA;QACA,KAAA9D,gCAAA;QACA,KAAAD,oBAAA;QACA,KAAAX,eAAA,CAAAG,8BAAA;QACA;MACA;MAEA;QACA,MAAAiN,oBAAA;QACA,WAAAhK,UAAA,IAAAoC,WAAA;UACA,MAAAhD,MAAA,cAAA2B,yBAAA,CAAAf,UAAA;UACAgK,oBAAA,CAAA7J,IAAA,CAAAf,MAAA;QACA;QAEA,MAAA6K,SAAA;QACA,MAAAC,SAAA,OAAAC,GAAA;QAEAH,oBAAA,CAAAzH,OAAA,CAAApB,cAAA;UACAA,cAAA,CAAAoB,OAAA,CAAAzB,KAAA;YACA,KAAAoJ,SAAA,CAAAE,GAAA,CAAAtJ,KAAA,CAAA8C,iBAAA;cACAsG,SAAA,CAAAG,GAAA,CAAAvJ,KAAA,CAAA8C,iBAAA;cACAqG,SAAA,CAAA9J,IAAA,CAAAW,KAAA;YACA;UACA;QACA;QAEA,KAAAtD,gCAAA,GAAAyM,SAAA;QAEA,KAAA1M,oBAAA;QACA,KAAAX,eAAA,CAAAG,8BAAA;MAEA,SAAA0C,KAAA;QACAtE,OAAA,CAAAsE,KAAA,gDAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;QACA,KAAAjC,gCAAA;MACA;IACA;IACA8M,+BAAAC,aAAA;MACA,KAAA3N,eAAA,CAAAG,8BAAA,GAAAsC,IAAA,CAAAmL,SAAA,CAAAD,aAAA;IACA;IACA,MAAAE,eAAA;MACA,KAAA9F,KAAA,CAAAiF,YAAA,CAAAtE,QAAA,OAAAf,KAAA;QACA,IAAAA,KAAA;UACA;YACA,KAAA3G,cAAA;YACA,WAAA+C,KAAA,CAAAC,IAAA;cACAC,GAAA;cACApI,IAAA,OAAAmE;YACA;YACA,KAAAkG,QAAA,CAAAyD,OAAA;YACA,KAAA9J,wBAAA;YACA,WAAAyL,cAAA;UACA,SAAAzI,KAAA;YACA,KAAAqD,QAAA,CAAArD,KAAA;YACAtE,OAAA,CAAAsE,KAAA,gCAAAA,KAAA;UACA;YACA,KAAA7B,cAAA;UACA;QACA;MACA;IACA;EACA;EACA,MAAA8M,QAAA;IACA,WAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;IAAA;IACA,WAAA5C,iBAAA;IACA,WAAAC,cAAA;IACA,WAAAC,eAAA;IACA,WAAA1B,gBAAA;IACA,WAAAoB,oBAAA;IACA,WAAAE,kBAAA;IACA,WAAAC,qBAAA;EACA;AACA", "ignoreList": []}]}