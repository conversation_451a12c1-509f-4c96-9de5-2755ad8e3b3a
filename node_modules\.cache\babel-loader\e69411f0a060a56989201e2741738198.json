{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js!E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\scene\\EditScene.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\scene\\EditScene.vue", "mtime": 1754017528000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapState", "mapActions", "name", "data", "activeStep", "loading", "form", "scene_id", "linked_project_team_name", "linked_task_type_code", "scene_name", "scene_description", "input_platforms", "output_platforms", "input_platforms_data", "output_platforms_data", "input_data_options", "output_data_options", "input_effect_params", "input_effect_params_config", "updated_prompt", "scene_running_frequency", "hour", "modality", "day", "weeks", "stored_strategy_refresh_days", "explore_strategy_trigger_days", "scene_business_type", "baseline_data_start_days_ago", "baseline_data_exclude_recent_days", "min_baseline_sample_count", "baseline_refresh_frequency_days", "frequencyValue", "frequencyUnit", "rules", "required", "message", "trigger", "validator", "rule", "value", "callback", "console", "log", "Error", "type", "min", "max", "projectTeams", "taskTypes", "modalityOptions", "selectedInputPlatforms", "selectedOutputPlatforms", "sceneInputAccountsData", "sceneOutputAccountsData", "effectParamsData", "addOptionDialogVisible", "newOptionForm", "option_name", "option_key", "platform_id", "optionRules", "addingOption", "addProjectDialogVisible", "addTaskTypeDialogVisible", "newProjectForm", "project_name", "newTaskTypeForm", "task_type_name", "task_type_description", "recommended_effect_param_codes", "effect_param_relationships_note", "is_content_from_external", "is_bilateral_pref_consideration_needed", "linked_platform_ids", "task_type_status", "task_type_owner", "selectedPlatformIds", "selectedEffectParams", "availableEffectParamsForTaskType", "projectRules", "taskTypeRules", "addingProject", "addingTaskType", "sceneTypes", "loadingInputPlatforms", "loadingOutputPlatforms", "computed", "availablePlatforms", "platforms", "selectedTaskType", "find", "taskType", "task_type_code", "linkedPlatformIds", "split", "map", "id", "parseInt", "trim", "filter", "platform", "includes", "availableEffectParams", "params", "JSON", "parse", "Array", "isArray", "error", "isPlatformConfigLoading", "methods", "fetchPlatformOptions", "platformId", "response", "$http", "post", "api", "param", "fetchPlatformEffectParams", "getAvailableEffectParamsForPlatform", "recommendedParams", "p", "platformParams", "effectParams", "info", "length", "effect_param_name", "getFieldComponent", "componentMap", "getFieldProps", "field", "props", "placeholder", "label", "field_type", "rows", "multiple", "options", "handleInputPlatformChange", "platformIds", "Object", "keys", "for<PERSON>ach", "key", "startsWith", "endsWith", "$delete", "platformsWithDetails", "detailResponse", "push", "$message", "platform_name", "$set", "additional_Information", "platformWithDetails", "fields", "fieldProp", "field_name", "accountData", "undefined", "adding_data_types", "selectedOptions", "item", "platformEffectParams", "values", "selectedPara<PERSON>", "effect_param_code", "configured_evaluation_days", "default_baseline_mean", "default_baseline_stddev", "handleOutputPlatformChange", "nextStep", "warning", "fieldsToValidate", "paramCode", "configPrefix", "validateFields", "valid", "validationPromises", "Promise", "resolve", "$refs", "validateField", "errorMessage", "all", "then", "results", "<PERSON><PERSON><PERSON><PERSON>", "some", "result", "prevStep", "submitForm", "validate", "accounts", "platformData", "dataOptions", "operate_type", "create_time", "Date", "toLocaleString", "replace", "join", "paramConfig", "output_effect_params", "output_effect_params_config", "submitData", "update_time", "$route", "effect_params", "success", "$router", "fetchSceneDetail", "sceneData", "account", "inputPlatforms", "outputPlatforms", "parseFrequencyFromMinutes", "calculateTotalMinutes", "$nextTick", "showAddOptionDialog", "optionForm", "clearValidate", "addNewOption", "openAIOptimize", "url", "window", "open", "handleDeleteScene", "$confirm", "confirmButtonText", "cancelButtonText", "fetchModalityOptions", "dict_type", "fetchProjectTeams", "fetchTaskTypes", "fetchSceneTypes", "handleProjectTeamChange", "handleTaskTypeChange", "handleEffectParamsChange", "effectParam", "pattern", "getEffectParamsTableData", "config", "ret", "updateEffectParamConfig", "paramName", "getMinValue", "getMaxValue", "getStep", "totalMinutes", "minutes", "handleFrequencyValueChange", "handleFrequencyUnitChange", "currentMinutes", "showAddProjectDialog", "projectForm", "addNewProject", "newProject", "project", "showAddTaskTypeDialog", "taskTypeForm", "handlePlatformSelectChange", "selectedIds", "fetchEffectParamsForTaskType", "platformParamsArrays", "allParams", "seenCodes", "Set", "has", "add", "handleEffectParamsSelectChange", "selectedCodes", "stringify", "addNewTaskType", "created", "fetchPlatforms", "page", "pageSize"], "sources": ["src/views/scene/EditScene.vue"], "sourcesContent": ["<template>\n    <div class=\"edit-scene\">\n        <el-breadcrumb separator=\"/\" class=\"edit-breadcrumb\" style=\"margin-bottom: 18px;\">\n            <el-breadcrumb-item :to=\"{ path: '/scene/manage' }\">场景管理</el-breadcrumb-item>\n            <el-breadcrumb-item :to=\"{ path: '/scene/edit/'+ form.scene_id }\">{{ form.scene_name }}</el-breadcrumb-item>\n            <el-breadcrumb-item>场景编辑</el-breadcrumb-item>\n        </el-breadcrumb>\n        <div class=\"edit-actions\">\n            <el-button type=\"primary\" size=\"small\" @click=\"openAIOptimize\" :disabled=\"!form.scene_id\">AI优化提示词</el-button>\n            <el-button type=\"danger\" size=\"small\" @click=\"handleDeleteScene\" :disabled=\"!form.scene_id\">删除场景</el-button>\n        </div>\n        <el-steps :active=\"activeStep\" finish-status=\"success\" simple>\n            <el-step title=\"基本信息\" />\n            <el-step title=\"计算基准线配置\" />\n            <el-step title=\"数据输入平台\" />\n            <el-step title=\"数据输出平台\" />\n            <el-step title=\"其他设置\" />\n        </el-steps>\n\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"150px\" class=\"mt-20\" v-loading=\"loading\">\n            <div v-show=\"activeStep === 0\">\n                <el-form-item label=\"场景名称\" prop=\"scene_name\">\n                    <el-input v-model=\"form.scene_name\" placeholder=\"请输入场景名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型\" prop=\"linked_task_type_code\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_task_type_code\" placeholder=\"请选择任务类型\" style=\"flex: 1;\" @change=\"handleTaskTypeChange\">\n                            <el-option\n                                v-for=\"taskType in taskTypes\"\n                                :key=\"taskType.task_type_code\"\n                                :label=\"taskType.task_type_name\"\n                                :value=\"taskType.task_type_code\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddTaskTypeDialog\">\n                            新增任务类型\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景类型\" prop=\"scene_business_type\">\n                    <el-select \n                        v-model=\"form.scene_business_type\" \n                        placeholder=\"请选择或输入场景类型\" \n                        filterable \n                        allow-create \n                        default-first-option\n                        style=\"width: 100%\">\n                        <el-option\n                            v-for=\"sceneType in sceneTypes\"\n                            :key=\"sceneType.scene_type_id || sceneType.scene_type_code\"\n                            :label=\"sceneType.scene_type_name\"\n                            :value=\"sceneType.scene_type_name\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"项目组\" prop=\"linked_project_team_name\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_project_team_name\" placeholder=\"请选择项目组\" style=\"flex: 1;\" @change=\"handleProjectTeamChange\">\n                            <el-option\n                                v-for=\"project in projectTeams\"\n                                :key=\"project.project_id\"\n                                :label=\"project.project_name\"\n                                :value=\"project.project_name\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddProjectDialog\">\n                            新增项目组\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景描述\" prop=\"scene_description\">\n                    <el-input type=\"textarea\" v-model=\"form.scene_description\" placeholder=\"请输入场景描述\"\n                        :rows=\"4\"></el-input>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 1\">\n                <el-form-item label=\"使用数据的起始天数\" prop=\"baseline_data_start_days_ago\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_start_days_ago\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线使用数据的起始天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"排除最近的数据天数\" prop=\"baseline_data_exclude_recent_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_exclude_recent_days\"\n                            :min=\"0\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线排除最近的数据天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <!-- <el-form-item label=\"样本总数\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input \n                            value=\"将根据上述配置自动计算\" \n                            disabled \n                            style=\"width: 300px;\">\n                        </el-input>\n                        <span style=\"color: #909399; font-size: 12px;\">基于起始天数和排除天数范围内的数据量</span>\n                    </div>\n                </el-form-item> -->\n                <el-form-item label=\"样本量最小阈值\" prop=\"min_baseline_sample_count\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.min_baseline_sample_count\"\n                            :min=\"1\"\n                            :max=\"10000\"\n                            :step=\"1\"\n                            placeholder=\"请输入样本量\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">个</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"一个场景在一次计算基准线时，所需要的最小样本量\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"基准线更新频率\" prop=\"baseline_refresh_frequency_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_refresh_frequency_days\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入频率\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"评估效果的基准线更新的频率\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 2\">\n                <el-form-item label=\"选择输入平台\" prop=\"input_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.input_platforms\" @change=\"handleInputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingInputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedInputPlatforms && selectedInputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingInputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输入平台配置</h3>\n                    <el-card v-for=\"platform in selectedInputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <component :is=\"getFieldComponent(field.field_type)\"\n                                v-model=\"form.input_platforms_data[platform.platform_id][field.field_name]\"\n                                v-bind=\"getFieldProps(field)\"></component>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据类型\" :prop=\"'input_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.input_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\"\n                                    @click=\"showAddOptionDialog(platform.platform_id)\">\n                                    新增数据类型\n                                </el-button>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"效果参数配置\">\n                            <div class=\"effect-params-container\">\n                                <div v-if=\"getAvailableEffectParamsForPlatform(platform.platform_id).length === 0\" class=\"no-effect-params-tip\">\n                                    <el-alert\n                                        title=\"该平台暂无可用的效果参数\"\n                                        type=\"info\"\n                                        :closable=\"false\"\n                                        show-icon>\n                                    </el-alert>\n                                </div>\n                                <el-checkbox-group v-else v-model=\"form.input_effect_params[platform.platform_id]\" @change=\"handleEffectParamsChange(platform.platform_id)\">\n                                    <el-checkbox v-for=\"param in getAvailableEffectParamsForPlatform(platform.platform_id)\" :key=\"param.effect_param_code\" :label=\"param.effect_param_code\" class=\"effect-param-checkbox\">\n                                        {{ param.effect_param_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                \n                                <div v-if=\"form.input_effect_params[platform.platform_id] && form.input_effect_params[platform.platform_id].length > 0\" class=\"effect-params-table\">\n                                    <h4>参数配置详情</h4>\n                                    <el-table :data=\"getEffectParamsTableData(platform.platform_id)\" border>\n                                        <el-table-column prop=\"effect_param_name\" label=\"参数名称\" width=\"120\"></el-table-column>\n                                        <el-table-column prop=\"configured_evaluation_days\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"系统会获取发布时间在'T-基线'范围内，且已满足各参数的Tij 值的样本总数量。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *效果实现天数\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.configured_evaluation_days`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.configured_evaluation_days\" \n                                                        placeholder=\"如：3,5,10\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'configured_evaluation_days', scope.row.configured_evaluation_days)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_mean\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"μi是选取的样本量的效果数据里，该参数的平均值。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算μi，而是会使用您输入的缺省状态下的μi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *平均值\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_mean`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input\n                                                        v-model=\"scope.row.default_baseline_mean\" \n                                                        placeholder=\"如：0\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_mean', scope.row.default_baseline_mean)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_stddev\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"σi是选取的样本量的效果数据里，该参数的标准差。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算σi，而是会使用您输入的缺省状态下的σi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *标准差\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_stddev`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.default_baseline_stddev\" \n                                                        placeholder=\"如：1\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_stddev', scope.row.default_baseline_stddev)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                    </el-table>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.input_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n            </div>\n\n            <div v-show=\"activeStep === 3\">\n                <el-form-item label=\"选择输出平台\" prop=\"output_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.output_platforms\" @change=\"handleOutputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingOutputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedOutputPlatforms && selectedOutputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingOutputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输出平台配置</h3>\n                    <el-card v-for=\"platform in selectedOutputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <component :is=\"getFieldComponent(field.field_type)\"\n                                v-model=\"form.output_platforms_data[platform.platform_id][field.field_name]\"\n                                v-bind=\"getFieldProps(field)\"></component>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据内容\" :prop=\"'output_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.output_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\"\n                                    @click=\"showAddOptionDialog(platform.platform_id)\">\n                                    新增数据内容\n                                </el-button>\n                            </div>\n                        </el-form-item>\n\n\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.output_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n\n                <el-form-item label=\"模态\" prop=\"modality\">\n                    <el-select v-model=\"form.modality\" placeholder=\"请选择模态\">\n                        <el-option\n                            v-for=\"item in modalityOptions\"\n                            :key=\"item.dict_name\"\n                            :label=\"item.dict_name\"\n                            :value=\"item.dict_name\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 4\">\n                <el-form-item label=\"运行频率\" prop=\"scene_running_frequency\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-input-number\n                            v-model=\"frequencyValue\"\n                            :min=\"getMinValue()\"\n                            :max=\"getMaxValue()\"\n                            :step=\"getStep()\"\n                            placeholder=\"请输入数值\"\n                            style=\"width: 150px\"\n                            @change=\"handleFrequencyValueChange\">\n                        </el-input-number>\n                        <el-select \n                            v-model=\"frequencyUnit\" \n                            placeholder=\"请选择单位\"\n                            style=\"width: 120px\"\n                            @change=\"handleFrequencyUnitChange\">\n                            <el-option label=\"分钟\" value=\"minutes\"></el-option>\n                            <el-option label=\"小时\" value=\"hours\"></el-option>\n                            <el-option label=\"天\" value=\"days\"></el-option>\n                        </el-select>\n                        <span style=\"color: #909399; font-size: 12px;\">\n                            (最小间隔30分钟)\n                        </span>\n                    </div>\n                </el-form-item>\n                <!-- <el-form-item label=\"运行时间\" prop=\"time_config\">\n                    <el-row>\n                        <el-col :span=\"8\">\n                            <el-time-picker\n                                v-model=\"form.hour\"\n                                format=\"HH:00\"\n                                :picker-options=\"{\n                                    selectableRange: '00:00:00 - 23:00:00',\n                                    format: 'HH:00'\n                                }\"\n                                placeholder=\"请选择运行时间\"\n                                style=\"width: 100%\">\n                            </el-time-picker>\n                        </el-col>\n                        <el-col :span=\"8\" v-if=\"form.scene_running_frequency === '每月一次'\">\n                            <el-select v-model=\"form.day\" placeholder=\"请选择运行日期\" style=\"width: 100%\">\n                                <el-option\n                                    v-for=\"day in 31\"\n                                    :key=\"day\"\n                                    :label=\"`${day}日`\"\n                                    :value=\"day\">\n                                </el-option>\n                            </el-select>\n                        </el-col>\n                        <el-col :span=\"8\" v-if=\"['每周一次', '每两周一次'].includes(form.scene_running_frequency)\">\n                            <el-select v-model=\"form.weeks\" placeholder=\"请选择运行星期\" style=\"width: 100%\">\n                                <el-option label=\"星期一\" value=\"1\"></el-option>\n                                <el-option label=\"星期二\" value=\"2\"></el-option>\n                                <el-option label=\"星期三\" value=\"3\"></el-option>\n                                <el-option label=\"星期四\" value=\"4\"></el-option>\n                                <el-option label=\"星期五\" value=\"5\"></el-option>\n                                <el-option label=\"星期六\" value=\"6\"></el-option>\n                                <el-option label=\"星期日\" value=\"0\"></el-option>\n                            </el-select>\n                        </el-col>\n                    </el-row>\n                </el-form-item> -->\n                <el-form-item label=\"个性化进化更新频率\" prop=\"stored_strategy_refresh_days\">\n                    <el-input-number\n                        v-model=\"form.stored_strategy_refresh_days\"\n                        :min=\"0\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">建议您设为0</span>\n                </el-form-item>\n                <el-form-item label=\"AI自行探索频率\" prop=\"explore_strategy_trigger_days\">\n                    <el-input-number\n                        v-model=\"form.explore_strategy_trigger_days\"\n                        :min=\"1\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">目前我们的探索模式暂未上线，建议您先将Z值设为365天</span>\n                </el-form-item>\n                <el-form-item label=\"AI提示词\" prop=\"updated_prompt\">\n                    <el-input type=\"textarea\" v-model=\"form.updated_prompt\" placeholder=\"请输入AI提示词\" :rows=\"10\"></el-input>\n                </el-form-item>\n            </div>\n\n            <el-form-item class=\"navigation-buttons\">\n                <el-button v-if=\"activeStep > 0\" @click=\"prevStep\">上一步</el-button>\n                <el-button \n                    v-if=\"activeStep < 4\" \n                    type=\"primary\" \n                    @click=\"nextStep\"\n                    :disabled=\"isPlatformConfigLoading\"\n                    :loading=\"isPlatformConfigLoading\">\n                    {{ isPlatformConfigLoading ? '配置加载中...' : '下一步' }}\n                </el-button>\n                <el-button v-if=\"activeStep === 4\" type=\"primary\" @click=\"submitForm\">保存</el-button>\n            </el-form-item>\n        </el-form>\n\n        <el-dialog title=\"新增数据内容\" :visible.sync=\"addOptionDialogVisible\" width=\"500px\">\n            <el-form ref=\"optionForm\" :model=\"newOptionForm\" :rules=\"optionRules\" label-width=\"120px\">\n                <el-form-item label=\"内容名称\" prop=\"option_name\">\n                    <el-input v-model=\"newOptionForm.option_name\" placeholder=\"请输入内容名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"内容标识\" prop=\"option_key\">\n                    <el-input v-model=\"newOptionForm.option_key\" placeholder=\"请输入内容标识（英文）\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addOptionDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewOption\" :loading=\"addingOption\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增项目组\" :visible.sync=\"addProjectDialogVisible\" width=\"500px\">\n            <el-form ref=\"projectForm\" :model=\"newProjectForm\" :rules=\"projectRules\" label-width=\"120px\">\n                <el-form-item label=\"项目名称\" prop=\"project_name\">\n                    <el-input v-model=\"newProjectForm.project_name\" placeholder=\"请输入项目名称\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addProjectDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewProject\" :loading=\"addingProject\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增任务类型\" :visible.sync=\"addTaskTypeDialogVisible\" width=\"600px\">\n            <el-form ref=\"taskTypeForm\" :model=\"newTaskTypeForm\" :rules=\"taskTypeRules\" label-width=\"140px\">\n                <el-form-item label=\"任务类型名称\" prop=\"task_type_name\">\n                    <el-input v-model=\"newTaskTypeForm.task_type_name\" placeholder=\"请输入任务类型名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型描述\" prop=\"task_type_description\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.task_type_description\" placeholder=\"请输入任务类型描述\" :rows=\"3\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"关联平台\" prop=\"linked_platform_ids\">\n                    <el-select \n                        v-model=\"selectedPlatformIds\" \n                        multiple \n                        placeholder=\"请选择关联平台\"\n                        style=\"width: 100%\"\n                        @change=\"handlePlatformSelectChange\">\n                        <el-option\n                            v-for=\"platform in platforms\"\n                            :key=\"platform.platform_id\"\n                            :label=\"platform.platform_name\"\n                            :value=\"platform.platform_id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"推荐效果参数\" prop=\"recommended_effect_param_codes\">\n                    <el-select \n                        v-model=\"selectedEffectParams\" \n                        multiple \n                        placeholder=\"请选择推荐效果参数\"\n                        style=\"width: 100%\"\n                        @change=\"handleEffectParamsSelectChange\">\n                        <el-option\n                            v-for=\"param in availableEffectParamsForTaskType\"\n                            :key=\"param.effect_param_code\"\n                            :label=\"`${param.effect_param_name} (${param.effect_param_code})`\"\n                            :value=\"param.effect_param_name\">\n                        </el-option>\n                    </el-select>\n                    <div v-if=\"availableEffectParamsForTaskType.length === 0 && selectedPlatformIds.length > 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        所选平台暂无可用的效果参数\n                    </div>\n                    <div v-if=\"selectedPlatformIds.length === 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        请先选择关联平台\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"参数关系说明\" prop=\"effect_param_relationships_note\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.effect_param_relationships_note\" placeholder=\"请输入各推荐参数之间的逻辑关系说明\" :rows=\"3\"></el-input>\n                </el-form-item>\n                <!-- <el-form-item label=\"是否需要外部内容\" prop=\"is_content_from_external\">\n                    <el-select v-model=\"newTaskTypeForm.is_content_from_external\" placeholder=\"请选择\">\n                        <el-option label=\"是\" value=\"1\"></el-option>\n                        <el-option label=\"否\" value=\"0\"></el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"是否需要双边偏好\" prop=\"is_bilateral_pref_consideration_needed\">\n                    <el-select v-model=\"newTaskTypeForm.is_bilateral_pref_consideration_needed\" placeholder=\"请选择\">\n                        <el-option label=\"是\" value=\"1\"></el-option>\n                        <el-option label=\"否\" value=\"0\"></el-option>\n                    </el-select>\n                </el-form-item> -->\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addTaskTypeDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewTaskType\" :loading=\"addingTaskType\">确定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\n\nexport default {\n    name: 'EditScene',\n    data() {\n        return {\n            activeStep: 0,\n            loading: false,\n            form: {\n                scene_id: null,\n                linked_project_team_name: null,\n                linked_task_type_code: null,\n                scene_name: '',\n                scene_description: '',\n                input_platforms: [],\n                output_platforms: [],\n                input_platforms_data: {},\n                output_platforms_data: {},\n                input_data_options: {},\n                output_data_options: {},\n                input_effect_params: {},\n                input_effect_params_config: {},\n                updated_prompt: '',\n                scene_running_frequency: '',\n                hour: '',\n                modality: '',\n                day: '',\n                weeks: '',\n                stored_strategy_refresh_days: 0,\n                explore_strategy_trigger_days: 365,\n                scene_business_type: '',\n                baseline_data_start_days_ago: 30,\n                baseline_data_exclude_recent_days: 3,\n                min_baseline_sample_count: 3,\n                baseline_refresh_frequency_days: 7\n            },\n            frequencyValue: 30,\n            frequencyUnit: 'minutes',\n            rules: {\n                linked_project_team_name: [\n                    { \n                        required: true, \n                        message: '请选择项目组', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            console.log('验证项目组:', value)\n                            if (!value) {\n                                callback(new Error('请选择项目组'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                linked_task_type_code: [\n                    { \n                        required: true, \n                        message: '请选择任务类型', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            console.log('验证任务类型:', value)\n                            if (!value) {\n                                callback(new Error('请选择任务类型'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                scene_name: [\n                    { required: true, message: '请输入场景名称', trigger: 'blur' }\n                ],\n                scene_description: [\n                    { required: false, message: '请输入场景描述', trigger: 'blur' }\n                ],\n                input_platforms: [\n                    { required: true, message: '请选择输入平台', trigger: 'change' }\n                ],\n                output_platforms: [\n                    { required: true, message: '请选择输出平台', trigger: 'change' }\n                ],\n                updated_prompt: [\n                    { required: true, message: '请输入AI提示词', trigger: 'blur' }\n                ],\n                scene_running_frequency: [\n                    { required: true, message: '请设置运行频率', trigger: 'change' },\n                    { \n                        type: 'number', \n                        min: 30, \n                        message: '运行频率最小间隔为30分钟', \n                        trigger: 'change' \n                    }\n                ],\n                stored_strategy_refresh_days: [\n                    { required: true, message: '请输入个性化进化策略更新频率', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-365之间的天数', trigger: 'blur' }\n                ],\n                explore_strategy_trigger_days: [\n                    { required: true, message: '请输入AI自行探索频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                scene_business_type: [\n                    { required: true, message: '请选择场景类型', trigger: 'change' }\n                ],\n                baseline_data_start_days_ago: [\n                    { required: true, message: '请输入使用数据的起始天数', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                baseline_data_exclude_recent_days: [\n                    { required: true, message: '请输入排除最近的数据天数', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-30之间的天数', trigger: 'blur' }\n                ],\n                min_baseline_sample_count: [\n                    { required: true, message: '请输入样本量最小阈值', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 10000, message: '请输入1-10000之间的数值', trigger: 'blur' }\n                ],\n                baseline_refresh_frequency_days: [\n                    { required: true, message: '请输入基准线更新频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ]\n            },\n            projectTeams: [], \n            taskTypes: [], \n            modalityOptions: [], \n            selectedInputPlatforms: [],\n            selectedOutputPlatforms: [],\n            sceneInputAccountsData: {}, \n            sceneOutputAccountsData: {}, \n            effectParamsData: {},\n            addOptionDialogVisible: false,\n            newOptionForm: {\n                option_name: '',\n                option_key: '',\n                platform_id: ''\n            },\n            optionRules: {\n                option_name: [\n                    { required: true, message: '请输入内容名称', trigger: 'blur' }\n                ],\n                option_key: [\n                    { required: true, message: '请输入内容标识（英文）', trigger: 'blur' }\n                ]\n            },\n            addingOption: false,\n            addProjectDialogVisible: false,\n            addTaskTypeDialogVisible: false,\n            newProjectForm: {\n                project_name: ''\n            },\n            newTaskTypeForm: {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            },\n            selectedPlatformIds: [], \n            selectedEffectParams: [], \n            availableEffectParamsForTaskType: [], \n            projectRules: {\n                project_name: [\n                    { required: true, message: '请输入项目名称', trigger: 'blur' }\n                ]\n            },\n            taskTypeRules: {\n                task_type_name: [\n                    { required: true, message: '请输入任务类型名称', trigger: 'blur' }\n                ],\n                task_type_description: [\n                    { required: true, message: '请输入任务类型描述', trigger: 'blur' }\n                ],\n                recommended_effect_param_codes: [\n                    { required: true, message: '请输入推荐效果参数编码', trigger: 'blur' }\n                ],\n                effect_param_relationships_note: [\n                    { required: true, message: '请输入各推荐参数之间的逻辑关系说明', trigger: 'blur' }\n                ]\n            },\n            addingProject: false,\n            addingTaskType: false,\n            sceneTypes: [], \n            loadingInputPlatforms: false, \n            loadingOutputPlatforms: false \n        }\n    },\n    computed: {\n        ...mapState(['platforms']),\n        availablePlatforms() {\n            if (!this.platforms || !this.form.linked_task_type_code) {\n                return this.platforms\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.linked_platform_ids) {\n                return this.platforms\n            }\n            \n            const linkedPlatformIds = selectedTaskType.linked_platform_ids.split(',').map(id => parseInt(id.trim()))\n            return this.platforms.filter(platform => linkedPlatformIds.includes(platform.platform_id))\n        },\n        availableEffectParams() {\n            if (!this.form.linked_task_type_code|| !this.taskTypes) {\n                return []\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.recommended_effect_param_codes) {\n                return []\n            }\n            \n            try {\n                const params = JSON.parse(selectedTaskType.recommended_effect_param_codes)\n                return Array.isArray(params) ? params : []\n            } catch (error) {\n                console.error('解析效果参数失败:', error)\n                return []\n            }\n        },\n        isPlatformConfigLoading() {\n            if (this.activeStep === 2 && this.loadingInputPlatforms) {\n                return true\n            }\n            if (this.activeStep === 3 && this.loadingOutputPlatforms) {\n                return true\n            }\n            return false\n        }\n    },\n    methods: {\n        ...mapActions(['fetchPlatforms']),\n        async fetchPlatformOptions(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/option/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform options:', error)\n                return []\n            }\n        },\n        async fetchPlatformEffectParams(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/effectParamCategory/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform effect params:', error)\n                return []\n            }\n        },\n        getAvailableEffectParamsForPlatform(platformId) {\n            const recommendedParams = this.availableEffectParams || []\n            \n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n\n            console.info(platformParams)\n\n            if (platformParams.length === 0) {\n                return []\n            }\n            \n            // const platformParamCodes = platformParams.map(param => param.effect_param_name || param.effect_param_code)\n            \n            return platformParams.filter(param => recommendedParams.includes(param.effect_param_name))\n        },\n        getFieldComponent(type) {\n            const componentMap = {\n                'string': 'el-input',\n                'password': 'el-input',\n                'select': 'el-select',\n                'multiselect': 'el-select',\n                'number': 'el-input-number',\n                'bool': 'el-switch',\n                'textarea': 'el-input'\n            }\n            return componentMap[type] || 'el-input'\n        },\n        getFieldProps(field) {\n            const props = {\n                placeholder: `请输入${field.label}`\n            }\n            if (field.field_type === 'password') {\n                props.type = 'password'\n            }\n            if (field.field_type === 'textarea') {\n                props.type = 'textarea'\n                props.rows = 3\n            }\n            if (field.field_type === 'select' || field.field_type === 'multiselect') {\n                props.multiple = field.field_type === 'multiselect'\n                props.options = field.options || []\n            }\n            return props\n        },\n        async handleInputPlatformChange(platformIds) {\n            if (this.loadingInputPlatforms) {\n                return\n            }\n            this.loadingInputPlatforms = true\n            \n            try {\n                this.selectedInputPlatforms = []\n\n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('input_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n\n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n                        \n                        const effectParams = await this.fetchPlatformEffectParams(platformId)\n\n                        console.info(\"effectParams\", effectParams);\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options,\n                            effectParams: effectParams\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedInputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n\n                        if (!this.form.input_platforms_data[platformId]) {\n                            this.$set(this.form.input_platforms_data, platformId, {\n                                additional_Information: ''\n                            })\n                        }\n\n                        if (!this.form.input_data_options[platformId]) {\n                            this.$set(this.form.input_data_options, platformId, [])\n                        }\n\n                        if (!this.form.input_effect_params[platformId]) {\n                            this.$set(this.form.input_effect_params, platformId, [])\n                        }\n\n                        const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n                        if (platformWithDetails && platformWithDetails.fields) {\n                            platformWithDetails.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `input_platforms_data.${platformId}.${field.field_name}`\n                                    this.$set(this.rules, fieldProp, [\n                                        { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                    ])\n                                }\n                            })\n                        }\n\n                        if (this.sceneInputAccountsData[platformId]) {\n                            const accountData = this.sceneInputAccountsData[platformId]\n                            const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n\n                            for (const field of (platformWithDetails && platformWithDetails.fields) || []) {\n                                if (accountData[field.field_name] !== undefined) {\n                                    this.$set(this.form.input_platforms_data[platformId], field.field_name, accountData[field.field_name])\n                                }\n                            }\n\n                            if (accountData.additional_Information !== undefined) {\n                                this.$set(this.form.input_platforms_data[platformId], 'additional_Information', accountData.additional_Information)\n                            }\n\n                            if (accountData.adding_data_types && accountData.adding_data_types !== '无') {\n                                const selectedOptions = accountData.adding_data_types.split(',').filter(item => item.trim())\n                                this.$set(this.form.input_data_options, platformId, selectedOptions)\n                            }\n                        }\n\n                        if (this.effectParamsData) {\n                            const platformEffectParams = Object.values(this.effectParamsData).filter(param => param.platform_id === platformId)\n                            \n                            if (platformEffectParams.length > 0) {\n                                if (!this.form.input_effect_params[platformId]) {\n                                    this.$set(this.form.input_effect_params, platformId, [])\n                                }\n                                \n                                if (!this.form.input_effect_params_config[platformId]) {\n                                    this.$set(this.form.input_effect_params_config, platformId, {})\n                                }\n                                \n                                const selectedParams = []\n                                platformEffectParams.forEach(param => {\n                                    selectedParams.push(param.effect_param_code)\n                                    this.$set(this.form.input_effect_params_config[platformId], param.effect_param_code, {\n                                        effect_param_code: param.effect_param_code,\n                                        effect_param_name: param.effect_param_name,\n                                        configured_evaluation_days: param.configured_evaluation_days || '',\n                                        default_baseline_mean: param.default_baseline_mean || '',\n                                        default_baseline_stddev: param.default_baseline_stddev || ''\n                                    })\n                                })\n                                \n                                this.$set(this.form.input_effect_params, platformId, selectedParams)\n                            }\n                        }\n                }\n            } catch (error) {\n                console.error('Error in handleInputPlatformChange:', error)\n                this.$message.error('处理输入平台变化时出错')\n            } finally {\n                this.loadingInputPlatforms = false\n            }\n        },\n        async handleOutputPlatformChange(platformIds) {\n            if (this.loadingOutputPlatforms) {\n                return\n            }\n            this.loadingOutputPlatforms = true\n            \n            try {\n                this.selectedOutputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('output_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedOutputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n\n                        if (!this.form.output_platforms_data[platformId]) {\n                            this.$set(this.form.output_platforms_data, platformId, {\n                                additional_Information: ''\n                            })\n                        }\n\n                        if (!this.form.output_data_options[platformId]) {\n                            this.$set(this.form.output_data_options, platformId, [])\n                        }\n\n                        const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)\n                        if (platformWithDetails && platformWithDetails.fields) {\n                            platformWithDetails.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `output_platforms_data.${platformId}.${field.field_name}`\n                                    this.$set(this.rules, fieldProp, [\n                                        { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                    ])\n                                }\n                            })\n                        }\n\n                        if (this.sceneOutputAccountsData[platformId]) {\n                            const accountData = this.sceneOutputAccountsData[platformId]\n                            const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)\n\n                            for (const field of (platformWithDetails && platformWithDetails.fields) || []) {\n                                if (accountData[field.field_name] !== undefined) {\n                                    this.$set(this.form.output_platforms_data[platformId], field.field_name, accountData[field.field_name])\n                                }\n                            }\n\n                            if (accountData.additional_Information !== undefined) {\n                                this.$set(this.form.output_platforms_data[platformId], 'additional_Information', accountData.additional_Information)\n                            }\n\n                            if (accountData.adding_data_types && accountData.adding_data_types !== '无') {\n                                const selectedOptions = accountData.adding_data_types.split(',').filter(item => item.trim())\n                                this.$set(this.form.output_data_options, platformId, selectedOptions)\n                            }\n                        }\n                }\n            } catch (error) {\n                console.error('Error in handleOutputPlatformChange:', error)\n                this.$message.error('处理输出平台变化时出错')\n            } finally {\n                this.loadingOutputPlatforms = false\n            }\n        },\n        nextStep() {\n            if (this.isPlatformConfigLoading) {\n                this.$message.warning('平台配置正在加载中，请稍候...')\n                return\n            }\n\n            let fieldsToValidate = []\n\n            switch (this.activeStep) {\n                case 0:\n                    fieldsToValidate = ['linked_project_team_name', 'linked_task_type_code', 'scene_business_type', 'scene_name', 'scene_description']\n                    break\n                case 1:\n                    fieldsToValidate = ['baseline_data_start_days_ago', 'baseline_data_exclude_recent_days', 'min_baseline_sample_count', 'baseline_refresh_frequency_days']\n                    break\n                case 2:\n                    fieldsToValidate = ['input_platforms']\n                    \n                    this.selectedInputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `input_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n                        \n                        const selectedParams = this.form.input_effect_params[platform.platform_id] || []\n                        selectedParams.forEach(paramCode => {\n                            const configPrefix = `input_effect_params_config.${platform.platform_id}.${paramCode}`\n                            fieldsToValidate.push(`${configPrefix}.configured_evaluation_days`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_mean`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_stddev`)\n                        })\n                    })\n                    break\n                case 3:\n                    fieldsToValidate = ['output_platforms']\n                    \n                    this.selectedOutputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `output_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n                    })\n                    break\n                case 4:\n                    fieldsToValidate = ['updated_prompt', 'scene_running_frequency', 'stored_strategy_refresh_days', 'explore_strategy_trigger_days']\n                    break\n                default:\n                    break\n            }\n\n            this.validateFields(fieldsToValidate, (valid) => {\n                if (valid) {\n                    this.activeStep++\n                }\n            })\n        },\n        validateFields(fields, callback) {\n            if (fields.length === 0) {\n                callback(true)\n                return\n            }\n\n            const validationPromises = fields.map(field => {\n                return new Promise((resolve) => {\n                    this.$refs.form.validateField(field, (errorMessage) => {\n                        console.log(`字段 ${field} 验证结果:`, errorMessage)\n                        resolve({ field, errorMessage })\n                    })\n                })\n            })\n\n            Promise.all(validationPromises).then(results => {\n                const hasError = results.some(result => result.errorMessage)\n                console.log('验证结果:', results, '是否有错误:', hasError)\n                callback(!hasError)\n            })\n        },\n        prevStep() {\n            this.activeStep--\n        },\n        async submitForm() {\n            this.$refs.form.validate(async valid => {\n                if (valid) {\n                    try {\n                        const accounts = []\n                        const effectParams = []\n\n                        for (const platformId of this.form.input_platforms) {\n                            const platformData = this.form.input_platforms_data[platformId] || {}\n                            const dataOptions = this.form.input_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 1,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n\n                            if (this.form.input_effect_params_config[platformId]) {\n                                Object.values(this.form.input_effect_params_config[platformId]).forEach(paramConfig => {\n                                    effectParams.push({\n                                        platform_id: platformId,\n                                        ...paramConfig\n                                    })\n                                })\n                            }\n                        }\n\n                        for (const platformId of this.form.output_platforms) {\n                            const platformData = this.form.output_platforms_data[platformId] || {}\n                            const dataOptions = this.form.output_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 2,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n                        }\n\n                        const { input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, ...submitData } = this.form;\n                        console.info(input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config)\n\n                        submitData.update_time = new Date().toLocaleString('sv-SE').replace('T', ' ');\n\n                        await this.$http.post('', {\n                            api: '/api/scene/update',\n                            data: {\n                                ...submitData,\n                                scene_id: this.$route.params.id\n                            },\n                            accounts: accounts,\n                            effect_params: effectParams\n                        })\n                        this.$message.success('场景更新成功')\n                        this.$router.push('/')\n                    } catch (error) {\n                        this.$message.error('场景更新失败')\n                        console.error('Error updating scene:', error)\n                    }\n                }\n            })\n        },\n        async fetchSceneDetail() {\n            if (!this.$route.params.id) {\n                return\n            }\n\n            this.loading = true\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/scene/getDetail',\n                    param: { scene_id: this.$route.params.id }\n                })\n                const sceneData = response.data.data\n                const accounts = response.data.accounts || []\n                const effectParams = response.data.effect_params || []\n\n                this.sceneInputAccountsData = {}\n                this.sceneOutputAccountsData = {}\n                accounts.forEach(account => {\n                    if (account.operate_type === 1) {\n                        this.sceneInputAccountsData[account.platform_id] = account\n                    } else if (account.operate_type === 2) {\n                        this.sceneOutputAccountsData[account.platform_id] = account\n                    }\n                })\n                const inputPlatforms = accounts\n                    .filter(platform => platform.operate_type === 1)\n                    .map(platform => platform.platform_id)\n\n                const outputPlatforms = accounts\n                    .filter(platform => platform.operate_type === 2)\n                    .map(platform => platform.platform_id)\n\n                this.effectParamsData = {}\n                effectParams.forEach(param => {\n                    this.effectParamsData[param.effect_param_code] = param\n                })\n\n                console.info(\"effectParamsData\", this.effectParamsData);\n\n                this.form = {\n                    ...this.form,\n                    ...sceneData,\n                    linked_task_type_code: parseInt(sceneData.linked_task_type_code),\n                    input_platforms: inputPlatforms,\n                    output_platforms: outputPlatforms\n                }\n\n                if (sceneData.scene_running_frequency) {\n                    this.parseFrequencyFromMinutes(parseInt(sceneData.scene_running_frequency))\n                } else {\n                    this.frequencyValue = 30\n                    this.frequencyUnit = 'minutes'\n                }\n                this.calculateTotalMinutes()\n\n                await this.$nextTick()\n                await this.handleInputPlatformChange(this.form.input_platforms)\n                await this.handleOutputPlatformChange(this.form.output_platforms)\n\n            } catch (error) {\n                this.$message.error('获取场景详情失败')\n                console.error('Error fetching scene detail:', error)\n            } finally {\n                this.loading = false\n            }\n        },\n        showAddOptionDialog(platformId) {\n            this.newOptionForm = {\n                option_name: '',\n                option_key: '',\n                platform_id: platformId\n            }\n            this.addOptionDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.optionForm && this.$refs.optionForm.clearValidate()\n            })\n        },\n        async addNewOption() {\n            this.$refs.optionForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingOption = true\n                        await this.$http.post('', {\n                            api: '/api/option/add',\n                            data: {\n                                ...this.newOptionForm,\n                                platform_id: this.newOptionForm.platform_id\n                            }\n                        })\n                        this.$message.success('新增数据类型成功')\n                        this.addOptionDialogVisible = false\n                        this.newOptionForm = {\n                            option_name: '',\n                            option_key: '',\n                            platform_id: ''\n                        }\n                        await this.handleInputPlatformChange(this.form.input_platforms)\n                        await this.handleOutputPlatformChange(this.form.output_platforms)\n                    } catch (error) {\n                        this.$message.error('新增数据类型失败')\n                        console.error('Error adding new option:', error)\n                    } finally {\n                        this.addingOption = false\n                    }\n                }\n            })\n        },\n        openAIOptimize() {\n            if (!this.form.scene_id) return\n            const url = `https://acpfbbeg.manus.space/?scene_id=${this.form.scene_id}`\n            window.open(url, '_blank')\n        },\n        async handleDeleteScene() {\n            if (!this.form.scene_id) return\n            try {\n                await this.$confirm('确认删除该场景吗？', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                })\n                await this.$http.post('', {\n                    api: '/api/scene/delete',\n                    param: { scene_id: this.form.scene_id }\n                })\n                this.$message.success('删除成功')\n                this.$router.push('/scene/manage')\n            } catch (error) {\n                if (error !== 'cancel') {\n                    this.$message.error('删除失败')\n                    console.error('Error deleting scene:', error)\n                }\n            }\n        },\n        async fetchModalityOptions() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'modality'\n                    }\n                })\n                this.modalityOptions = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching modality options:', error)\n                this.$message.error('获取模态列表失败')\n            }\n        },\n        async fetchProjectTeams() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/projectTeam/getList'\n                })\n                this.projectTeams = response.data.data || []\n                console.log('项目组数据:', this.projectTeams)\n            } catch (error) {\n                console.error('Error fetching project teams:', error)\n                this.$message.error('获取项目组列表失败')\n            }\n        },\n        async fetchTaskTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/taskType/getList'\n                })\n                this.taskTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching task types:', error)\n                this.$message.error('获取任务类型列表失败')\n            }\n        },\n        async fetchSceneTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/sceneType/getList'\n                })\n                this.sceneTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching scene types:', error)\n                this.$message.error('获取场景类型列表失败')\n            }\n        },\n        handleProjectTeamChange() {\n            console.log('项目组改变:', this.form.linked_project_team_name)\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_project_team_name')\n            })\n        },\n        handleTaskTypeChange() {\n            this.form.input_platforms = []\n            this.form.output_platforms = []\n            this.selectedInputPlatforms = []\n            this.selectedOutputPlatforms = []\n            this.form.input_platforms_data = {}\n            this.form.output_platforms_data = {}\n            this.form.input_data_options = {}\n            this.form.output_data_options = {}\n            this.form.input_effect_params = {}\n            this.form.input_effect_params_config = {}\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_task_type_code')\n            })\n        },\n        handleEffectParamsChange(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n\n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n\n            if (platformParams.length == 0) {\n                return;\n            }\n\n            Object.keys(this.rules).forEach(key => {\n                if (key.startsWith(`input_effect_params_config.${platformId}.`)) {\n                    this.$delete(this.rules, key)\n                }\n            })\n\n            selectedParams.forEach(param => {\n                if (!this.form.input_effect_params_config[platformId][param]) {\n\n                    const effectParam = platformParams.find(p => p.effect_param_code === param);\n\n                    this.$set(this.form.input_effect_params_config[platformId], param, {\n                        effect_param_code: effectParam.effect_param_code,\n                        effect_param_name: effectParam.effect_param_name,\n                        configured_evaluation_days: '',\n                        default_baseline_mean: '',\n                        default_baseline_stddev: ''\n                    })\n                }\n\n                const configPrefix = `input_effect_params_config.${platformId}.${param}`\n                this.$set(this.rules, `${configPrefix}.configured_evaluation_days`, [\n                    { required: true, message: '请输入效果实现天数', trigger: 'blur' },\n                    { pattern: /^[\\d,\\s]+$/, message: '请输入有效的天数格式，如：3,5,10', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_mean`, [\n                    { required: true, message: '请输入平均值', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '平均值必须是数字', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_stddev`, [\n                    { required: true, message: '请输入标准差', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '标准差必须是数字', trigger: 'blur' }\n                ])\n            })\n            \n            Object.keys(this.form.input_effect_params_config[platformId]).forEach(param => {\n                if (!selectedParams.includes(param)) {\n                    this.$delete(this.form.input_effect_params_config[platformId], param)\n                }\n            })\n        },\n        getEffectParamsTableData(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n            const config = this.form.input_effect_params_config[platformId] || {}\n            \n            const ret = selectedParams.map(paramCode => ({\n                effect_param_code: paramCode,\n                effect_param_name: (config[paramCode] && config[paramCode].effect_param_name) || '',\n                configured_evaluation_days: (config[paramCode] && config[paramCode].configured_evaluation_days) || '',\n                default_baseline_mean: (config[paramCode] && config[paramCode].default_baseline_mean) || 0,\n                default_baseline_stddev: (config[paramCode] && config[paramCode].default_baseline_stddev) || 1\n            }))\n\n            console.info(\"ret\", ret);\n            return ret;\n        },\n        updateEffectParamConfig(platformId, paramName, field, value) {\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n            if (!this.form.input_effect_params_config[platformId][paramName]) {\n                this.$set(this.form.input_effect_params_config[platformId], paramName, {})\n            }\n            this.$set(this.form.input_effect_params_config[platformId][paramName], field, value)\n        },\n        getMinValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30 \n                case 'hours':\n                    return 1 \n                case 'days':\n                    return 1 \n                default:\n                    return 1\n            }\n        },\n        getMaxValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 1440\n                case 'hours':\n                    return 24 \n                case 'days':\n                    return 365 \n                default:\n                    return 1\n            }\n        },\n        getStep() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30 \n                case 'hours':\n                    return 1 \n                case 'days':\n                    return 1 \n                default:\n                    return 1\n            }\n        },\n        calculateTotalMinutes() {\n            let totalMinutes = 0\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    totalMinutes = this.frequencyValue\n                    break\n                case 'hours':\n                    totalMinutes = this.frequencyValue * 60\n                    break\n                case 'days':\n                    totalMinutes = this.frequencyValue * 24 * 60\n                    break\n            }\n            \n            if (totalMinutes < 30) {\n                totalMinutes = 30\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n            }\n            \n            this.form.scene_running_frequency = totalMinutes\n            return totalMinutes\n        },\n        parseFrequencyFromMinutes(minutes) {\n            if (!minutes || minutes < 30) {\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n                return\n            }\n            \n            if (minutes >= 1440 && minutes % 1440 === 0) {\n                this.frequencyValue = minutes / 1440\n                this.frequencyUnit = 'days'\n            } else if (minutes >= 60 && minutes % 60 === 0) {\n                this.frequencyValue = minutes / 60\n                this.frequencyUnit = 'hours'\n            } else {\n                this.frequencyValue = minutes\n                this.frequencyUnit = 'minutes'\n            }\n        },\n        handleFrequencyValueChange() {\n            this.calculateTotalMinutes()\n        },\n        handleFrequencyUnitChange() {\n            const currentMinutes = this.calculateTotalMinutes()\n            this.parseFrequencyFromMinutes(currentMinutes)\n            this.calculateTotalMinutes()\n        },\n        showAddProjectDialog() {\n            this.newProjectForm = {\n                project_name: ''\n            }\n            this.addProjectDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.projectForm && this.$refs.projectForm.clearValidate()\n            })\n        },\n        async addNewProject() {\n            this.$refs.projectForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingProject = true\n                        await this.$http.post('', {\n                            api: '/api/projectTeam/add',\n                            data: this.newProjectForm\n                        })\n                        this.$message.success('新增项目组成功')\n                        this.addProjectDialogVisible = false\n                        await this.fetchProjectTeams()\n                        if (this.projectTeams.length > 0) {\n                            const newProject = this.projectTeams.find(project => project.project_name === this.newProjectForm.project_name)\n                            if (newProject) {\n                                this.form.linked_project_team_name = newProject.project_name\n                            }\n                        }\n                    } catch (error) {\n                        this.$message.error('新增项目组失败')\n                        console.error('Error adding new project:', error)\n                    } finally {\n                        this.addingProject = false\n                    }\n                }\n            })\n        },\n        showAddTaskTypeDialog() {\n            this.newTaskTypeForm = {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            }\n            this.selectedPlatformIds = [] \n            this.selectedEffectParams = [] \n            this.availableEffectParamsForTaskType = []\n            this.addTaskTypeDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.taskTypeForm && this.$refs.taskTypeForm.clearValidate()\n            })\n        },\n        async handlePlatformSelectChange(selectedIds) {\n            this.newTaskTypeForm.linked_platform_ids = selectedIds.join(',')\n            \n            await this.fetchEffectParamsForTaskType(selectedIds)\n        },\n        async fetchEffectParamsForTaskType(platformIds) {\n            if (platformIds.length === 0) {\n                this.availableEffectParamsForTaskType = []\n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                return\n            }\n            \n            try {\n                const platformParamsArrays = []\n                for (const platformId of platformIds) {\n                    const params = await this.fetchPlatformEffectParams(platformId)\n                    platformParamsArrays.push(params)\n                }\n                \n                const allParams = []\n                const seenCodes = new Set()\n                \n                platformParamsArrays.forEach(platformParams => {\n                    platformParams.forEach(param => {\n                        if (!seenCodes.has(param.effect_param_code)) {\n                            seenCodes.add(param.effect_param_code)\n                            allParams.push(param)\n                        }\n                    })\n                })\n                \n                this.availableEffectParamsForTaskType = allParams\n                \n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                \n            } catch (error) {\n                console.error('Error fetching effect params for task type:', error)\n                this.$message.error('获取效果参数失败')\n                this.availableEffectParamsForTaskType = []\n            }\n        },\n        handleEffectParamsSelectChange(selectedCodes) {\n            this.newTaskTypeForm.recommended_effect_param_codes = JSON.stringify(selectedCodes)\n        },\n        async addNewTaskType() {\n            this.$refs.taskTypeForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingTaskType = true\n                        await this.$http.post('', {\n                            api: '/api/taskType/add',\n                            data: this.newTaskTypeForm\n                        })\n                        this.$message.success('新增任务类型成功')\n                        this.addTaskTypeDialogVisible = false\n                        await this.fetchTaskTypes()\n                    } catch (error) {\n                        this.$message.error('新增任务类型失败')\n                        console.error('Error adding new task type:', error)\n                    } finally {\n                        this.addingTaskType = false\n                    }\n                }\n            })\n        }\n    },\n    async created() {\n        await this.fetchPlatforms({ page: 1, pageSize: 100 })\n        await this.fetchProjectTeams()\n        await this.fetchTaskTypes()\n        await this.fetchSceneTypes() \n        await this.fetchSceneDetail()\n        await this.fetchModalityOptions()\n    }\n}\n</script>\n\n<style scoped>\n.edit-scene {\n    padding: 20px;\n}\n\n.mt-20 {\n    margin-top: 20px;\n}\n\n.el-steps {\n    margin-bottom: 30px;\n}\n\n.platform-configs {\n    margin-top: 20px;\n    margin-bottom: 20px;\n}\n\n.platform-configs h3 {\n    margin-bottom: 16px;\n    color: #303133;\n    font-size: 16px;\n}\n\n.platform-card {\n    margin-bottom: 16px;\n}\n\n.platform-card:last-child {\n    margin-bottom: 0;\n}\n\n.data-options-container {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n}\n\n.checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.platform-selection-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.edit-breadcrumb {\n    font-size: 14px;\n    margin-bottom: 18px;\n}\n\n.edit-actions {\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n    margin-bottom: 18px;\n}\n\n.navigation-buttons {\n    margin-top: 30px;\n}\n\n.platform-selection-tip {\n    margin-bottom: 16px;\n}\n\n.no-platforms-tip {\n    margin-top: 16px;\n}\n\n.loading-tip {\n    margin-top: 16px;\n}\n\n.effect-params-container {\n    margin-top: 16px;\n}\n\n.effect-param-checkbox {\n    margin-right: 16px;\n    margin-bottom: 8px;\n}\n\n.effect-params-table {\n    margin-top: 16px;\n}\n\n.effect-params-table h4 {\n    margin-bottom: 12px;\n    color: #303133;\n    font-size: 14px;\n}\n\n\n\n.table-header-with-tooltip {\n    cursor: help;\n    display: flex;\n    align-items: center;\n    gap: 4px;\n}\n\n.table-header-with-tooltip .el-icon-question {\n    color: #609399;\n    font-size: 14px;\n}\n</style>"], "mappings": "AAomBA,SAAAA,QAAA,EAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,UAAA;MACAC,OAAA;MACAC,IAAA;QACAC,QAAA;QACAC,wBAAA;QACAC,qBAAA;QACAC,UAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,0BAAA;QACAC,cAAA;QACAC,uBAAA;QACAC,IAAA;QACAC,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,4BAAA;QACAC,6BAAA;QACAC,mBAAA;QACAC,4BAAA;QACAC,iCAAA;QACAC,yBAAA;QACAC,+BAAA;MACA;MACAC,cAAA;MACAC,aAAA;MACAC,KAAA;QACA3B,wBAAA,GACA;UACA4B,QAAA;UACAC,OAAA;UACAC,OAAA;UACAC,SAAA,EAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACAC,OAAA,CAAAC,GAAA,WAAAH,KAAA;YACA,KAAAA,KAAA;cACAC,QAAA,KAAAG,KAAA;YACA;cACAH,QAAA;YACA;UACA;QACA,EACA;QACAjC,qBAAA,GACA;UACA2B,QAAA;UACAC,OAAA;UACAC,OAAA;UACAC,SAAA,EAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACAC,OAAA,CAAAC,GAAA,YAAAH,KAAA;YACA,KAAAA,KAAA;cACAC,QAAA,KAAAG,KAAA;YACA;cACAH,QAAA;YACA;UACA;QACA,EACA;QACAhC,UAAA,GACA;UAAA0B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA3B,iBAAA,GACA;UAAAyB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA1B,eAAA,GACA;UAAAwB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAzB,gBAAA,GACA;UAAAuB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,cAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjB,uBAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAQ,IAAA;UACAC,GAAA;UACAV,OAAA;UACAC,OAAA;QACA,EACA;QACAZ,4BAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAX,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,6BAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAX,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,mBAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,4BAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAX,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,iCAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAX,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,yBAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAX,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,+BAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAX,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAW,YAAA;MACAC,SAAA;MACAC,eAAA;MACAC,sBAAA;MACAC,uBAAA;MACAC,sBAAA;MACAC,uBAAA;MACAC,gBAAA;MACAC,sBAAA;MACAC,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,WAAA;MACA;MACAC,WAAA;QACAH,WAAA,GACA;UAAAvB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAsB,UAAA,GACA;UAAAxB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAyB,YAAA;MACAC,uBAAA;MACAC,wBAAA;MACAC,cAAA;QACAC,YAAA;MACA;MACAC,eAAA;QACAC,cAAA;QACAC,qBAAA;QACAC,8BAAA;QACAC,+BAAA;QACAC,wBAAA;QACAC,sCAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,eAAA;MACA;MACAC,mBAAA;MACAC,oBAAA;MACAC,gCAAA;MACAC,YAAA;QACAd,YAAA,GACA;UAAA/B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA4C,aAAA;QACAb,cAAA,GACA;UAAAjC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAgC,qBAAA,GACA;UAAAlC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAiC,8BAAA,GACA;UAAAnC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAkC,+BAAA,GACA;UAAApC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA6C,aAAA;MACAC,cAAA;MACAC,UAAA;MACAC,qBAAA;MACAC,sBAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAxF,QAAA;IACAyF,mBAAA;MACA,UAAAC,SAAA,UAAApF,IAAA,CAAAG,qBAAA;QACA,YAAAiF,SAAA;MACA;MAEA,MAAAC,gBAAA,QAAAzC,SAAA,CAAA0C,IAAA,CAAAC,QAAA,IAAAA,QAAA,CAAAC,cAAA,UAAAxF,IAAA,CAAAG,qBAAA;MACA,KAAAkF,gBAAA,KAAAA,gBAAA,CAAAhB,mBAAA;QACA,YAAAe,SAAA;MACA;MAEA,MAAAK,iBAAA,GAAAJ,gBAAA,CAAAhB,mBAAA,CAAAqB,KAAA,MAAAC,GAAA,CAAAC,EAAA,IAAAC,QAAA,CAAAD,EAAA,CAAAE,IAAA;MACA,YAAAV,SAAA,CAAAW,MAAA,CAAAC,QAAA,IAAAP,iBAAA,CAAAQ,QAAA,CAAAD,QAAA,CAAAzC,WAAA;IACA;IACA2C,sBAAA;MACA,UAAAlG,IAAA,CAAAG,qBAAA,UAAAyC,SAAA;QACA;MACA;MAEA,MAAAyC,gBAAA,QAAAzC,SAAA,CAAA0C,IAAA,CAAAC,QAAA,IAAAA,QAAA,CAAAC,cAAA,UAAAxF,IAAA,CAAAG,qBAAA;MACA,KAAAkF,gBAAA,KAAAA,gBAAA,CAAApB,8BAAA;QACA;MACA;MAEA;QACA,MAAAkC,MAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAhB,gBAAA,CAAApB,8BAAA;QACA,OAAAqC,KAAA,CAAAC,OAAA,CAAAJ,MAAA,IAAAA,MAAA;MACA,SAAAK,KAAA;QACAnE,OAAA,CAAAmE,KAAA,cAAAA,KAAA;QACA;MACA;IACA;IACAC,wBAAA;MACA,SAAA3G,UAAA,eAAAkF,qBAAA;QACA;MACA;MACA,SAAAlF,UAAA,eAAAmF,sBAAA;QACA;MACA;MACA;IACA;EACA;EACAyB,OAAA;IACA,GAAA/G,UAAA;IACA,MAAAgH,qBAAAC,UAAA;MACA;QACA,MAAAC,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACA1D,WAAA,EAAAqD;UACA;QACA;QACA,OAAAC,QAAA,CAAAhH,IAAA,CAAAA,IAAA;MACA,SAAA2G,KAAA;QACAnE,OAAA,CAAAmE,KAAA,qCAAAA,KAAA;QACA;MACA;IACA;IACA,MAAAU,0BAAAN,UAAA;MACA;QACA,MAAAC,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACA1D,WAAA,EAAAqD;UACA;QACA;QACA,OAAAC,QAAA,CAAAhH,IAAA,CAAAA,IAAA;MACA,SAAA2G,KAAA;QACAnE,OAAA,CAAAmE,KAAA,2CAAAA,KAAA;QACA;MACA;IACA;IACAW,oCAAAP,UAAA;MACA,MAAAQ,iBAAA,QAAAlB,qBAAA;MAEA,MAAAF,QAAA,QAAAlD,sBAAA,CAAAwC,IAAA,CAAA+B,CAAA,IAAAA,CAAA,CAAA9D,WAAA,KAAAqD,UAAA;MACA,MAAAU,cAAA,GAAAtB,QAAA,GAAAA,QAAA,CAAAuB,YAAA;MAEAlF,OAAA,CAAAmF,IAAA,CAAAF,cAAA;MAEA,IAAAA,cAAA,CAAAG,MAAA;QACA;MACA;;MAEA;;MAEA,OAAAH,cAAA,CAAAvB,MAAA,CAAAkB,KAAA,IAAAG,iBAAA,CAAAnB,QAAA,CAAAgB,KAAA,CAAAS,iBAAA;IACA;IACAC,kBAAAnF,IAAA;MACA,MAAAoF,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,YAAA,CAAApF,IAAA;IACA;IACAqF,cAAAC,KAAA;MACA,MAAAC,KAAA;QACAC,WAAA,QAAAF,KAAA,CAAAG,KAAA;MACA;MACA,IAAAH,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAvF,IAAA;MACA;MACA,IAAAsF,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAvF,IAAA;QACAuF,KAAA,CAAAI,IAAA;MACA;MACA,IAAAL,KAAA,CAAAI,UAAA,iBAAAJ,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAK,QAAA,GAAAN,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAM,OAAA,GAAAP,KAAA,CAAAO,OAAA;MACA;MACA,OAAAN,KAAA;IACA;IACA,MAAAO,0BAAAC,WAAA;MACA,SAAAvD,qBAAA;QACA;MACA;MACA,KAAAA,qBAAA;MAEA;QACA,KAAAlC,sBAAA;QAEA0F,MAAA,CAAAC,IAAA,MAAA5G,KAAA,EAAA6G,OAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,UAAA,8BAAAD,GAAA,CAAAE,QAAA;YACA,KAAAC,OAAA,MAAAjH,KAAA,EAAA8G,GAAA;UACA;QACA;QAEA,MAAAI,oBAAA;QACA,WAAAnC,UAAA,IAAA2B,WAAA;UACA,MAAAvC,QAAA,QAAAZ,SAAA,CAAAE,IAAA,CAAA+B,CAAA,IAAAA,CAAA,CAAA9D,WAAA,KAAAqD,UAAA;UACA,KAAAZ,QAAA;UAEA;YACA,MAAAgD,cAAA,cAAAlC,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAC,KAAA;gBAAA1D,WAAA,EAAAqD;cAAA;YACA;YAEA,MAAAyB,OAAA,cAAA1B,oBAAA,CAAAC,UAAA;YAEA,MAAAW,YAAA,cAAAL,yBAAA,CAAAN,UAAA;YAEAvE,OAAA,CAAAmF,IAAA,iBAAAD,YAAA;YAEAwB,oBAAA,CAAAE,IAAA;cACA,GAAAD,cAAA,CAAAnJ,IAAA,CAAAA,IAAA;cACAwI,OAAA,EAAAA,OAAA;cACAd,YAAA,EAAAA;YACA;UACA,SAAAf,KAAA;YACAnE,OAAA,CAAAmE,KAAA,oCAAAA,KAAA;YACA,KAAA0C,QAAA,CAAA1C,KAAA,QAAAR,QAAA,CAAAmD,aAAA;UACA;QACA;QAEA,KAAArG,sBAAA,GAAAiG,oBAAA;QAEA,WAAAnC,UAAA,IAAA2B,WAAA;UAEA,UAAAvI,IAAA,CAAAQ,oBAAA,CAAAoG,UAAA;YACA,KAAAwC,IAAA,MAAApJ,IAAA,CAAAQ,oBAAA,EAAAoG,UAAA;cACAyC,sBAAA;YACA;UACA;UAEA,UAAArJ,IAAA,CAAAU,kBAAA,CAAAkG,UAAA;YACA,KAAAwC,IAAA,MAAApJ,IAAA,CAAAU,kBAAA,EAAAkG,UAAA;UACA;UAEA,UAAA5G,IAAA,CAAAY,mBAAA,CAAAgG,UAAA;YACA,KAAAwC,IAAA,MAAApJ,IAAA,CAAAY,mBAAA,EAAAgG,UAAA;UACA;UAEA,MAAA0C,mBAAA,QAAAxG,sBAAA,CAAAwC,IAAA,CAAA+B,CAAA,IAAAA,CAAA,CAAA9D,WAAA,KAAAqD,UAAA;UACA,IAAA0C,mBAAA,IAAAA,mBAAA,CAAAC,MAAA;YACAD,mBAAA,CAAAC,MAAA,CAAAb,OAAA,CAAAZ,KAAA;cACA,IAAAA,KAAA,CAAAhG,QAAA;gBACA,MAAA0H,SAAA,2BAAA5C,UAAA,IAAAkB,KAAA,CAAA2B,UAAA;gBACA,KAAAL,IAAA,MAAAvH,KAAA,EAAA2H,SAAA,GACA;kBAAA1H,QAAA;kBAAAC,OAAA,QAAA+F,KAAA,CAAAG,KAAA;kBAAAjG,OAAA;gBAAA,EACA;cACA;YACA;UACA;UAEA,SAAAgB,sBAAA,CAAA4D,UAAA;YACA,MAAA8C,WAAA,QAAA1G,sBAAA,CAAA4D,UAAA;YACA,MAAA0C,mBAAA,QAAAxG,sBAAA,CAAAwC,IAAA,CAAA+B,CAAA,IAAAA,CAAA,CAAA9D,WAAA,KAAAqD,UAAA;YAEA,WAAAkB,KAAA,IAAAwB,mBAAA,IAAAA,mBAAA,CAAAC,MAAA;cACA,IAAAG,WAAA,CAAA5B,KAAA,CAAA2B,UAAA,MAAAE,SAAA;gBACA,KAAAP,IAAA,MAAApJ,IAAA,CAAAQ,oBAAA,CAAAoG,UAAA,GAAAkB,KAAA,CAAA2B,UAAA,EAAAC,WAAA,CAAA5B,KAAA,CAAA2B,UAAA;cACA;YACA;YAEA,IAAAC,WAAA,CAAAL,sBAAA,KAAAM,SAAA;cACA,KAAAP,IAAA,MAAApJ,IAAA,CAAAQ,oBAAA,CAAAoG,UAAA,6BAAA8C,WAAA,CAAAL,sBAAA;YACA;YAEA,IAAAK,WAAA,CAAAE,iBAAA,IAAAF,WAAA,CAAAE,iBAAA;cACA,MAAAC,eAAA,GAAAH,WAAA,CAAAE,iBAAA,CAAAlE,KAAA,MAAAK,MAAA,CAAA+D,IAAA,IAAAA,IAAA,CAAAhE,IAAA;cACA,KAAAsD,IAAA,MAAApJ,IAAA,CAAAU,kBAAA,EAAAkG,UAAA,EAAAiD,eAAA;YACA;UACA;UAEA,SAAA3G,gBAAA;YACA,MAAA6G,oBAAA,GAAAvB,MAAA,CAAAwB,MAAA,MAAA9G,gBAAA,EAAA6C,MAAA,CAAAkB,KAAA,IAAAA,KAAA,CAAA1D,WAAA,KAAAqD,UAAA;YAEA,IAAAmD,oBAAA,CAAAtC,MAAA;cACA,UAAAzH,IAAA,CAAAY,mBAAA,CAAAgG,UAAA;gBACA,KAAAwC,IAAA,MAAApJ,IAAA,CAAAY,mBAAA,EAAAgG,UAAA;cACA;cAEA,UAAA5G,IAAA,CAAAa,0BAAA,CAAA+F,UAAA;gBACA,KAAAwC,IAAA,MAAApJ,IAAA,CAAAa,0BAAA,EAAA+F,UAAA;cACA;cAEA,MAAAqD,cAAA;cACAF,oBAAA,CAAArB,OAAA,CAAAzB,KAAA;gBACAgD,cAAA,CAAAhB,IAAA,CAAAhC,KAAA,CAAAiD,iBAAA;gBACA,KAAAd,IAAA,MAAApJ,IAAA,CAAAa,0BAAA,CAAA+F,UAAA,GAAAK,KAAA,CAAAiD,iBAAA;kBACAA,iBAAA,EAAAjD,KAAA,CAAAiD,iBAAA;kBACAxC,iBAAA,EAAAT,KAAA,CAAAS,iBAAA;kBACAyC,0BAAA,EAAAlD,KAAA,CAAAkD,0BAAA;kBACAC,qBAAA,EAAAnD,KAAA,CAAAmD,qBAAA;kBACAC,uBAAA,EAAApD,KAAA,CAAAoD,uBAAA;gBACA;cACA;cAEA,KAAAjB,IAAA,MAAApJ,IAAA,CAAAY,mBAAA,EAAAgG,UAAA,EAAAqD,cAAA;YACA;UACA;QACA;MACA,SAAAzD,KAAA;QACAnE,OAAA,CAAAmE,KAAA,wCAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;MACA;QACA,KAAAxB,qBAAA;MACA;IACA;IACA,MAAAsF,2BAAA/B,WAAA;MACA,SAAAtD,sBAAA;QACA;MACA;MACA,KAAAA,sBAAA;MAEA;QACA,KAAAlC,uBAAA;QAEAyF,MAAA,CAAAC,IAAA,MAAA5G,KAAA,EAAA6G,OAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,UAAA,+BAAAD,GAAA,CAAAE,QAAA;YACA,KAAAC,OAAA,MAAAjH,KAAA,EAAA8G,GAAA;UACA;QACA;QAEA,MAAAI,oBAAA;QACA,WAAAnC,UAAA,IAAA2B,WAAA;UACA,MAAAvC,QAAA,QAAAZ,SAAA,CAAAE,IAAA,CAAA+B,CAAA,IAAAA,CAAA,CAAA9D,WAAA,KAAAqD,UAAA;UACA,KAAAZ,QAAA;UAEA;YACA,MAAAgD,cAAA,cAAAlC,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAC,KAAA;gBAAA1D,WAAA,EAAAqD;cAAA;YACA;YAEA,MAAAyB,OAAA,cAAA1B,oBAAA,CAAAC,UAAA;YAEAmC,oBAAA,CAAAE,IAAA;cACA,GAAAD,cAAA,CAAAnJ,IAAA,CAAAA,IAAA;cACAwI,OAAA,EAAAA;YACA;UACA,SAAA7B,KAAA;YACAnE,OAAA,CAAAmE,KAAA,oCAAAA,KAAA;YACA,KAAA0C,QAAA,CAAA1C,KAAA,QAAAR,QAAA,CAAAmD,aAAA;UACA;QACA;QAEA,KAAApG,uBAAA,GAAAgG,oBAAA;QAEA,WAAAnC,UAAA,IAAA2B,WAAA;UAEA,UAAAvI,IAAA,CAAAS,qBAAA,CAAAmG,UAAA;YACA,KAAAwC,IAAA,MAAApJ,IAAA,CAAAS,qBAAA,EAAAmG,UAAA;cACAyC,sBAAA;YACA;UACA;UAEA,UAAArJ,IAAA,CAAAW,mBAAA,CAAAiG,UAAA;YACA,KAAAwC,IAAA,MAAApJ,IAAA,CAAAW,mBAAA,EAAAiG,UAAA;UACA;UAEA,MAAA0C,mBAAA,QAAAvG,uBAAA,CAAAuC,IAAA,CAAA+B,CAAA,IAAAA,CAAA,CAAA9D,WAAA,KAAAqD,UAAA;UACA,IAAA0C,mBAAA,IAAAA,mBAAA,CAAAC,MAAA;YACAD,mBAAA,CAAAC,MAAA,CAAAb,OAAA,CAAAZ,KAAA;cACA,IAAAA,KAAA,CAAAhG,QAAA;gBACA,MAAA0H,SAAA,4BAAA5C,UAAA,IAAAkB,KAAA,CAAA2B,UAAA;gBACA,KAAAL,IAAA,MAAAvH,KAAA,EAAA2H,SAAA,GACA;kBAAA1H,QAAA;kBAAAC,OAAA,QAAA+F,KAAA,CAAAG,KAAA;kBAAAjG,OAAA;gBAAA,EACA;cACA;YACA;UACA;UAEA,SAAAiB,uBAAA,CAAA2D,UAAA;YACA,MAAA8C,WAAA,QAAAzG,uBAAA,CAAA2D,UAAA;YACA,MAAA0C,mBAAA,QAAAvG,uBAAA,CAAAuC,IAAA,CAAA+B,CAAA,IAAAA,CAAA,CAAA9D,WAAA,KAAAqD,UAAA;YAEA,WAAAkB,KAAA,IAAAwB,mBAAA,IAAAA,mBAAA,CAAAC,MAAA;cACA,IAAAG,WAAA,CAAA5B,KAAA,CAAA2B,UAAA,MAAAE,SAAA;gBACA,KAAAP,IAAA,MAAApJ,IAAA,CAAAS,qBAAA,CAAAmG,UAAA,GAAAkB,KAAA,CAAA2B,UAAA,EAAAC,WAAA,CAAA5B,KAAA,CAAA2B,UAAA;cACA;YACA;YAEA,IAAAC,WAAA,CAAAL,sBAAA,KAAAM,SAAA;cACA,KAAAP,IAAA,MAAApJ,IAAA,CAAAS,qBAAA,CAAAmG,UAAA,6BAAA8C,WAAA,CAAAL,sBAAA;YACA;YAEA,IAAAK,WAAA,CAAAE,iBAAA,IAAAF,WAAA,CAAAE,iBAAA;cACA,MAAAC,eAAA,GAAAH,WAAA,CAAAE,iBAAA,CAAAlE,KAAA,MAAAK,MAAA,CAAA+D,IAAA,IAAAA,IAAA,CAAAhE,IAAA;cACA,KAAAsD,IAAA,MAAApJ,IAAA,CAAAW,mBAAA,EAAAiG,UAAA,EAAAiD,eAAA;YACA;UACA;QACA;MACA,SAAArD,KAAA;QACAnE,OAAA,CAAAmE,KAAA,yCAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;MACA;QACA,KAAAvB,sBAAA;MACA;IACA;IACAsF,SAAA;MACA,SAAA9D,uBAAA;QACA,KAAAyC,QAAA,CAAAsB,OAAA;QACA;MACA;MAEA,IAAAC,gBAAA;MAEA,aAAA3K,UAAA;QACA;UACA2K,gBAAA;UACA;QACA;UACAA,gBAAA;UACA;QACA;UACAA,gBAAA;UAEA,KAAA3H,sBAAA,CAAA4F,OAAA,CAAA1C,QAAA;YACA,IAAAA,QAAA,CAAAuD,MAAA;cACAvD,QAAA,CAAAuD,MAAA,CAAAb,OAAA,CAAAZ,KAAA;gBACA,IAAAA,KAAA,CAAAhG,QAAA;kBACA,MAAA0H,SAAA,2BAAAxD,QAAA,CAAAzC,WAAA,IAAAuE,KAAA,CAAA2B,UAAA;kBACAgB,gBAAA,CAAAxB,IAAA,CAAAO,SAAA;gBACA;cACA;YACA;YAEA,MAAAS,cAAA,QAAAjK,IAAA,CAAAY,mBAAA,CAAAoF,QAAA,CAAAzC,WAAA;YACA0G,cAAA,CAAAvB,OAAA,CAAAgC,SAAA;cACA,MAAAC,YAAA,iCAAA3E,QAAA,CAAAzC,WAAA,IAAAmH,SAAA;cACAD,gBAAA,CAAAxB,IAAA,IAAA0B,YAAA;cACAF,gBAAA,CAAAxB,IAAA,IAAA0B,YAAA;cACAF,gBAAA,CAAAxB,IAAA,IAAA0B,YAAA;YACA;UACA;UACA;QACA;UACAF,gBAAA;UAEA,KAAA1H,uBAAA,CAAA2F,OAAA,CAAA1C,QAAA;YACA,IAAAA,QAAA,CAAAuD,MAAA;cACAvD,QAAA,CAAAuD,MAAA,CAAAb,OAAA,CAAAZ,KAAA;gBACA,IAAAA,KAAA,CAAAhG,QAAA;kBACA,MAAA0H,SAAA,4BAAAxD,QAAA,CAAAzC,WAAA,IAAAuE,KAAA,CAAA2B,UAAA;kBACAgB,gBAAA,CAAAxB,IAAA,CAAAO,SAAA;gBACA;cACA;YACA;UACA;UACA;QACA;UACAiB,gBAAA;UACA;QACA;UACA;MACA;MAEA,KAAAG,cAAA,CAAAH,gBAAA,EAAAI,KAAA;QACA,IAAAA,KAAA;UACA,KAAA/K,UAAA;QACA;MACA;IACA;IACA8K,eAAArB,MAAA,EAAAnH,QAAA;MACA,IAAAmH,MAAA,CAAA9B,MAAA;QACArF,QAAA;QACA;MACA;MAEA,MAAA0I,kBAAA,GAAAvB,MAAA,CAAA5D,GAAA,CAAAmC,KAAA;QACA,WAAAiD,OAAA,CAAAC,OAAA;UACA,KAAAC,KAAA,CAAAjL,IAAA,CAAAkL,aAAA,CAAApD,KAAA,EAAAqD,YAAA;YACA9I,OAAA,CAAAC,GAAA,OAAAwF,KAAA,UAAAqD,YAAA;YACAH,OAAA;cAAAlD,KAAA;cAAAqD;YAAA;UACA;QACA;MACA;MAEAJ,OAAA,CAAAK,GAAA,CAAAN,kBAAA,EAAAO,IAAA,CAAAC,OAAA;QACA,MAAAC,QAAA,GAAAD,OAAA,CAAAE,IAAA,CAAAC,MAAA,IAAAA,MAAA,CAAAN,YAAA;QACA9I,OAAA,CAAAC,GAAA,UAAAgJ,OAAA,YAAAC,QAAA;QACAnJ,QAAA,EAAAmJ,QAAA;MACA;IACA;IACAG,SAAA;MACA,KAAA5L,UAAA;IACA;IACA,MAAA6L,WAAA;MACA,KAAAV,KAAA,CAAAjL,IAAA,CAAA4L,QAAA,OAAAf,KAAA;QACA,IAAAA,KAAA;UACA;YACA,MAAAgB,QAAA;YACA,MAAAtE,YAAA;YAEA,WAAAX,UAAA,SAAA5G,IAAA,CAAAM,eAAA;cACA,MAAAwL,YAAA,QAAA9L,IAAA,CAAAQ,oBAAA,CAAAoG,UAAA;cACA,MAAAmF,WAAA,QAAA/L,IAAA,CAAAU,kBAAA,CAAAkG,UAAA;cAEAiF,QAAA,CAAA5C,IAAA;gBACA+C,YAAA;gBACAzI,WAAA,EAAAqD,UAAA;gBACAqF,WAAA,MAAAC,IAAA,GAAAC,cAAA,UAAAC,OAAA;gBACAxC,iBAAA,EAAAmC,WAAA,CAAAtE,MAAA,OAAAsE,WAAA,CAAAM,IAAA;gBACA,GAAAP;cACA;cAEA,SAAA9L,IAAA,CAAAa,0BAAA,CAAA+F,UAAA;gBACA4B,MAAA,CAAAwB,MAAA,MAAAhK,IAAA,CAAAa,0BAAA,CAAA+F,UAAA,GAAA8B,OAAA,CAAA4D,WAAA;kBACA/E,YAAA,CAAA0B,IAAA;oBACA1F,WAAA,EAAAqD,UAAA;oBACA,GAAA0F;kBACA;gBACA;cACA;YACA;YAEA,WAAA1F,UAAA,SAAA5G,IAAA,CAAAO,gBAAA;cACA,MAAAuL,YAAA,QAAA9L,IAAA,CAAAS,qBAAA,CAAAmG,UAAA;cACA,MAAAmF,WAAA,QAAA/L,IAAA,CAAAW,mBAAA,CAAAiG,UAAA;cAEAiF,QAAA,CAAA5C,IAAA;gBACA+C,YAAA;gBACAzI,WAAA,EAAAqD,UAAA;gBACAqF,WAAA,MAAAC,IAAA,GAAAC,cAAA,UAAAC,OAAA;gBACAxC,iBAAA,EAAAmC,WAAA,CAAAtE,MAAA,OAAAsE,WAAA,CAAAM,IAAA;gBACA,GAAAP;cACA;YACA;YAEA;cAAAxL,eAAA;cAAAI,kBAAA;cAAAF,oBAAA;cAAAG,mBAAA;cAAAJ,gBAAA;cAAAE,qBAAA;cAAAG,mBAAA;cAAA2L,oBAAA;cAAA1L,0BAAA;cAAA2L,2BAAA;cAAA,GAAAC;YAAA,SAAAzM,IAAA;YACAqC,OAAA,CAAAmF,IAAA,CAAAlH,eAAA,EAAAI,kBAAA,EAAAF,oBAAA,EAAAG,mBAAA,EAAAJ,gBAAA,EAAAE,qBAAA,EAAAG,mBAAA,EAAA2L,oBAAA,EAAA1L,0BAAA,EAAA2L,2BAAA;YAEAC,UAAA,CAAAC,WAAA,OAAAR,IAAA,GAAAC,cAAA,UAAAC,OAAA;YAEA,WAAAtF,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAnH,IAAA;gBACA,GAAA4M,UAAA;gBACAxM,QAAA,OAAA0M,MAAA,CAAAxG,MAAA,CAAAP;cACA;cACAiG,QAAA,EAAAA,QAAA;cACAe,aAAA,EAAArF;YACA;YACA,KAAA2B,QAAA,CAAA2D,OAAA;YACA,KAAAC,OAAA,CAAA7D,IAAA;UACA,SAAAzC,KAAA;YACA,KAAA0C,QAAA,CAAA1C,KAAA;YACAnE,OAAA,CAAAmE,KAAA,0BAAAA,KAAA;UACA;QACA;MACA;IACA;IACA,MAAAuG,iBAAA;MACA,UAAAJ,MAAA,CAAAxG,MAAA,CAAAP,EAAA;QACA;MACA;MAEA,KAAA7F,OAAA;MACA;QACA,MAAA8G,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YAAAhH,QAAA,OAAA0M,MAAA,CAAAxG,MAAA,CAAAP;UAAA;QACA;QACA,MAAAoH,SAAA,GAAAnG,QAAA,CAAAhH,IAAA,CAAAA,IAAA;QACA,MAAAgM,QAAA,GAAAhF,QAAA,CAAAhH,IAAA,CAAAgM,QAAA;QACA,MAAAtE,YAAA,GAAAV,QAAA,CAAAhH,IAAA,CAAA+M,aAAA;QAEA,KAAA5J,sBAAA;QACA,KAAAC,uBAAA;QACA4I,QAAA,CAAAnD,OAAA,CAAAuE,OAAA;UACA,IAAAA,OAAA,CAAAjB,YAAA;YACA,KAAAhJ,sBAAA,CAAAiK,OAAA,CAAA1J,WAAA,IAAA0J,OAAA;UACA,WAAAA,OAAA,CAAAjB,YAAA;YACA,KAAA/I,uBAAA,CAAAgK,OAAA,CAAA1J,WAAA,IAAA0J,OAAA;UACA;QACA;QACA,MAAAC,cAAA,GAAArB,QAAA,CACA9F,MAAA,CAAAC,QAAA,IAAAA,QAAA,CAAAgG,YAAA,QACArG,GAAA,CAAAK,QAAA,IAAAA,QAAA,CAAAzC,WAAA;QAEA,MAAA4J,eAAA,GAAAtB,QAAA,CACA9F,MAAA,CAAAC,QAAA,IAAAA,QAAA,CAAAgG,YAAA,QACArG,GAAA,CAAAK,QAAA,IAAAA,QAAA,CAAAzC,WAAA;QAEA,KAAAL,gBAAA;QACAqE,YAAA,CAAAmB,OAAA,CAAAzB,KAAA;UACA,KAAA/D,gBAAA,CAAA+D,KAAA,CAAAiD,iBAAA,IAAAjD,KAAA;QACA;QAEA5E,OAAA,CAAAmF,IAAA,0BAAAtE,gBAAA;QAEA,KAAAlD,IAAA;UACA,QAAAA,IAAA;UACA,GAAAgN,SAAA;UACA7M,qBAAA,EAAA0F,QAAA,CAAAmH,SAAA,CAAA7M,qBAAA;UACAG,eAAA,EAAA4M,cAAA;UACA3M,gBAAA,EAAA4M;QACA;QAEA,IAAAH,SAAA,CAAAjM,uBAAA;UACA,KAAAqM,yBAAA,CAAAvH,QAAA,CAAAmH,SAAA,CAAAjM,uBAAA;QACA;UACA,KAAAY,cAAA;UACA,KAAAC,aAAA;QACA;QACA,KAAAyL,qBAAA;QAEA,WAAAC,SAAA;QACA,WAAAhF,yBAAA,MAAAtI,IAAA,CAAAM,eAAA;QACA,WAAAgK,0BAAA,MAAAtK,IAAA,CAAAO,gBAAA;MAEA,SAAAiG,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;QACAnE,OAAA,CAAAmE,KAAA,iCAAAA,KAAA;MACA;QACA,KAAAzG,OAAA;MACA;IACA;IACAwN,oBAAA3G,UAAA;MACA,KAAAxD,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,WAAA,EAAAqD;MACA;MACA,KAAAzD,sBAAA;MACA,KAAAmK,SAAA;QACA,KAAArC,KAAA,CAAAuC,UAAA,SAAAvC,KAAA,CAAAuC,UAAA,CAAAC,aAAA;MACA;IACA;IACA,MAAAC,aAAA;MACA,KAAAzC,KAAA,CAAAuC,UAAA,CAAA5B,QAAA,OAAAf,KAAA;QACA,IAAAA,KAAA;UACA;YACA,KAAApH,YAAA;YACA,WAAAqD,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAnH,IAAA;gBACA,QAAAuD,aAAA;gBACAG,WAAA,OAAAH,aAAA,CAAAG;cACA;YACA;YACA,KAAA2F,QAAA,CAAA2D,OAAA;YACA,KAAA1J,sBAAA;YACA,KAAAC,aAAA;cACAC,WAAA;cACAC,UAAA;cACAC,WAAA;YACA;YACA,WAAA+E,yBAAA,MAAAtI,IAAA,CAAAM,eAAA;YACA,WAAAgK,0BAAA,MAAAtK,IAAA,CAAAO,gBAAA;UACA,SAAAiG,KAAA;YACA,KAAA0C,QAAA,CAAA1C,KAAA;YACAnE,OAAA,CAAAmE,KAAA,6BAAAA,KAAA;UACA;YACA,KAAA/C,YAAA;UACA;QACA;MACA;IACA;IACAkK,eAAA;MACA,UAAA3N,IAAA,CAAAC,QAAA;MACA,MAAA2N,GAAA,kDAAA5N,IAAA,CAAAC,QAAA;MACA4N,MAAA,CAAAC,IAAA,CAAAF,GAAA;IACA;IACA,MAAAG,kBAAA;MACA,UAAA/N,IAAA,CAAAC,QAAA;MACA;QACA,WAAA+N,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACA1L,IAAA;QACA;QACA,WAAAsE,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YAAAhH,QAAA,OAAAD,IAAA,CAAAC;UAAA;QACA;QACA,KAAAiJ,QAAA,CAAA2D,OAAA;QACA,KAAAC,OAAA,CAAA7D,IAAA;MACA,SAAAzC,KAAA;QACA,IAAAA,KAAA;UACA,KAAA0C,QAAA,CAAA1C,KAAA;UACAnE,OAAA,CAAAmE,KAAA,0BAAAA,KAAA;QACA;MACA;IACA;IACA,MAAA2H,qBAAA;MACA;QACA,MAAAtH,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACAmH,SAAA;UACA;QACA;QACA,KAAAvL,eAAA,GAAAgE,QAAA,CAAAhH,IAAA,CAAAA,IAAA;MACA,SAAA2G,KAAA;QACAnE,OAAA,CAAAmE,KAAA,qCAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;MACA;IACA;IACA,MAAA6H,kBAAA;MACA;QACA,MAAAxH,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;QACA;QACA,KAAArE,YAAA,GAAAkE,QAAA,CAAAhH,IAAA,CAAAA,IAAA;QACAwC,OAAA,CAAAC,GAAA,gBAAAK,YAAA;MACA,SAAA6D,KAAA;QACAnE,OAAA,CAAAmE,KAAA,kCAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;MACA;IACA;IACA,MAAA8H,eAAA;MACA;QACA,MAAAzH,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;QACA;QACA,KAAApE,SAAA,GAAAiE,QAAA,CAAAhH,IAAA,CAAAA,IAAA;MACA,SAAA2G,KAAA;QACAnE,OAAA,CAAAmE,KAAA,+BAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;MACA;IACA;IACA,MAAA+H,gBAAA;MACA;QACA,MAAA1H,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;QACA;QACA,KAAAjC,UAAA,GAAA8B,QAAA,CAAAhH,IAAA,CAAAA,IAAA;MACA,SAAA2G,KAAA;QACAnE,OAAA,CAAAmE,KAAA,gCAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;MACA;IACA;IACAgI,wBAAA;MACAnM,OAAA,CAAAC,GAAA,gBAAAtC,IAAA,CAAAE,wBAAA;MACA,KAAAoN,SAAA;QACA,KAAArC,KAAA,CAAAjL,IAAA,CAAAyN,aAAA;MACA;IACA;IACAgB,qBAAA;MACA,KAAAzO,IAAA,CAAAM,eAAA;MACA,KAAAN,IAAA,CAAAO,gBAAA;MACA,KAAAuC,sBAAA;MACA,KAAAC,uBAAA;MACA,KAAA/C,IAAA,CAAAQ,oBAAA;MACA,KAAAR,IAAA,CAAAS,qBAAA;MACA,KAAAT,IAAA,CAAAU,kBAAA;MACA,KAAAV,IAAA,CAAAW,mBAAA;MACA,KAAAX,IAAA,CAAAY,mBAAA;MACA,KAAAZ,IAAA,CAAAa,0BAAA;MACA,KAAAyM,SAAA;QACA,KAAArC,KAAA,CAAAjL,IAAA,CAAAyN,aAAA;MACA;IACA;IACAiB,yBAAA9H,UAAA;MACA,MAAAqD,cAAA,QAAAjK,IAAA,CAAAY,mBAAA,CAAAgG,UAAA;MAEA,UAAA5G,IAAA,CAAAa,0BAAA,CAAA+F,UAAA;QACA,KAAAwC,IAAA,MAAApJ,IAAA,CAAAa,0BAAA,EAAA+F,UAAA;MACA;MAEA,MAAAZ,QAAA,QAAAlD,sBAAA,CAAAwC,IAAA,CAAA+B,CAAA,IAAAA,CAAA,CAAA9D,WAAA,KAAAqD,UAAA;MACA,MAAAU,cAAA,GAAAtB,QAAA,GAAAA,QAAA,CAAAuB,YAAA;MAEA,IAAAD,cAAA,CAAAG,MAAA;QACA;MACA;MAEAe,MAAA,CAAAC,IAAA,MAAA5G,KAAA,EAAA6G,OAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,UAAA,+BAAAhC,UAAA;UACA,KAAAkC,OAAA,MAAAjH,KAAA,EAAA8G,GAAA;QACA;MACA;MAEAsB,cAAA,CAAAvB,OAAA,CAAAzB,KAAA;QACA,UAAAjH,IAAA,CAAAa,0BAAA,CAAA+F,UAAA,EAAAK,KAAA;UAEA,MAAA0H,WAAA,GAAArH,cAAA,CAAAhC,IAAA,CAAA+B,CAAA,IAAAA,CAAA,CAAA6C,iBAAA,KAAAjD,KAAA;UAEA,KAAAmC,IAAA,MAAApJ,IAAA,CAAAa,0BAAA,CAAA+F,UAAA,GAAAK,KAAA;YACAiD,iBAAA,EAAAyE,WAAA,CAAAzE,iBAAA;YACAxC,iBAAA,EAAAiH,WAAA,CAAAjH,iBAAA;YACAyC,0BAAA;YACAC,qBAAA;YACAC,uBAAA;UACA;QACA;QAEA,MAAAM,YAAA,iCAAA/D,UAAA,IAAAK,KAAA;QACA,KAAAmC,IAAA,MAAAvH,KAAA,KAAA8I,YAAA,gCACA;UAAA7I,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAA4M,OAAA;UAAA7M,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,KAAAoH,IAAA,MAAAvH,KAAA,KAAA8I,YAAA,2BACA;UAAA7I,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAA4M,OAAA;UAAA7M,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,KAAAoH,IAAA,MAAAvH,KAAA,KAAA8I,YAAA,6BACA;UAAA7I,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAA4M,OAAA;UAAA7M,OAAA;UAAAC,OAAA;QAAA,EACA;MACA;MAEAwG,MAAA,CAAAC,IAAA,MAAAzI,IAAA,CAAAa,0BAAA,CAAA+F,UAAA,GAAA8B,OAAA,CAAAzB,KAAA;QACA,KAAAgD,cAAA,CAAAhE,QAAA,CAAAgB,KAAA;UACA,KAAA6B,OAAA,MAAA9I,IAAA,CAAAa,0BAAA,CAAA+F,UAAA,GAAAK,KAAA;QACA;MACA;IACA;IACA4H,yBAAAjI,UAAA;MACA,MAAAqD,cAAA,QAAAjK,IAAA,CAAAY,mBAAA,CAAAgG,UAAA;MACA,MAAAkI,MAAA,QAAA9O,IAAA,CAAAa,0BAAA,CAAA+F,UAAA;MAEA,MAAAmI,GAAA,GAAA9E,cAAA,CAAAtE,GAAA,CAAA+E,SAAA;QACAR,iBAAA,EAAAQ,SAAA;QACAhD,iBAAA,EAAAoH,MAAA,CAAApE,SAAA,KAAAoE,MAAA,CAAApE,SAAA,EAAAhD,iBAAA;QACAyC,0BAAA,EAAA2E,MAAA,CAAApE,SAAA,KAAAoE,MAAA,CAAApE,SAAA,EAAAP,0BAAA;QACAC,qBAAA,EAAA0E,MAAA,CAAApE,SAAA,KAAAoE,MAAA,CAAApE,SAAA,EAAAN,qBAAA;QACAC,uBAAA,EAAAyE,MAAA,CAAApE,SAAA,KAAAoE,MAAA,CAAApE,SAAA,EAAAL,uBAAA;MACA;MAEAhI,OAAA,CAAAmF,IAAA,QAAAuH,GAAA;MACA,OAAAA,GAAA;IACA;IACAC,wBAAApI,UAAA,EAAAqI,SAAA,EAAAnH,KAAA,EAAA3F,KAAA;MACA,UAAAnC,IAAA,CAAAa,0BAAA,CAAA+F,UAAA;QACA,KAAAwC,IAAA,MAAApJ,IAAA,CAAAa,0BAAA,EAAA+F,UAAA;MACA;MACA,UAAA5G,IAAA,CAAAa,0BAAA,CAAA+F,UAAA,EAAAqI,SAAA;QACA,KAAA7F,IAAA,MAAApJ,IAAA,CAAAa,0BAAA,CAAA+F,UAAA,GAAAqI,SAAA;MACA;MACA,KAAA7F,IAAA,MAAApJ,IAAA,CAAAa,0BAAA,CAAA+F,UAAA,EAAAqI,SAAA,GAAAnH,KAAA,EAAA3F,KAAA;IACA;IACA+M,YAAA;MACA,aAAAtN,aAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAuN,YAAA;MACA,aAAAvN,aAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAwN,QAAA;MACA,aAAAxN,aAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAyL,sBAAA;MACA,IAAAgC,YAAA;MACA,aAAAzN,aAAA;QACA;UACAyN,YAAA,QAAA1N,cAAA;UACA;QACA;UACA0N,YAAA,QAAA1N,cAAA;UACA;QACA;UACA0N,YAAA,QAAA1N,cAAA;UACA;MACA;MAEA,IAAA0N,YAAA;QACAA,YAAA;QACA,KAAA1N,cAAA;QACA,KAAAC,aAAA;MACA;MAEA,KAAA5B,IAAA,CAAAe,uBAAA,GAAAsO,YAAA;MACA,OAAAA,YAAA;IACA;IACAjC,0BAAAkC,OAAA;MACA,KAAAA,OAAA,IAAAA,OAAA;QACA,KAAA3N,cAAA;QACA,KAAAC,aAAA;QACA;MACA;MAEA,IAAA0N,OAAA,YAAAA,OAAA;QACA,KAAA3N,cAAA,GAAA2N,OAAA;QACA,KAAA1N,aAAA;MACA,WAAA0N,OAAA,UAAAA,OAAA;QACA,KAAA3N,cAAA,GAAA2N,OAAA;QACA,KAAA1N,aAAA;MACA;QACA,KAAAD,cAAA,GAAA2N,OAAA;QACA,KAAA1N,aAAA;MACA;IACA;IACA2N,2BAAA;MACA,KAAAlC,qBAAA;IACA;IACAmC,0BAAA;MACA,MAAAC,cAAA,QAAApC,qBAAA;MACA,KAAAD,yBAAA,CAAAqC,cAAA;MACA,KAAApC,qBAAA;IACA;IACAqC,qBAAA;MACA,KAAA9L,cAAA;QACAC,YAAA;MACA;MACA,KAAAH,uBAAA;MACA,KAAA4J,SAAA;QACA,KAAArC,KAAA,CAAA0E,WAAA,SAAA1E,KAAA,CAAA0E,WAAA,CAAAlC,aAAA;MACA;IACA;IACA,MAAAmC,cAAA;MACA,KAAA3E,KAAA,CAAA0E,WAAA,CAAA/D,QAAA,OAAAf,KAAA;QACA,IAAAA,KAAA;UACA;YACA,KAAAhG,aAAA;YACA,WAAAiC,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAnH,IAAA,OAAA+D;YACA;YACA,KAAAsF,QAAA,CAAA2D,OAAA;YACA,KAAAnJ,uBAAA;YACA,WAAA2K,iBAAA;YACA,SAAA1L,YAAA,CAAA8E,MAAA;cACA,MAAAoI,UAAA,QAAAlN,YAAA,CAAA2C,IAAA,CAAAwK,OAAA,IAAAA,OAAA,CAAAjM,YAAA,UAAAD,cAAA,CAAAC,YAAA;cACA,IAAAgM,UAAA;gBACA,KAAA7P,IAAA,CAAAE,wBAAA,GAAA2P,UAAA,CAAAhM,YAAA;cACA;YACA;UACA,SAAA2C,KAAA;YACA,KAAA0C,QAAA,CAAA1C,KAAA;YACAnE,OAAA,CAAAmE,KAAA,8BAAAA,KAAA;UACA;YACA,KAAA3B,aAAA;UACA;QACA;MACA;IACA;IACAkL,sBAAA;MACA,KAAAjM,eAAA;QACAC,cAAA;QACAC,qBAAA;QACAC,8BAAA;QACAC,+BAAA;QACAC,wBAAA;QACAC,sCAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,eAAA;MACA;MACA,KAAAC,mBAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,gCAAA;MACA,KAAAf,wBAAA;MACA,KAAA2J,SAAA;QACA,KAAArC,KAAA,CAAA+E,YAAA,SAAA/E,KAAA,CAAA+E,YAAA,CAAAvC,aAAA;MACA;IACA;IACA,MAAAwC,2BAAAC,WAAA;MACA,KAAApM,eAAA,CAAAO,mBAAA,GAAA6L,WAAA,CAAA7D,IAAA;MAEA,WAAA8D,4BAAA,CAAAD,WAAA;IACA;IACA,MAAAC,6BAAA5H,WAAA;MACA,IAAAA,WAAA,CAAAd,MAAA;QACA,KAAA/C,gCAAA;QACA,KAAAD,oBAAA;QACA,KAAAX,eAAA,CAAAG,8BAAA;QACA;MACA;MAEA;QACA,MAAAmM,oBAAA;QACA,WAAAxJ,UAAA,IAAA2B,WAAA;UACA,MAAApC,MAAA,cAAAe,yBAAA,CAAAN,UAAA;UACAwJ,oBAAA,CAAAnH,IAAA,CAAA9C,MAAA;QACA;QAEA,MAAAkK,SAAA;QACA,MAAAC,SAAA,OAAAC,GAAA;QAEAH,oBAAA,CAAA1H,OAAA,CAAApB,cAAA;UACAA,cAAA,CAAAoB,OAAA,CAAAzB,KAAA;YACA,KAAAqJ,SAAA,CAAAE,GAAA,CAAAvJ,KAAA,CAAAiD,iBAAA;cACAoG,SAAA,CAAAG,GAAA,CAAAxJ,KAAA,CAAAiD,iBAAA;cACAmG,SAAA,CAAApH,IAAA,CAAAhC,KAAA;YACA;UACA;QACA;QAEA,KAAAvC,gCAAA,GAAA2L,SAAA;QAEA,KAAA5L,oBAAA;QACA,KAAAX,eAAA,CAAAG,8BAAA;MAEA,SAAAuC,KAAA;QACAnE,OAAA,CAAAmE,KAAA,gDAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;QACA,KAAA9B,gCAAA;MACA;IACA;IACAgM,+BAAAC,aAAA;MACA,KAAA7M,eAAA,CAAAG,8BAAA,GAAAmC,IAAA,CAAAwK,SAAA,CAAAD,aAAA;IACA;IACA,MAAAE,eAAA;MACA,KAAA5F,KAAA,CAAA+E,YAAA,CAAApE,QAAA,OAAAf,KAAA;QACA,IAAAA,KAAA;UACA;YACA,KAAA/F,cAAA;YACA,WAAAgC,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAnH,IAAA,OAAAiE;YACA;YACA,KAAAoF,QAAA,CAAA2D,OAAA;YACA,KAAAlJ,wBAAA;YACA,WAAA2K,cAAA;UACA,SAAA9H,KAAA;YACA,KAAA0C,QAAA,CAAA1C,KAAA;YACAnE,OAAA,CAAAmE,KAAA,gCAAAA,KAAA;UACA;YACA,KAAA1B,cAAA;UACA;QACA;MACA;IACA;EACA;EACA,MAAAgM,QAAA;IACA,WAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;IAAA;IACA,WAAA5C,iBAAA;IACA,WAAAC,cAAA;IACA,WAAAC,eAAA;IACA,WAAAxB,gBAAA;IACA,WAAAoB,oBAAA;EACA;AACA", "ignoreList": []}]}