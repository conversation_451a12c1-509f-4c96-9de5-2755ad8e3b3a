{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\scene\\EditScene.vue?vue&type=template&id=69abc815&scoped=true", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\scene\\EditScene.vue", "mtime": 1754034064975}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754032187340}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImVkaXQtc2NlbmUiPgogICAgPGVsLWJyZWFkY3J1bWIgc2VwYXJhdG9yPSIvIiBjbGFzcz0iZWRpdC1icmVhZGNydW1iIiBzdHlsZT0ibWFyZ2luLWJvdHRvbTogMThweDsiPgogICAgICAgIDxlbC1icmVhZGNydW1iLWl0ZW0gOnRvPSJ7IHBhdGg6ICcvc2NlbmUvbWFuYWdlJyB9Ij7lnLrmma/nrqHnkIY8L2VsLWJyZWFkY3J1bWItaXRlbT4KICAgICAgICA8ZWwtYnJlYWRjcnVtYi1pdGVtIDp0bz0ieyBwYXRoOiAnL3NjZW5lL2VkaXQvJysgZm9ybS5zY2VuZV9pZCB9Ij57eyBmb3JtLnNjZW5lX25hbWUgfX08L2VsLWJyZWFkY3J1bWItaXRlbT4KICAgICAgICA8ZWwtYnJlYWRjcnVtYi1pdGVtPuWcuuaZr+e8lui+kTwvZWwtYnJlYWRjcnVtYi1pdGVtPgogICAgPC9lbC1icmVhZGNydW1iPgogICAgPGRpdiBjbGFzcz0iZWRpdC1hY3Rpb25zIj4KICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIHNpemU9InNtYWxsIiBAY2xpY2s9Im9wZW5BSU9wdGltaXplIiA6ZGlzYWJsZWQ9IiFmb3JtLnNjZW5lX2lkIj5BSeS8mOWMluaPkOekuuivjTwvZWwtYnV0dG9uPgogICAgICAgIDxlbC1idXR0b24gdHlwZT0iZGFuZ2VyIiBzaXplPSJzbWFsbCIgQGNsaWNrPSJoYW5kbGVEZWxldGVTY2VuZSIgOmRpc2FibGVkPSIhZm9ybS5zY2VuZV9pZCI+5Yig6Zmk5Zy65pmvPC9lbC1idXR0b24+CiAgICA8L2Rpdj4KICAgIDxlbC1zdGVwcyA6YWN0aXZlPSJhY3RpdmVTdGVwIiBmaW5pc2gtc3RhdHVzPSJzdWNjZXNzIiBzaW1wbGU+CiAgICAgICAgPGVsLXN0ZXAgdGl0bGU9IuWfuuacrOS/oeaBryIgLz4KICAgICAgICA8ZWwtc3RlcCB0aXRsZT0i6K6h566X5Z+65YeG57q/6YWN572uIiAvPgogICAgICAgIDxlbC1zdGVwIHRpdGxlPSLmlbDmja7ovpPlhaXlubPlj7AiIC8+CiAgICAgICAgPGVsLXN0ZXAgdGl0bGU9IuaVsOaNrui+k+WHuuW5s+WPsCIgLz4KICAgICAgICA8ZWwtc3RlcCB0aXRsZT0i5YW25LuW6K6+572uIiAvPgogICAgPC9lbC1zdGVwcz4KCiAgICA8ZWwtZm9ybSByZWY9ImZvcm0iIDptb2RlbD0iZm9ybSIgOnJ1bGVzPSJydWxlcyIgbGFiZWwtd2lkdGg9IjE1MHB4IiBjbGFzcz0ibXQtMjAiIHYtbG9hZGluZz0ibG9hZGluZyI+CiAgICAgICAgPGRpdiB2LXNob3c9ImFjdGl2ZVN0ZXAgPT09IDAiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlnLrmma/lkI3np7AiIHByb3A9InNjZW5lX25hbWUiPgogICAgICAgICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9ImZvcm0uc2NlbmVfbmFtZSIgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeWcuuaZr+WQjeensCI+PC9lbC1pbnB1dD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuS7u+WKoeexu+WeiyIgcHJvcD0ibGlua2VkX3Rhc2tfdHlwZV9jb2RlIj4KICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImRpc3BsYXk6IGZsZXg7IGdhcDogMTBweDsgYWxpZ24taXRlbXM6IGNlbnRlcjsiPgogICAgICAgICAgICAgICAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0iZm9ybS5saW5rZWRfdGFza190eXBlX2NvZGUiIHBsYWNlaG9sZGVyPSLor7fpgInmi6nku7vliqHnsbvlnosiIHN0eWxlPSJmbGV4OiAxOyIgQGNoYW5nZT0iaGFuZGxlVGFza1R5cGVDaGFuZ2UiPgogICAgICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2LWZvcj0idGFza1R5cGUgaW4gdGFza1R5cGVzIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgOmtleT0idGFza1R5cGUudGFza190eXBlX2NvZGUiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6bGFiZWw9InRhc2tUeXBlLnRhc2tfdHlwZV9uYW1lIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgOnZhbHVlPSJ0YXNrVHlwZS50YXNrX3R5cGVfY29kZSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtb3B0aW9uPgogICAgICAgICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICAgICAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgc2l6ZT0ic21hbGwiIGljb249ImVsLWljb24tcGx1cyIgQGNsaWNrPSJzaG93QWRkVGFza1R5cGVEaWFsb2ciPgogICAgICAgICAgICAgICAgICAgICAgICDmlrDlop7ku7vliqHnsbvlnosKICAgICAgICAgICAgICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5Zy65pmv57G75Z6LIiBwcm9wPSJzY2VuZV9idXNpbmVzc190eXBlIj4KICAgICAgICAgICAgICAgIDxlbC1zZWxlY3QgCiAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5zY2VuZV9idXNpbmVzc190eXBlIiAKICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup5oiW6L6T5YWl5Zy65pmv57G75Z6LIiAKICAgICAgICAgICAgICAgICAgICBmaWx0ZXJhYmxlIAogICAgICAgICAgICAgICAgICAgIGFsbG93LWNyZWF0ZSAKICAgICAgICAgICAgICAgICAgICBkZWZhdWx0LWZpcnN0LW9wdGlvbgogICAgICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSI+CiAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICAgICAgICAgICAgICB2LWZvcj0ic2NlbmVUeXBlIGluIHNjZW5lVHlwZXMiCiAgICAgICAgICAgICAgICAgICAgICAgIDprZXk9InNjZW5lVHlwZS5zY2VuZV90eXBlX2lkIHx8IHNjZW5lVHlwZS5zY2VuZV90eXBlX2NvZGUiCiAgICAgICAgICAgICAgICAgICAgICAgIDpsYWJlbD0ic2NlbmVUeXBlLnNjZW5lX3R5cGVfbmFtZSIKICAgICAgICAgICAgICAgICAgICAgICAgOnZhbHVlPSJzY2VuZVR5cGUuc2NlbmVfdHlwZV9uYW1lIj4KICAgICAgICAgICAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6aG555uu57uEIiBwcm9wPSJsaW5rZWRfcHJvamVjdF90ZWFtX25hbWUiPgogICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogZmxleDsgZ2FwOiAxMHB4OyBhbGlnbi1pdGVtczogY2VudGVyOyI+CiAgICAgICAgICAgICAgICAgICAgPGVsLXNlbGVjdCB2LW1vZGVsPSJmb3JtLmxpbmtlZF9wcm9qZWN0X3RlYW1fbmFtZSIgcGxhY2Vob2xkZXI9Iuivt+mAieaLqemhueebrue7hCIgc3R5bGU9ImZsZXg6IDE7IiBAY2hhbmdlPSJoYW5kbGVQcm9qZWN0VGVhbUNoYW5nZSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHYtZm9yPSJwcm9qZWN0IGluIHByb2plY3RUZWFtcyIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIDprZXk9InByb2plY3QucHJvamVjdF9pZCIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIDpsYWJlbD0icHJvamVjdC5wcm9qZWN0X25hbWUiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6dmFsdWU9InByb2plY3QucHJvamVjdF9uYW1lIj4KICAgICAgICAgICAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBzaXplPSJzbWFsbCIgaWNvbj0iZWwtaWNvbi1wbHVzIiBAY2xpY2s9InNob3dBZGRQcm9qZWN0RGlhbG9nIj4KICAgICAgICAgICAgICAgICAgICAgICAg5paw5aKe6aG555uu57uECiAgICAgICAgICAgICAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWcuuaZr+aPj+i/sCIgcHJvcD0ic2NlbmVfZGVzY3JpcHRpb24iPgogICAgICAgICAgICAgICAgPGVsLWlucHV0IHR5cGU9InRleHRhcmVhIiB2LW1vZGVsPSJmb3JtLnNjZW5lX2Rlc2NyaXB0aW9uIiBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5Zy65pmv5o+P6L+wIgogICAgICAgICAgICAgICAgICAgIDpyb3dzPSI0Ij48L2VsLWlucHV0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2Rpdj4KCiAgICAgICAgPGRpdiB2LXNob3c9ImFjdGl2ZVN0ZXAgPT09IDEiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLkvb/nlKjmlbDmja7nmoTotbflp4vlpKnmlbAiIHByb3A9ImJhc2VsaW5lX2RhdGFfc3RhcnRfZGF5c19hZ28iPgogICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogZmxleDsgYWxpZ24taXRlbXM6IGNlbnRlcjsgZ2FwOiAxMHB4OyI+CiAgICAgICAgICAgICAgICAgICAgPGVsLWlucHV0LW51bWJlcgogICAgICAgICAgICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLmJhc2VsaW5lX2RhdGFfc3RhcnRfZGF5c19hZ28iCiAgICAgICAgICAgICAgICAgICAgICAgIDptaW49IjEiCiAgICAgICAgICAgICAgICAgICAgICAgIDptYXg9IjM2NSIKICAgICAgICAgICAgICAgICAgICAgICAgOnN0ZXA9IjEiCiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlpKnmlbAiCiAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMjAwcHgiPgogICAgICAgICAgICAgICAgICAgIDwvZWwtaW5wdXQtbnVtYmVyPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJjb2xvcjogIzkwOTM5OTsiPuWkqTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8ZWwtdG9vbHRpcCBlZmZlY3Q9ImRhcmsiIHBsYWNlbWVudD0idG9wIiBjb250ZW50PSJULeWfuue6v+S9v+eUqOaVsOaNrueahOi1t+Wni+WkqeaVsCI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLXF1ZXN0aW9uIiBzdHlsZT0iY29sb3I6ICM5MDkzOTk7IGN1cnNvcjogaGVscDsiPjwvaT4KICAgICAgICAgICAgICAgICAgICA8L2VsLXRvb2x0aXA+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaOkumZpOacgOi/keeahOaVsOaNruWkqeaVsCIgcHJvcD0iYmFzZWxpbmVfZGF0YV9leGNsdWRlX3JlY2VudF9kYXlzIj4KICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImRpc3BsYXk6IGZsZXg7IGFsaWduLWl0ZW1zOiBjZW50ZXI7IGdhcDogMTBweDsiPgogICAgICAgICAgICAgICAgICAgIDxlbC1pbnB1dC1udW1iZXIKICAgICAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5iYXNlbGluZV9kYXRhX2V4Y2x1ZGVfcmVjZW50X2RheXMiCiAgICAgICAgICAgICAgICAgICAgICAgIDptaW49IjAiCiAgICAgICAgICAgICAgICAgICAgICAgIDptYXg9IjM2NSIKICAgICAgICAgICAgICAgICAgICAgICAgOnN0ZXA9IjEiCiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlpKnmlbAiCiAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMjAwcHgiPgogICAgICAgICAgICAgICAgICAgIDwvZWwtaW5wdXQtbnVtYmVyPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJjb2xvcjogIzkwOTM5OTsiPuWkqTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8ZWwtdG9vbHRpcCBlZmZlY3Q9ImRhcmsiIHBsYWNlbWVudD0idG9wIiBjb250ZW50PSJULeWfuue6v+aOkumZpOacgOi/keeahOaVsOaNruWkqeaVsCI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLXF1ZXN0aW9uIiBzdHlsZT0iY29sb3I6ICM5MDkzOTk7IGN1cnNvcjogaGVscDsiPjwvaT4KICAgICAgICAgICAgICAgICAgICA8L2VsLXRvb2x0aXA+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDwhLS0gPGVsLWZvcm0taXRlbSBsYWJlbD0i5qC35pys5oC75pWwIj4KICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImRpc3BsYXk6IGZsZXg7IGFsaWduLWl0ZW1zOiBjZW50ZXI7IGdhcDogMTBweDsiPgogICAgICAgICAgICAgICAgICAgIDxlbC1pbnB1dCAKICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9IuWwhuagueaNruS4iui/sOmFjee9ruiHquWKqOiuoeeulyIgCiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkIAogICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDMwMHB4OyI+CiAgICAgICAgICAgICAgICAgICAgPC9lbC1pbnB1dD4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iY29sb3I6ICM5MDkzOTk7IGZvbnQtc2l6ZTogMTJweDsiPuWfuuS6jui1t+Wni+WkqeaVsOWSjOaOkumZpOWkqeaVsOiMg+WbtOWGheeahOaVsOaNrumHjzwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4gLS0+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9Iuagt+acrOmHj+acgOWwj+mYiOWAvCIgcHJvcD0ibWluX2Jhc2VsaW5lX3NhbXBsZV9jb3VudCI+CiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogY2VudGVyOyBnYXA6IDEwcHg7Ij4KICAgICAgICAgICAgICAgICAgICA8ZWwtaW5wdXQtbnVtYmVyCiAgICAgICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0ubWluX2Jhc2VsaW5lX3NhbXBsZV9jb3VudCIKICAgICAgICAgICAgICAgICAgICAgICAgOm1pbj0iMSIKICAgICAgICAgICAgICAgICAgICAgICAgOm1heD0iMTAwMDAiCiAgICAgICAgICAgICAgICAgICAgICAgIDpzdGVwPSIxIgogICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5qC35pys6YePIgogICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDIwMHB4Ij4KICAgICAgICAgICAgICAgICAgICA8L2VsLWlucHV0LW51bWJlcj4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iY29sb3I6ICM5MDkzOTk7Ij7kuKo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPGVsLXRvb2x0aXAgZWZmZWN0PSJkYXJrIiBwbGFjZW1lbnQ9InRvcCIgY29udGVudD0i5LiA5Liq5Zy65pmv5Zyo5LiA5qyh6K6h566X5Z+65YeG57q/5pe277yM5omA6ZyA6KaB55qE5pyA5bCP5qC35pys6YePIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tcXVlc3Rpb24iIHN0eWxlPSJjb2xvcjogIzkwOTM5OTsgY3Vyc29yOiBoZWxwOyI+PC9pPgogICAgICAgICAgICAgICAgICAgIDwvZWwtdG9vbHRpcD4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5Z+65YeG57q/5pu05paw6aKR546HIiBwcm9wPSJiYXNlbGluZV9yZWZyZXNoX2ZyZXF1ZW5jeV9kYXlzIj4KICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImRpc3BsYXk6IGZsZXg7IGFsaWduLWl0ZW1zOiBjZW50ZXI7IGdhcDogMTBweDsiPgogICAgICAgICAgICAgICAgICAgIDxlbC1pbnB1dC1udW1iZXIKICAgICAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5iYXNlbGluZV9yZWZyZXNoX2ZyZXF1ZW5jeV9kYXlzIgogICAgICAgICAgICAgICAgICAgICAgICA6bWluPSIxIgogICAgICAgICAgICAgICAgICAgICAgICA6bWF4PSIzNjUiCiAgICAgICAgICAgICAgICAgICAgICAgIDpzdGVwPSIxIgogICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl6aKR546HIgogICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDIwMHB4Ij4KICAgICAgICAgICAgICAgICAgICA8L2VsLWlucHV0LW51bWJlcj4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iY29sb3I6ICM5MDkzOTk7Ij7lpKk8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPGVsLXRvb2x0aXAgZWZmZWN0PSJkYXJrIiBwbGFjZW1lbnQ9InRvcCIgY29udGVudD0i6K+E5Lyw5pWI5p6c55qE5Z+65YeG57q/5pu05paw55qE6aKR546HIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tcXVlc3Rpb24iIHN0eWxlPSJjb2xvcjogIzkwOTM5OTsgY3Vyc29yOiBoZWxwOyI+PC9pPgogICAgICAgICAgICAgICAgICAgIDwvZWwtdG9vbHRpcD4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2Rpdj4KCiAgICAgICAgPGRpdiB2LXNob3c9ImFjdGl2ZVN0ZXAgPT09IDIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLpgInmi6novpPlhaXlubPlj7AiIHByb3A9ImlucHV0X3BsYXRmb3JtcyI+CiAgICAgICAgICAgICAgICA8ZGl2IHYtaWY9IiFmb3JtLmxpbmtlZF90YXNrX3R5cGVfY29kZSIgY2xhc3M9InBsYXRmb3JtLXNlbGVjdGlvbi10aXAiPgogICAgICAgICAgICAgICAgICAgIDxlbC1hbGVydAogICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT0i6K+35YWI5Zyo56ys5LiA5q2l6YCJ5oup5Lu75Yqh57G75Z6LIgogICAgICAgICAgICAgICAgICAgICAgICB0eXBlPSJpbmZvIgogICAgICAgICAgICAgICAgICAgICAgICA6Y2xvc2FibGU9ImZhbHNlIgogICAgICAgICAgICAgICAgICAgICAgICBzaG93LWljb24+CiAgICAgICAgICAgICAgICAgICAgPC9lbC1hbGVydD4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiB2LWVsc2UgY2xhc3M9InBsYXRmb3JtLXNlbGVjdGlvbi1jb250YWluZXIiPgogICAgICAgICAgICAgICAgICAgIDxlbC1jaGVja2JveC1ncm91cCB2LW1vZGVsPSJmb3JtLmlucHV0X3BsYXRmb3JtcyIgQGNoYW5nZT0iaGFuZGxlSW5wdXRQbGF0Zm9ybUNoYW5nZSIKICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3M9InBsYXRmb3JtLWNoZWNrYm94LWdyb3VwIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGVsLWNoZWNrYm94IHYtZm9yPSJwbGF0Zm9ybSBpbiBhdmFpbGFibGVQbGF0Zm9ybXMiIDprZXk9InBsYXRmb3JtLnBsYXRmb3JtX2lkIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgOmxhYmVsPSJwbGF0Zm9ybS5wbGF0Zm9ybV9pZCIgY2xhc3M9InBsYXRmb3JtLWNoZWNrYm94LWl0ZW0iPgogICAgICAgICAgICAgICAgICAgICAgICAgICAge3sgcGxhdGZvcm0ucGxhdGZvcm1fbmFtZSB9fQogICAgICAgICAgICAgICAgICAgICAgICA8L2VsLWNoZWNrYm94PgogICAgICAgICAgICAgICAgICAgIDwvZWwtY2hlY2tib3gtZ3JvdXA+CiAgICAgICAgICAgICAgICAgICAgPGRpdiB2LWlmPSJsb2FkaW5nSW5wdXRQbGF0Zm9ybXMiIGNsYXNzPSJsb2FkaW5nLXRpcCI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1hbGVydAogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9Iuato+WcqOWKoOi9veW5s+WPsOmFjee9ru+8jOivt+eojeWAmS4uLiIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9ImluZm8iCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6Y2xvc2FibGU9ImZhbHNlIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgc2hvdy1pY29uPgogICAgICAgICAgICAgICAgICAgICAgICA8L2VsLWFsZXJ0PgogICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAgIDxkaXYgdi1pZj0iYXZhaWxhYmxlUGxhdGZvcm1zLmxlbmd0aCA9PT0gMCIgY2xhc3M9Im5vLXBsYXRmb3Jtcy10aXAiPgogICAgICAgICAgICAgICAgICAgICAgICA8ZWwtYWxlcnQKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPSLlvZPliY3ku7vliqHnsbvlnovmsqHmnInlhbPogZTnmoTlubPlj7AiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPSJ3YXJuaW5nIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgOmNsb3NhYmxlPSJmYWxzZSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3ctaWNvbj4KICAgICAgICAgICAgICAgICAgICAgICAgPC9lbC1hbGVydD4KICAgICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KCiAgICAgICAgICAgIDxkaXYgdi1pZj0ic2VsZWN0ZWRJbnB1dFBsYXRmb3JtcyAmJiBzZWxlY3RlZElucHV0UGxhdGZvcm1zLmxlbmd0aCA+IDAiIGNsYXNzPSJwbGF0Zm9ybS1jb25maWdzIiB2LWxvYWRpbmc9ImxvYWRpbmdJbnB1dFBsYXRmb3JtcyIgZWxlbWVudC1sb2FkaW5nLXRleHQ9Iuato+WcqOWKoOi9veW5s+WPsOmFjee9ri4uLiI+CiAgICAgICAgICAgICAgICA8aDM+6L6T5YWl5bmz5Y+w6YWN572uPC9oMz4KICAgICAgICAgICAgICAgIDxlbC1jYXJkIHYtZm9yPSJwbGF0Zm9ybSBpbiBzZWxlY3RlZElucHV0UGxhdGZvcm1zIiA6a2V5PSJwbGF0Zm9ybS5wbGF0Zm9ybV9pZCIKICAgICAgICAgICAgICAgICAgICBjbGFzcz0icGxhdGZvcm0tY2FyZCI+CiAgICAgICAgICAgICAgICAgICAgPGRpdiBzbG90PSJoZWFkZXIiPgogICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57eyBwbGF0Zm9ybS5wbGF0Zm9ybV9uYW1lIH19PC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHYtZm9yPSJmaWVsZCBpbiBwbGF0Zm9ybS5maWVsZHMiIDprZXk9ImZpZWxkLmZpZWxkX25hbWUiIDpsYWJlbD0iZmllbGQubGFiZWwiCiAgICAgICAgICAgICAgICAgICAgICAgIDpwcm9wPSInaW5wdXRfcGxhdGZvcm1zX2RhdGEuJyArIHBsYXRmb3JtLnBsYXRmb3JtX2lkICsgJy4nICsgZmllbGQuZmllbGRfbmFtZSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxjb21wb25lbnQgOmlzPSJnZXRGaWVsZENvbXBvbmVudChmaWVsZC5maWVsZF90eXBlKSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uaW5wdXRfcGxhdGZvcm1zX2RhdGFbcGxhdGZvcm0ucGxhdGZvcm1faWRdW2ZpZWxkLmZpZWxkX25hbWVdIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgdi1iaW5kPSJnZXRGaWVsZFByb3BzKGZpZWxkKSI+PC9jb21wb25lbnQ+CiAgICAgICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaVsOaNruexu+WeiyIgOnByb3A9IidpbnB1dF9kYXRhX29wdGlvbnMuJyArIHBsYXRmb3JtLnBsYXRmb3JtX2lkIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iZGF0YS1vcHRpb25zLWNvbnRhaW5lciI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtY2hlY2tib3gtZ3JvdXAgdi1tb2RlbD0iZm9ybS5pbnB1dF9kYXRhX29wdGlvbnNbcGxhdGZvcm0ucGxhdGZvcm1faWRdIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzPSJjaGVja2JveC1ncm91cCI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLWNoZWNrYm94IHYtZm9yPSJvcHRpb24gaW4gcGxhdGZvcm0ub3B0aW9ucyIgOmtleT0ib3B0aW9uLm9wdGlvbl9rZXkiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDpsYWJlbD0ib3B0aW9uLm9wdGlvbl9rZXkiIGNsYXNzPSJjaGVja2JveC1pdGVtIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3sgb3B0aW9uLm9wdGlvbl9uYW1lIH19CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9lbC1jaGVja2JveD4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtY2hlY2tib3gtZ3JvdXA+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIHNpemU9InNtYWxsIiBpY29uPSJlbC1pY29uLXBsdXMiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQGNsaWNrPSJzaG93QWRkT3B0aW9uRGlhbG9nKHBsYXRmb3JtLnBsYXRmb3JtX2lkKSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5paw5aKe5pWw5o2u57G75Z6LCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaViOaenOWPguaVsOmFjee9riI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImVmZmVjdC1wYXJhbXMtY29udGFpbmVyIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgdi1pZj0iZ2V0QXZhaWxhYmxlRWZmZWN0UGFyYW1zRm9yUGxhdGZvcm0ocGxhdGZvcm0ucGxhdGZvcm1faWQpLmxlbmd0aCA9PT0gMCIgY2xhc3M9Im5vLWVmZmVjdC1wYXJhbXMtdGlwIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtYWxlcnQKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9IuivpeW5s+WPsOaaguaXoOWPr+eUqOeahOaViOaenOWPguaVsCIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT0iaW5mbyIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOmNsb3NhYmxlPSJmYWxzZSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2hvdy1pY29uPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtYWxlcnQ+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1jaGVja2JveC1ncm91cCB2LWVsc2Ugdi1tb2RlbD0iZm9ybS5pbnB1dF9lZmZlY3RfcGFyYW1zW3BsYXRmb3JtLnBsYXRmb3JtX2lkXSIgQGNoYW5nZT0iaGFuZGxlRWZmZWN0UGFyYW1zQ2hhbmdlKHBsYXRmb3JtLnBsYXRmb3JtX2lkKSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLWNoZWNrYm94IHYtZm9yPSJwYXJhbSBpbiBnZXRBdmFpbGFibGVFZmZlY3RQYXJhbXNGb3JQbGF0Zm9ybShwbGF0Zm9ybS5wbGF0Zm9ybV9pZCkiIDprZXk9InBhcmFtLmVmZmVjdF9wYXJhbV9jb2RlIiA6bGFiZWw9InBhcmFtLmVmZmVjdF9wYXJhbV9jb2RlIiBjbGFzcz0iZWZmZWN0LXBhcmFtLWNoZWNrYm94Ij4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3sgcGFyYW0uZWZmZWN0X3BhcmFtX25hbWUgfX0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2VsLWNoZWNrYm94PgogICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9lbC1jaGVja2JveC1ncm91cD4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiB2LWlmPSJmb3JtLmlucHV0X2VmZmVjdF9wYXJhbXNbcGxhdGZvcm0ucGxhdGZvcm1faWRdICYmIGZvcm0uaW5wdXRfZWZmZWN0X3BhcmFtc1twbGF0Zm9ybS5wbGF0Zm9ybV9pZF0ubGVuZ3RoID4gMCIgY2xhc3M9ImVmZmVjdC1wYXJhbXMtdGFibGUiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoND7lj4LmlbDphY3nva7or6bmg4U8L2g0PgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC10YWJsZSA6ZGF0YT0iZ2V0RWZmZWN0UGFyYW1zVGFibGVEYXRhKHBsYXRmb3JtLnBsYXRmb3JtX2lkKSIgYm9yZGVyPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9ImVmZmVjdF9wYXJhbV9uYW1lIiBsYWJlbD0i5Y+C5pWw5ZCN56ewIiB3aWR0aD0iMTIwIj48L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJjb25maWd1cmVkX2V2YWx1YXRpb25fZGF5cyIgd2lkdGg9IjIwMCI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGVtcGxhdGUgc2xvdD0iaGVhZGVyIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtdG9vbHRpcCBlZmZlY3Q9ImRhcmsiIHBsYWNlbWVudD0idG9wIiBjb250ZW50PSLns7vnu5/kvJrojrflj5blj5HluIPml7bpl7TlnKgnVC3ln7rnur8n6IyD5Zu05YaF77yM5LiU5bey5ruh6Laz5ZCE5Y+C5pWw55qEVGlqIOWAvOeahOagt+acrOaAu+aVsOmHj+OAgiI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJ0YWJsZS1oZWFkZXItd2l0aC10b29sdGlwIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICrmlYjmnpzlrp7njrDlpKnmlbAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLXF1ZXN0aW9uIj48L2k+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2VsLXRvb2x0aXA+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6cHJvcD0iYGlucHV0X2VmZmVjdF9wYXJhbXNfY29uZmlnLiR7cGxhdGZvcm0ucGxhdGZvcm1faWR9LiR7c2NvcGUucm93LmVmZmVjdF9wYXJhbV9jb2RlfS5jb25maWd1cmVkX2V2YWx1YXRpb25fZGF5c2AiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPSJtYXJnaW4tYm90dG9tOiAwOyI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1pbnB1dCAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9InNjb3BlLnJvdy5jb25maWd1cmVkX2V2YWx1YXRpb25fZGF5cyIgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i5aaC77yaMyw1LDEwIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQGNoYW5nZT0idXBkYXRlRWZmZWN0UGFyYW1Db25maWcocGxhdGZvcm0ucGxhdGZvcm1faWQsIHNjb3BlLnJvdy5lZmZlY3RfcGFyYW1fY29kZSwgJ2NvbmZpZ3VyZWRfZXZhbHVhdGlvbl9kYXlzJywgc2NvcGUucm93LmNvbmZpZ3VyZWRfZXZhbHVhdGlvbl9kYXlzKSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtaW5wdXQ+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJkZWZhdWx0X2Jhc2VsaW5lX21lYW4iIHdpZHRoPSIyMDAiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Q9ImhlYWRlciI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLXRvb2x0aXAgZWZmZWN0PSJkYXJrIiBwbGFjZW1lbnQ9InRvcCIgY29udGVudD0izrxp5piv6YCJ5Y+W55qE5qC35pys6YeP55qE5pWI5p6c5pWw5o2u6YeM77yM6K+l5Y+C5pWw55qE5bmz5Z2H5YC844CC5b2T5qC35pys6YeP5bCR5LqO5oKo6K6+572u55qE5pyA5L2O5qC35pys6YeP55qE6ZiI5YC85pe277yM5bCG5LiN5Lya5qC55o2u5a6e6ZmF5qC35pys5Y676K6h566Xzrxp77yM6ICM5piv5Lya5L2/55So5oKo6L6T5YWl55qE57y655yB54q25oCB5LiL55qEzrxp44CCIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InRhYmxlLWhlYWRlci13aXRoLXRvb2x0aXAiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKuW5s+Wdh+WAvAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tcXVlc3Rpb24iPjwvaT4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtdG9vbHRpcD4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDpwcm9wPSJgaW5wdXRfZWZmZWN0X3BhcmFtc19jb25maWcuJHtwbGF0Zm9ybS5wbGF0Zm9ybV9pZH0uJHtzY29wZS5yb3cuZWZmZWN0X3BhcmFtX2NvZGV9LmRlZmF1bHRfYmFzZWxpbmVfbWVhbmAiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPSJtYXJnaW4tYm90dG9tOiAwOyI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0ic2NvcGUucm93LmRlZmF1bHRfYmFzZWxpbmVfbWVhbiIgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i5aaC77yaMCIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEBjaGFuZ2U9InVwZGF0ZUVmZmVjdFBhcmFtQ29uZmlnKHBsYXRmb3JtLnBsYXRmb3JtX2lkLCBzY29wZS5yb3cuZWZmZWN0X3BhcmFtX2NvZGUsICdkZWZhdWx0X2Jhc2VsaW5lX21lYW4nLCBzY29wZS5yb3cuZGVmYXVsdF9iYXNlbGluZV9tZWFuKSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtaW5wdXQ+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJkZWZhdWx0X2Jhc2VsaW5lX3N0ZGRldiIgd2lkdGg9IjIwMCI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGVtcGxhdGUgc2xvdD0iaGVhZGVyIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtdG9vbHRpcCBlZmZlY3Q9ImRhcmsiIHBsYWNlbWVudD0idG9wIiBjb250ZW50PSLPg2nmmK/pgInlj5bnmoTmoLfmnKzph4/nmoTmlYjmnpzmlbDmja7ph4zvvIzor6Xlj4LmlbDnmoTmoIflh4blt67jgILlvZPmoLfmnKzph4/lsJHkuo7mgqjorr7nva7nmoTmnIDkvY7moLfmnKzph4/nmoTpmIjlgLzml7bvvIzlsIbkuI3kvJrmoLnmja7lrp7pmYXmoLfmnKzljrvorqHnrpfPg2nvvIzogIzmmK/kvJrkvb/nlKjmgqjovpPlhaXnmoTnvLrnnIHnirbmgIHkuIvnmoTPg2njgIIiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0idGFibGUtaGVhZGVyLXdpdGgtdG9vbHRpcCI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAq5qCH5YeG5beuCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1xdWVzdGlvbiI+PC9pPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9lbC10b29sdGlwPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOnByb3A9ImBpbnB1dF9lZmZlY3RfcGFyYW1zX2NvbmZpZy4ke3BsYXRmb3JtLnBsYXRmb3JtX2lkfS4ke3Njb3BlLnJvdy5lZmZlY3RfcGFyYW1fY29kZX0uZGVmYXVsdF9iYXNlbGluZV9zdGRkZXZgIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT0ibWFyZ2luLWJvdHRvbTogMDsiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtaW5wdXQgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2LW1vZGVsPSJzY29wZS5yb3cuZGVmYXVsdF9iYXNlbGluZV9zdGRkZXYiIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9IuWmgu+8mjEiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBAY2hhbmdlPSJ1cGRhdGVFZmZlY3RQYXJhbUNvbmZpZyhwbGF0Zm9ybS5wbGF0Zm9ybV9pZCwgc2NvcGUucm93LmVmZmVjdF9wYXJhbV9jb2RlLCAnZGVmYXVsdF9iYXNlbGluZV9zdGRkZXYnLCBzY29wZS5yb3cuZGVmYXVsdF9iYXNlbGluZV9zdGRkZXYpIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9lbC1pbnB1dD4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtdGFibGU+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuihpeWFheS/oeaBryIKICAgICAgICAgICAgICAgICAgICAgICAgOnByb3A9IidpbnB1dF9wbGF0Zm9ybXNfZGF0YS4nICsgcGxhdGZvcm0ucGxhdGZvcm1faWQgKyAnLmFkZGl0aW9uYWxfSW5mb3JtYXRpb24nIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9ImZvcm0uaW5wdXRfcGxhdGZvcm1zX2RhdGFbcGxhdGZvcm0ucGxhdGZvcm1faWRdLmFkZGl0aW9uYWxfSW5mb3JtYXRpb24iCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIgOnJvd3M9IjUiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXooaXlhYXkv6Hmga8iPgogICAgICAgICAgICAgICAgICAgICAgICA8L2VsLWlucHV0PgogICAgICAgICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICAgICAgPC9lbC1jYXJkPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KCiAgICAgICAgPGRpdiB2LXNob3c9ImFjdGl2ZVN0ZXAgPT09IDMiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLpgInmi6novpPlh7rlubPlj7AiIHByb3A9Im91dHB1dF9wbGF0Zm9ybXMiPgogICAgICAgICAgICAgICAgPGRpdiB2LWlmPSIhZm9ybS5saW5rZWRfdGFza190eXBlX2NvZGUiIGNsYXNzPSJwbGF0Zm9ybS1zZWxlY3Rpb24tdGlwIj4KICAgICAgICAgICAgICAgICAgICA8ZWwtYWxlcnQKICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9Iuivt+WFiOWcqOesrOS4gOatpemAieaLqeS7u+WKoeexu+WeiyIKICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT0iaW5mbyIKICAgICAgICAgICAgICAgICAgICAgICAgOmNsb3NhYmxlPSJmYWxzZSIKICAgICAgICAgICAgICAgICAgICAgICAgc2hvdy1pY29uPgogICAgICAgICAgICAgICAgICAgIDwvZWwtYWxlcnQ+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgdi1lbHNlIGNsYXNzPSJwbGF0Zm9ybS1zZWxlY3Rpb24tY29udGFpbmVyIj4KICAgICAgICAgICAgICAgICAgICA8ZWwtY2hlY2tib3gtZ3JvdXAgdi1tb2RlbD0iZm9ybS5vdXRwdXRfcGxhdGZvcm1zIiBAY2hhbmdlPSJoYW5kbGVPdXRwdXRQbGF0Zm9ybUNoYW5nZSIKICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3M9InBsYXRmb3JtLWNoZWNrYm94LWdyb3VwIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGVsLWNoZWNrYm94IHYtZm9yPSJwbGF0Zm9ybSBpbiBhdmFpbGFibGVQbGF0Zm9ybXMiIDprZXk9InBsYXRmb3JtLnBsYXRmb3JtX2lkIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgOmxhYmVsPSJwbGF0Zm9ybS5wbGF0Zm9ybV9pZCIgY2xhc3M9InBsYXRmb3JtLWNoZWNrYm94LWl0ZW0iPgogICAgICAgICAgICAgICAgICAgICAgICAgICAge3sgcGxhdGZvcm0ucGxhdGZvcm1fbmFtZSB9fQogICAgICAgICAgICAgICAgICAgICAgICA8L2VsLWNoZWNrYm94PgogICAgICAgICAgICAgICAgICAgIDwvZWwtY2hlY2tib3gtZ3JvdXA+CiAgICAgICAgICAgICAgICAgICAgPGRpdiB2LWlmPSJsb2FkaW5nT3V0cHV0UGxhdGZvcm1zIiBjbGFzcz0ibG9hZGluZy10aXAiPgogICAgICAgICAgICAgICAgICAgICAgICA8ZWwtYWxlcnQKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPSLmraPlnKjliqDovb3lubPlj7DphY3nva7vvIzor7fnqI3lgJkuLi4iCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPSJpbmZvIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgOmNsb3NhYmxlPSJmYWxzZSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3ctaWNvbj4KICAgICAgICAgICAgICAgICAgICAgICAgPC9lbC1hbGVydD4KICAgICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgICA8ZGl2IHYtaWY9ImF2YWlsYWJsZVBsYXRmb3Jtcy5sZW5ndGggPT09IDAiIGNsYXNzPSJuby1wbGF0Zm9ybXMtdGlwIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGVsLWFsZXJ0CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT0i5b2T5YmN5Lu75Yqh57G75Z6L5rKh5pyJ5YWz6IGU55qE5bmz5Y+wIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT0id2FybmluZyIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIDpjbG9zYWJsZT0iZmFsc2UiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaG93LWljb24+CiAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtYWxlcnQ+CiAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICAgICAgICA8ZGl2IHYtaWY9InNlbGVjdGVkT3V0cHV0UGxhdGZvcm1zICYmIHNlbGVjdGVkT3V0cHV0UGxhdGZvcm1zLmxlbmd0aCA+IDAiIGNsYXNzPSJwbGF0Zm9ybS1jb25maWdzIiB2LWxvYWRpbmc9ImxvYWRpbmdPdXRwdXRQbGF0Zm9ybXMiIGVsZW1lbnQtbG9hZGluZy10ZXh0PSLmraPlnKjliqDovb3lubPlj7DphY3nva4uLi4iPgogICAgICAgICAgICAgICAgPGgzPui+k+WHuuW5s+WPsOmFjee9rjwvaDM+CiAgICAgICAgICAgICAgICA8ZWwtY2FyZCB2LWZvcj0icGxhdGZvcm0gaW4gc2VsZWN0ZWRPdXRwdXRQbGF0Zm9ybXMiIDprZXk9InBsYXRmb3JtLnBsYXRmb3JtX2lkIgogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJwbGF0Zm9ybS1jYXJkIj4KICAgICAgICAgICAgICAgICAgICA8ZGl2IHNsb3Q9ImhlYWRlciI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPnt7IHBsYXRmb3JtLnBsYXRmb3JtX25hbWUgfX08L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPC9kaXY+CgogICAgICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gdi1mb3I9ImZpZWxkIGluIHBsYXRmb3JtLmZpZWxkcyIgOmtleT0iZmllbGQuZmllbGRfbmFtZSIgOmxhYmVsPSJmaWVsZC5sYWJlbCIKICAgICAgICAgICAgICAgICAgICAgICAgOnByb3A9IidvdXRwdXRfcGxhdGZvcm1zX2RhdGEuJyArIHBsYXRmb3JtLnBsYXRmb3JtX2lkICsgJy4nICsgZmllbGQuZmllbGRfbmFtZSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxjb21wb25lbnQgOmlzPSJnZXRGaWVsZENvbXBvbmVudChmaWVsZC5maWVsZF90eXBlKSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0ub3V0cHV0X3BsYXRmb3Jtc19kYXRhW3BsYXRmb3JtLnBsYXRmb3JtX2lkXVtmaWVsZC5maWVsZF9uYW1lXSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHYtYmluZD0iZ2V0RmllbGRQcm9wcyhmaWVsZCkiPjwvY29tcG9uZW50PgogICAgICAgICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgICAgICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmlbDmja7lhoXlrrkiIDpwcm9wPSInb3V0cHV0X2RhdGFfb3B0aW9ucy4nICsgcGxhdGZvcm0ucGxhdGZvcm1faWQiPgogICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJkYXRhLW9wdGlvbnMtY29udGFpbmVyIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1jaGVja2JveC1ncm91cCB2LW1vZGVsPSJmb3JtLm91dHB1dF9kYXRhX29wdGlvbnNbcGxhdGZvcm0ucGxhdGZvcm1faWRdIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzPSJjaGVja2JveC1ncm91cCI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLWNoZWNrYm94IHYtZm9yPSJvcHRpb24gaW4gcGxhdGZvcm0ub3B0aW9ucyIgOmtleT0ib3B0aW9uLm9wdGlvbl9rZXkiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDpsYWJlbD0ib3B0aW9uLm9wdGlvbl9rZXkiIGNsYXNzPSJjaGVja2JveC1pdGVtIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3sgb3B0aW9uLm9wdGlvbl9uYW1lIH19CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9lbC1jaGVja2JveD4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtY2hlY2tib3gtZ3JvdXA+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIHNpemU9InNtYWxsIiBpY29uPSJlbC1pY29uLXBsdXMiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQGNsaWNrPSJzaG93QWRkT3B0aW9uRGlhbG9nKHBsYXRmb3JtLnBsYXRmb3JtX2lkKSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5paw5aKe5pWw5o2u5YaF5a65CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CgoKCiAgICAgICAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5aSa5qih5oCB5YaF5a65IiA6cHJvcD0iJ3BsYXRmb3JtX21vZGFsaXRpZXMuJyArIHBsYXRmb3JtLnBsYXRmb3JtX2lkIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibW9kYWxpdHktc2VsZWN0aW9uLWNvbnRhaW5lciI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzZWxlY3Rpb24tcm93Ij4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9InRlbXBNb2RhbGl0eVNlbGVjdGlvbltwbGF0Zm9ybS5wbGF0Zm9ybV9pZF0iCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6nmqKHmgIHnsbvlnosiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMzAwcHg7IgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBAY2hhbmdlPSJoYW5kbGVNb2RhbGl0eVNlbGVjdChwbGF0Zm9ybS5wbGF0Zm9ybV9pZCwgJGV2ZW50KSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHYtZm9yPSJtb2RhbGl0eSBpbiBNT0RBTElUWV9UWVBFUyIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDprZXk9Im1vZGFsaXR5IgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOmxhYmVsPSJtb2RhbGl0eSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDp2YWx1ZT0ibW9kYWxpdHkiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ZGlzYWJsZWQ9ImZvcm0ucGxhdGZvcm1fbW9kYWxpdGllc1twbGF0Zm9ybS5wbGF0Zm9ybV9pZF0gJiYgZm9ybS5wbGF0Zm9ybV9tb2RhbGl0aWVzW3BsYXRmb3JtLnBsYXRmb3JtX2lkXS5pbmNsdWRlcyhtb2RhbGl0eSkiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzZWxlY3RlZC10YWdzIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLXRhZwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdi1mb3I9Im1vZGFsaXR5IGluIGZvcm0ucGxhdGZvcm1fbW9kYWxpdGllc1twbGF0Zm9ybS5wbGF0Zm9ybV9pZF0gfHwgW10iCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6a2V5PSJtb2RhbGl0eSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsb3NhYmxlCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBAY2xvc2U9InJlbW92ZU1vZGFsaXR5KHBsYXRmb3JtLnBsYXRmb3JtX2lkLCBtb2RhbGl0eSkiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT0ibWFyZ2luLXJpZ2h0OiA4cHg7IG1hcmdpbi1ib3R0b206IDhweDsiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3sgbW9kYWxpdHkgfX0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9lbC10YWc+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWPkeW4g+W9ouaAgSIgOnByb3A9IidwbGF0Zm9ybV9wdWJsaXNoX2Zvcm1zLicgKyBwbGF0Zm9ybS5wbGF0Zm9ybV9pZCI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InB1Ymxpc2gtZm9ybS1zZWxlY3Rpb24tY29udGFpbmVyIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InNlbGVjdGlvbi1yb3ciPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5wbGF0Zm9ybV9wdWJsaXNoX2Zvcm1zW3BsYXRmb3JtLnBsYXRmb3JtX2lkXSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeWPkeW4g+W9ouaAgSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAzMDBweDsiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2LWZvcj0icHVibGlzaEZvcm0gaW4gZ2V0QXZhaWxhYmxlUHVibGlzaEZvcm1zKHBsYXRmb3JtKSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDprZXk9InB1Ymxpc2hGb3JtIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOmxhYmVsPSJwdWJsaXNoRm9ybSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDp2YWx1ZT0icHVibGlzaEZvcm0iPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzZWxlY3RlZC10YWdzIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLXRhZwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdi1pZj0iZm9ybS5wbGF0Zm9ybV9wdWJsaXNoX2Zvcm1zW3BsYXRmb3JtLnBsYXRmb3JtX2lkXSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsb3NhYmxlCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBAY2xvc2U9InJlbW92ZVB1Ymxpc2hGb3JtKHBsYXRmb3JtLnBsYXRmb3JtX2lkKSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPSJtYXJnaW4tbGVmdDogOHB4OyI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7eyBmb3JtLnBsYXRmb3JtX3B1Ymxpc2hfZm9ybXNbcGxhdGZvcm0ucGxhdGZvcm1faWRdIH19CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtdGFnPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgICAgICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLooaXlhYXkv6Hmga8iCiAgICAgICAgICAgICAgICAgICAgICAgIDpwcm9wPSInb3V0cHV0X3BsYXRmb3Jtc19kYXRhLicgKyBwbGF0Zm9ybS5wbGF0Zm9ybV9pZCArICcuYWRkaXRpb25hbF9JbmZvcm1hdGlvbiciPgogICAgICAgICAgICAgICAgICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybS5vdXRwdXRfcGxhdGZvcm1zX2RhdGFbcGxhdGZvcm0ucGxhdGZvcm1faWRdLmFkZGl0aW9uYWxfSW5mb3JtYXRpb24iCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIgOnJvd3M9IjUiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXooaXlhYXkv6Hmga8iPgogICAgICAgICAgICAgICAgICAgICAgICA8L2VsLWlucHV0PgogICAgICAgICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICAgICAgPC9lbC1jYXJkPgogICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaooeaAgSIgcHJvcD0ibW9kYWxpdHkiPgogICAgICAgICAgICAgICAgPGVsLXNlbGVjdCB2LW1vZGVsPSJmb3JtLm1vZGFsaXR5IiBwbGFjZWhvbGRlcj0i6K+36YCJ5oup5qih5oCBIj4KICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIG1vZGFsaXR5T3B0aW9ucyIKICAgICAgICAgICAgICAgICAgICAgICAgOmtleT0iaXRlbS5kaWN0X25hbWUiCiAgICAgICAgICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5kaWN0X25hbWUiCiAgICAgICAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iaXRlbS5kaWN0X25hbWUiPgogICAgICAgICAgICAgICAgICAgIDwvZWwtb3B0aW9uPgogICAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDwvZGl2PgoKICAgICAgICA8ZGl2IHYtc2hvdz0iYWN0aXZlU3RlcCA9PT0gNCI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9Iui/kOihjOmikeeOhyIgcHJvcD0ic2NlbmVfcnVubmluZ19mcmVxdWVuY3kiPgogICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogZmxleDsgZ2FwOiAxMHB4OyBhbGlnbi1pdGVtczogY2VudGVyOyI+CiAgICAgICAgICAgICAgICAgICAgPGVsLWlucHV0LW51bWJlcgogICAgICAgICAgICAgICAgICAgICAgICB2LW1vZGVsPSJmcmVxdWVuY3lWYWx1ZSIKICAgICAgICAgICAgICAgICAgICAgICAgOm1pbj0iZ2V0TWluVmFsdWUoKSIKICAgICAgICAgICAgICAgICAgICAgICAgOm1heD0iZ2V0TWF4VmFsdWUoKSIKICAgICAgICAgICAgICAgICAgICAgICAgOnN0ZXA9ImdldFN0ZXAoKSIKICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeaVsOWAvCIKICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxNTBweCIKICAgICAgICAgICAgICAgICAgICAgICAgQGNoYW5nZT0iaGFuZGxlRnJlcXVlbmN5VmFsdWVDaGFuZ2UiPgogICAgICAgICAgICAgICAgICAgIDwvZWwtaW5wdXQtbnVtYmVyPgogICAgICAgICAgICAgICAgICAgIDxlbC1zZWxlY3QgCiAgICAgICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZyZXF1ZW5jeVVuaXQiIAogICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup5Y2V5L2NIgogICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEyMHB4IgogICAgICAgICAgICAgICAgICAgICAgICBAY2hhbmdlPSJoYW5kbGVGcmVxdWVuY3lVbml0Q2hhbmdlIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5YiG6ZKfIiB2YWx1ZT0ibWludXRlcyI+PC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuWwj+aXtiIgdmFsdWU9ImhvdXJzIj48L2VsLW9wdGlvbj4KICAgICAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5aSpIiB2YWx1ZT0iZGF5cyI+PC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjOTA5Mzk5OyBmb250LXNpemU6IDEycHg7Ij4KICAgICAgICAgICAgICAgICAgICAgICAgKOacgOWwj+mXtOmalDMw5YiG6ZKfKQogICAgICAgICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgPCEtLSA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLov5DooYzml7bpl7QiIHByb3A9InRpbWVfY29uZmlnIj4KICAgICAgICAgICAgICAgIDxlbC1yb3c+CiAgICAgICAgICAgICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iOCI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxlbC10aW1lLXBpY2tlcgogICAgICAgICAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5ob3VyIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybWF0PSJISDowMCIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIDpwaWNrZXItb3B0aW9ucz0iewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGFibGVSYW5nZTogJzAwOjAwOjAwIC0gMjM6MDA6MDAnLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvcm1hdDogJ0hIOjAwJwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6nov5DooYzml7bpl7QiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiPgogICAgICAgICAgICAgICAgICAgICAgICA8L2VsLXRpbWUtcGlja2VyPgogICAgICAgICAgICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjgiIHYtaWY9ImZvcm0uc2NlbmVfcnVubmluZ19mcmVxdWVuY3kgPT09ICfmr4/mnIjkuIDmrKEnIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGVsLXNlbGVjdCB2LW1vZGVsPSJmb3JtLmRheSIgcGxhY2Vob2xkZXI9Iuivt+mAieaLqei/kOihjOaXpeacnyIgc3R5bGU9IndpZHRoOiAxMDAlIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2LWZvcj0iZGF5IGluIDMxIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDprZXk9ImRheSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6bGFiZWw9ImAke2RheX3ml6VgIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iZGF5Ij4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtb3B0aW9uPgogICAgICAgICAgICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgICAgICAgICAgICA8ZWwtY29sIDpzcGFuPSI4IiB2LWlmPSJbJ+avj+WRqOS4gOasoScsICfmr4/kuKTlkajkuIDmrKEnXS5pbmNsdWRlcyhmb3JtLnNjZW5lX3J1bm5pbmdfZnJlcXVlbmN5KSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0iZm9ybS53ZWVrcyIgcGxhY2Vob2xkZXI9Iuivt+mAieaLqei/kOihjOaYn+acnyIgc3R5bGU9IndpZHRoOiAxMDAlIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaYn+acn+S4gCIgdmFsdWU9IjEiPjwvZWwtb3B0aW9uPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5pif5pyf5LqMIiB2YWx1ZT0iMiI+PC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmmJ/mnJ/kuIkiIHZhbHVlPSIzIj48L2VsLW9wdGlvbj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaYn+acn+WbmyIgdmFsdWU9IjQiPjwvZWwtb3B0aW9uPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5pif5pyf5LqUIiB2YWx1ZT0iNSI+PC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmmJ/mnJ/lha0iIHZhbHVlPSI2Ij48L2VsLW9wdGlvbj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaYn+acn+aXpSIgdmFsdWU9IjAiPjwvZWwtb3B0aW9uPgogICAgICAgICAgICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgICAgICAgIDwvZWwtcm93PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4gLS0+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuS4quaAp+WMlui/m+WMluabtOaWsOmikeeOhyIgcHJvcD0ic3RvcmVkX3N0cmF0ZWd5X3JlZnJlc2hfZGF5cyI+CiAgICAgICAgICAgICAgICA8ZWwtaW5wdXQtbnVtYmVyCiAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5zdG9yZWRfc3RyYXRlZ3lfcmVmcmVzaF9kYXlzIgogICAgICAgICAgICAgICAgICAgIDptaW49IjAiCiAgICAgICAgICAgICAgICAgICAgOm1heD0iMzY1IgogICAgICAgICAgICAgICAgICAgIDpzdGVwPSIxIgogICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlpKnmlbAiCiAgICAgICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAyMDBweCI+CiAgICAgICAgICAgICAgICA8L2VsLWlucHV0LW51bWJlcj4KICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJtYXJnaW4tbGVmdDogOHB4OyBjb2xvcjogIzkwOTM5OTsiPuWkqTwvc3Bhbj4KICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJtYXJnaW4tbGVmdDogMTZweDsgY29sb3I6ICM5MDkzOTk7IGZvbnQtc2l6ZTogMTJweDsiPuW7uuiuruaCqOiuvuS4ujA8L3NwYW4+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSJBSeiHquihjOaOoue0oumikeeOhyIgcHJvcD0iZXhwbG9yZV9zdHJhdGVneV90cmlnZ2VyX2RheXMiPgogICAgICAgICAgICAgICAgPGVsLWlucHV0LW51bWJlcgogICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uZXhwbG9yZV9zdHJhdGVneV90cmlnZ2VyX2RheXMiCiAgICAgICAgICAgICAgICAgICAgOm1pbj0iMSIKICAgICAgICAgICAgICAgICAgICA6bWF4PSIzNjUiCiAgICAgICAgICAgICAgICAgICAgOnN0ZXA9IjEiCiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeWkqeaVsCIKICAgICAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDIwMHB4Ij4KICAgICAgICAgICAgICAgIDwvZWwtaW5wdXQtbnVtYmVyPgogICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9Im1hcmdpbi1sZWZ0OiA4cHg7IGNvbG9yOiAjOTA5Mzk5OyI+5aSpPC9zcGFuPgogICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9Im1hcmdpbi1sZWZ0OiAxNnB4OyBjb2xvcjogIzkwOTM5OTsgZm9udC1zaXplOiAxMnB4OyI+55uu5YmN5oiR5Lus55qE5o6i57Si5qih5byP5pqC5pyq5LiK57q/77yM5bu66K6u5oKo5YWI5bCGWuWAvOiuvuS4ujM2NeWkqTwvc3Bhbj4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IkFJ5o+Q56S66K+NIiBwcm9wPSJ1cGRhdGVkX3Byb21wdCI+CiAgICAgICAgICAgICAgICA8ZWwtaW5wdXQgdHlwZT0idGV4dGFyZWEiIHYtbW9kZWw9ImZvcm0udXBkYXRlZF9wcm9tcHQiIHBsYWNlaG9sZGVyPSLor7fovpPlhaVBSeaPkOekuuivjSIgOnJvd3M9IjEwIj48L2VsLWlucHV0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2Rpdj4KCiAgICAgICAgPGVsLWZvcm0taXRlbSBjbGFzcz0ibmF2aWdhdGlvbi1idXR0b25zIj4KICAgICAgICAgICAgPGVsLWJ1dHRvbiB2LWlmPSJhY3RpdmVTdGVwID4gMCIgQGNsaWNrPSJwcmV2U3RlcCI+5LiK5LiA5q2lPC9lbC1idXR0b24+CiAgICAgICAgICAgIDxlbC1idXR0b24gCiAgICAgICAgICAgICAgICB2LWlmPSJhY3RpdmVTdGVwIDwgNCIgCiAgICAgICAgICAgICAgICB0eXBlPSJwcmltYXJ5IiAKICAgICAgICAgICAgICAgIEBjbGljaz0ibmV4dFN0ZXAiCiAgICAgICAgICAgICAgICA6ZGlzYWJsZWQ9ImlzUGxhdGZvcm1Db25maWdMb2FkaW5nIgogICAgICAgICAgICAgICAgOmxvYWRpbmc9ImlzUGxhdGZvcm1Db25maWdMb2FkaW5nIj4KICAgICAgICAgICAgICAgIHt7IGlzUGxhdGZvcm1Db25maWdMb2FkaW5nID8gJ+mFjee9ruWKoOi9veS4rS4uLicgOiAn5LiL5LiA5q2lJyB9fQogICAgICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgPGVsLWJ1dHRvbiB2LWlmPSJhY3RpdmVTdGVwID09PSA0IiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9InN1Ym1pdEZvcm0iPuS/neWtmDwvZWwtYnV0dG9uPgogICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPC9lbC1mb3JtPgoKICAgIDxlbC1kaWFsb2cgdGl0bGU9IuaWsOWinuaVsOaNruWGheWuuSIgOnZpc2libGUuc3luYz0iYWRkT3B0aW9uRGlhbG9nVmlzaWJsZSIgd2lkdGg9IjUwMHB4Ij4KICAgICAgICA8ZWwtZm9ybSByZWY9Im9wdGlvbkZvcm0iIDptb2RlbD0ibmV3T3B0aW9uRm9ybSIgOnJ1bGVzPSJvcHRpb25SdWxlcyIgbGFiZWwtd2lkdGg9IjEyMHB4Ij4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5YaF5a655ZCN56ewIiBwcm9wPSJvcHRpb25fbmFtZSI+CiAgICAgICAgICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0ibmV3T3B0aW9uRm9ybS5vcHRpb25fbmFtZSIgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeWGheWuueWQjeensCI+PC9lbC1pbnB1dD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWGheWuueagh+ivhiIgcHJvcD0ib3B0aW9uX2tleSI+CiAgICAgICAgICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0ibmV3T3B0aW9uRm9ybS5vcHRpb25fa2V5IiBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5YaF5a655qCH6K+G77yI6Iux5paH77yJIj48L2VsLWlucHV0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWZvcm0+CiAgICAgICAgPGRpdiBzbG90PSJmb290ZXIiIGNsYXNzPSJkaWFsb2ctZm9vdGVyIj4KICAgICAgICAgICAgPGVsLWJ1dHRvbiBAY2xpY2s9ImFkZE9wdGlvbkRpYWxvZ1Zpc2libGUgPSBmYWxzZSI+5Y+W5raIPC9lbC1idXR0b24+CiAgICAgICAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgQGNsaWNrPSJhZGROZXdPcHRpb24iIDpsb2FkaW5nPSJhZGRpbmdPcHRpb24iPuehruWumjwvZWwtYnV0dG9uPgogICAgICAgIDwvZGl2PgogICAgPC9lbC1kaWFsb2c+CgogICAgPGVsLWRpYWxvZyB0aXRsZT0i5paw5aKe6aG555uu57uEIiA6dmlzaWJsZS5zeW5jPSJhZGRQcm9qZWN0RGlhbG9nVmlzaWJsZSIgd2lkdGg9IjUwMHB4Ij4KICAgICAgICA8ZWwtZm9ybSByZWY9InByb2plY3RGb3JtIiA6bW9kZWw9Im5ld1Byb2plY3RGb3JtIiA6cnVsZXM9InByb2plY3RSdWxlcyIgbGFiZWwtd2lkdGg9IjEyMHB4Ij4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6aG555uu5ZCN56ewIiBwcm9wPSJwcm9qZWN0X25hbWUiPgogICAgICAgICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9Im5ld1Byb2plY3RGb3JtLnByb2plY3RfbmFtZSIgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpemhueebruWQjeensCI+PC9lbC1pbnB1dD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1mb3JtPgogICAgICAgIDxkaXYgc2xvdD0iZm9vdGVyIiBjbGFzcz0iZGlhbG9nLWZvb3RlciI+CiAgICAgICAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJhZGRQcm9qZWN0RGlhbG9nVmlzaWJsZSA9IGZhbHNlIj7lj5bmtog8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImFkZE5ld1Byb2plY3QiIDpsb2FkaW5nPSJhZGRpbmdQcm9qZWN0Ij7noa7lrpo8L2VsLWJ1dHRvbj4KICAgICAgICA8L2Rpdj4KICAgIDwvZWwtZGlhbG9nPgoKICAgIDxlbC1kaWFsb2cgdGl0bGU9IuaWsOWinuS7u+WKoeexu+WeiyIgOnZpc2libGUuc3luYz0iYWRkVGFza1R5cGVEaWFsb2dWaXNpYmxlIiB3aWR0aD0iNjAwcHgiPgogICAgICAgIDxlbC1mb3JtIHJlZj0idGFza1R5cGVGb3JtIiA6bW9kZWw9Im5ld1Rhc2tUeXBlRm9ybSIgOnJ1bGVzPSJ0YXNrVHlwZVJ1bGVzIiBsYWJlbC13aWR0aD0iMTQwcHgiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLku7vliqHnsbvlnovlkI3np7AiIHByb3A9InRhc2tfdHlwZV9uYW1lIj4KICAgICAgICAgICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJuZXdUYXNrVHlwZUZvcm0udGFza190eXBlX25hbWUiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXku7vliqHnsbvlnovlkI3np7AiPjwvZWwtaW5wdXQ+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLku7vliqHnsbvlnovmj4/ov7AiIHByb3A9InRhc2tfdHlwZV9kZXNjcmlwdGlvbiI+CiAgICAgICAgICAgICAgICA8ZWwtaW5wdXQgdHlwZT0idGV4dGFyZWEiIHYtbW9kZWw9Im5ld1Rhc2tUeXBlRm9ybS50YXNrX3R5cGVfZGVzY3JpcHRpb24iIHBsYWNlaG9sZGVyPSLor7fovpPlhaXku7vliqHnsbvlnovmj4/ov7AiIDpyb3dzPSIzIj48L2VsLWlucHV0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5YWz6IGU5bmz5Y+wIiBwcm9wPSJsaW5rZWRfcGxhdGZvcm1faWRzIj4KICAgICAgICAgICAgICAgIDxlbC1zZWxlY3QgCiAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0ic2VsZWN0ZWRQbGF0Zm9ybUlkcyIgCiAgICAgICAgICAgICAgICAgICAgbXVsdGlwbGUgCiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeWFs+iBlOW5s+WPsCIKICAgICAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgICAgICAgICAgQGNoYW5nZT0iaGFuZGxlUGxhdGZvcm1TZWxlY3RDaGFuZ2UiPgogICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgICAgICAgdi1mb3I9InBsYXRmb3JtIGluIHBsYXRmb3JtcyIKICAgICAgICAgICAgICAgICAgICAgICAgOmtleT0icGxhdGZvcm0ucGxhdGZvcm1faWQiCiAgICAgICAgICAgICAgICAgICAgICAgIDpsYWJlbD0icGxhdGZvcm0ucGxhdGZvcm1fbmFtZSIKICAgICAgICAgICAgICAgICAgICAgICAgOnZhbHVlPSJwbGF0Zm9ybS5wbGF0Zm9ybV9pZCI+CiAgICAgICAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaOqOiNkOaViOaenOWPguaVsCIgcHJvcD0icmVjb21tZW5kZWRfZWZmZWN0X3BhcmFtX2NvZGVzIj4KICAgICAgICAgICAgICAgIDxlbC1zZWxlY3QgCiAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0ic2VsZWN0ZWRFZmZlY3RQYXJhbXMiIAogICAgICAgICAgICAgICAgICAgIG11bHRpcGxlIAogICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6nmjqjojZDmlYjmnpzlj4LmlbAiCiAgICAgICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgICAgICAgICAgIEBjaGFuZ2U9ImhhbmRsZUVmZmVjdFBhcmFtc1NlbGVjdENoYW5nZSI+CiAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICAgICAgICAgICAgICB2LWZvcj0icGFyYW0gaW4gYXZhaWxhYmxlRWZmZWN0UGFyYW1zRm9yVGFza1R5cGUiCiAgICAgICAgICAgICAgICAgICAgICAgIDprZXk9InBhcmFtLmVmZmVjdF9wYXJhbV9jb2RlIgogICAgICAgICAgICAgICAgICAgICAgICA6bGFiZWw9ImAke3BhcmFtLmVmZmVjdF9wYXJhbV9uYW1lfSAoJHtwYXJhbS5lZmZlY3RfcGFyYW1fY29kZX0pYCIKICAgICAgICAgICAgICAgICAgICAgICAgOnZhbHVlPSJwYXJhbS5lZmZlY3RfcGFyYW1fbmFtZSI+CiAgICAgICAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgICAgIDxkaXYgdi1pZj0iYXZhaWxhYmxlRWZmZWN0UGFyYW1zRm9yVGFza1R5cGUubGVuZ3RoID09PSAwICYmIHNlbGVjdGVkUGxhdGZvcm1JZHMubGVuZ3RoID4gMCIgc3R5bGU9Im1hcmdpbi10b3A6IDhweDsgY29sb3I6ICM5MDkzOTk7IGZvbnQtc2l6ZTogMTJweDsiPgogICAgICAgICAgICAgICAgICAgIOaJgOmAieW5s+WPsOaaguaXoOWPr+eUqOeahOaViOaenOWPguaVsAogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2IHYtaWY9InNlbGVjdGVkUGxhdGZvcm1JZHMubGVuZ3RoID09PSAwIiBzdHlsZT0ibWFyZ2luLXRvcDogOHB4OyBjb2xvcjogIzkwOTM5OTsgZm9udC1zaXplOiAxMnB4OyI+CiAgICAgICAgICAgICAgICAgICAg6K+35YWI6YCJ5oup5YWz6IGU5bmz5Y+wCiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWPguaVsOWFs+ezu+ivtOaYjiIgcHJvcD0iZWZmZWN0X3BhcmFtX3JlbGF0aW9uc2hpcHNfbm90ZSI+CiAgICAgICAgICAgICAgICA8ZWwtaW5wdXQgdHlwZT0idGV4dGFyZWEiIHYtbW9kZWw9Im5ld1Rhc2tUeXBlRm9ybS5lZmZlY3RfcGFyYW1fcmVsYXRpb25zaGlwc19ub3RlIiBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5ZCE5o6o6I2Q5Y+C5pWw5LmL6Ze055qE6YC76L6R5YWz57O76K+05piOIiA6cm93cz0iMyI+PC9lbC1pbnB1dD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDwhLS0gPGVsLWZvcm0taXRlbSBsYWJlbD0i5piv5ZCm6ZyA6KaB5aSW6YOo5YaF5a65IiBwcm9wPSJpc19jb250ZW50X2Zyb21fZXh0ZXJuYWwiPgogICAgICAgICAgICAgICAgPGVsLXNlbGVjdCB2LW1vZGVsPSJuZXdUYXNrVHlwZUZvcm0uaXNfY29udGVudF9mcm9tX2V4dGVybmFsIiBwbGFjZWhvbGRlcj0i6K+36YCJ5oupIj4KICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmmK8iIHZhbHVlPSIxIj48L2VsLW9wdGlvbj4KICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLlkKYiIHZhbHVlPSIwIj48L2VsLW9wdGlvbj4KICAgICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5piv5ZCm6ZyA6KaB5Y+M6L655YGP5aW9IiBwcm9wPSJpc19iaWxhdGVyYWxfcHJlZl9jb25zaWRlcmF0aW9uX25lZWRlZCI+CiAgICAgICAgICAgICAgICA8ZWwtc2VsZWN0IHYtbW9kZWw9Im5ld1Rhc2tUeXBlRm9ybS5pc19iaWxhdGVyYWxfcHJlZl9jb25zaWRlcmF0aW9uX25lZWRlZCIgcGxhY2Vob2xkZXI9Iuivt+mAieaLqSI+CiAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5pivIiB2YWx1ZT0iMSI+PC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5ZCmIiB2YWx1ZT0iMCI+PC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+IC0tPgogICAgICAgIDwvZWwtZm9ybT4KICAgICAgICA8ZGl2IHNsb3Q9ImZvb3RlciIgY2xhc3M9ImRpYWxvZy1mb290ZXIiPgogICAgICAgICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iYWRkVGFza1R5cGVEaWFsb2dWaXNpYmxlID0gZmFsc2UiPuWPlua2iDwvZWwtYnV0dG9uPgogICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIEBjbGljaz0iYWRkTmV3VGFza1R5cGUiIDpsb2FkaW5nPSJhZGRpbmdUYXNrVHlwZSI+56Gu5a6aPC9lbC1idXR0b24+CiAgICAgICAgPC9kaXY+CiAgICA8L2VsLWRpYWxvZz4KPC9kaXY+Cg=="}, null]}