{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\scene\\NewScene.vue?vue&type=template&id=02de0229&scoped=true", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\scene\\NewScene.vue", "mtime": 1754036804722}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754032187340}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}