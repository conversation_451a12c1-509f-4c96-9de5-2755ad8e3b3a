# Context
Filename: 多模态功能迭代任务.md
Created On: 2025-01-08
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
在EditScene.vue中迭代前端功能，添加两个核心功能：
1. 将单选模态内容变为多选模态内容，支持平台与多模态类型的绑定关系
2. 添加发布形态选择功能，支持平台特定的发布形态配置

# Project Overview
AI自适应系统的场景编辑模块，需要增强输出平台的模态配置能力，从单一模态选择升级为多模态组合选择，并增加发布形态的精细化控制。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## 当前实现分析
1. **现有模态选择**：
   - 位置：第3步"数据输出平台"配置中
   - 实现：单选下拉框 `<el-select v-model="form.modality">`
   - 数据源：通过API `/api/dict/getList` 获取 `dict_type: 'modality'`
   - 存储：`form.modality` 字符串字段

2. **平台配置结构**：
   - 输出平台：`form.output_platforms` 数组
   - 平台数据：`form.output_platforms_data` 对象
   - 数据选项：`form.output_data_options` 对象
   - 选中平台：`selectedOutputPlatforms` 数组

3. **关键发现**：
   - 当前模态选择是全局的，不与具体平台绑定
   - 平台配置采用动态字段渲染机制
   - 需要重构为平台级别的模态配置

## 需求分析
1. **多模态类型**：
   - 数字人视频、卡通视频、语音、游戏、图文结合、图文排版、播客视频
   - 支持一种或多种组合选择
   - 每个平台独立配置

2. **发布形态类型**：
   - PPT、Word文档、PDF、Excel表格、文本、作业练习题、评测考试题目、模态原生态展示
   - 每个平台只能选择一种形态
   - ECL平台特殊：可选择所有类型

# Proposed Solution (Populated by INNOVATE mode)

## 方案设计
1. **数据结构重构**：
   ```javascript
   // 新增字段
   platform_modalities: {
     [platformId]: ['数字人视频', '语音'] // 多选数组
   },
   platform_publish_forms: {
     [platformId]: 'PPT' // 单选字符串
   }
   ```

2. **UI组件设计**：
   - 多模态选择：下拉多选 + 右侧已选标签展示
   - 发布形态选择：下拉单选 + 右侧已选标签展示
   - 集成到现有平台配置卡片中

3. **平台关联逻辑**：
   - 根据平台类型动态显示可选模态和形态
   - ECL平台特殊处理：显示所有发布形态选项
   - 数据验证：确保每个平台至少选择一种模态

## 技术实现要点
1. **组件复用**：参考现有的数据选项多选实现
2. **数据同步**：平台变更时清理对应的模态配置
3. **表单验证**：添加必填验证规则
4. **API适配**：提交时将新字段包含在请求中

# Implementation Plan (Generated by PLAN mode)

## 详细实现计划

### 第一阶段：数据结构准备
1. **扩展form数据结构**
   - 添加 `platform_modalities: {}` 字段
   - 添加 `platform_publish_forms: {}` 字段
   - 移除或保留原 `modality` 字段作为兼容

2. **定义模态和形态常量**
   - 创建模态类型常量数组
   - 创建发布形态类型常量数组
   - 定义平台特殊规则（ECL平台）

### 第二阶段：UI组件实现
1. **多模态选择组件**
   - 下拉多选框实现
   - 已选标签展示区域
   - 集成到平台配置卡片

2. **发布形态选择组件**
   - 下拉单选框实现
   - 已选标签展示区域
   - 平台特殊逻辑处理

### 第三阶段：逻辑集成
1. **平台变更处理**
   - 更新 `handleOutputPlatformChange` 方法
   - 初始化新平台的模态配置
   - 清理移除平台的配置数据

2. **表单验证**
   - 添加模态选择必填验证
   - 添加发布形态必填验证
   - 集成到步骤验证逻辑

3. **数据提交**
   - 更新 `submitForm` 方法
   - 包含新字段到提交数据
   - 保持向后兼容性

### 第四阶段：测试和优化
1. **功能测试**
   - 多平台配置测试
   - 数据保存和加载测试
   - 表单验证测试

2. **用户体验优化**
   - 加载状态处理
   - 错误提示优化
   - 界面响应性调整

```
Implementation Checklist:
1. 在form数据中添加platform_modalities和platform_publish_forms字段
2. 定义模态类型和发布形态类型的常量数组
3. 在输出平台配置卡片中添加多模态选择组件
4. 在输出平台配置卡片中添加发布形态选择组件
5. 实现已选标签的展示逻辑
6. 更新handleOutputPlatformChange方法以初始化新字段
7. 添加表单验证规则
8. 更新submitForm方法包含新字段
9. 测试多平台配置功能
10. 优化用户界面和交互体验
```

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "准备开始实现"

# Task Progress (Appended by EXECUTE mode after each step completion)

# Final Review (Populated by REVIEW mode)
