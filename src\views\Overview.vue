<template>
    <div class="overview">
        <!-- <el-row :gutter="20">
            <el-col :span="24">
                <h2>最近运行场景</h2>
            </el-col>
        </el-row> -->

        <!-- <el-row :gutter="20" v-loading="loading">
            <el-col :span="8" v-for="scene in scenes" :key="scene.scene_id">
                <el-card class="scene-card" shadow="hover">
                    <div slot="header" class="clearfix">
                        <span>{{ scene.scene_name }}</span>
                        <el-button style="float: right; padding: 3px 0" type="text" @click="editScene(scene.scene_id)">
                            编辑
                        </el-button>
                    </div>
                    <div class="scene-description">{{ scene.scene_description }}</div>
                    <div class="scene-info">
                        <el-tag size="small" :type="getStateType(scene.state)">
                            {{ getStateText(scene.state) }}
                        </el-tag>
                        <span class="frequency">{{ scene.Frequency_of_operation }}</span>
                    </div>
                </el-card>
            </el-col>
        </el-row> -->

        <el-row :gutter="20" class="mt-20">
            <el-col :span="24">
                <div class="section-header">
                    <h2>最近运行场景</h2>
                    <el-button type="primary" @click="$router.push('/scene/new')">
                        新建场景
                    </el-button>
                </div>
            </el-col>
        </el-row>

        <el-row :gutter="20">
            <el-col :span="24">
                <el-card>
                    <el-table :data="scenes" v-loading="loading" style="width: 100%">
                        <el-table-column prop="scene_name" label="场景名称" />
                        <el-table-column label="运行状态">
                            <template slot-scope="{ row }">
                                <el-tag :type="getStateType(row.state)">
                                    {{ getStateText(row.state) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="final_running_time" label="最近运行时间"/>
        
                        <el-table-column label="操作" width="100">
                            <template slot-scope="{ row }">
                                <el-button type="text" size="small" @click="editScene(row.scene_id)">
                                    编辑
                                </el-button>
                                <!-- <el-button type="text" size="small" @click="viewScene(row.scene_id)">
                                    查看
                                </el-button> -->
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- <div class="pagination">
                        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
                            layout="total, sizes, prev, pager, next" :total="total">
                        </el-pagination>
                    </div> -->
                </el-card>
            </el-col>
        </el-row>


    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
    name: 'Overview',
    data() {
        return {
            allScenes: [],
            listLoading: false,
            currentPage: 1,
            pageSize: 10,
            total: 0
        }
    },
    computed: {
        ...mapState(['scenes', 'loading'])
    },
    methods: {
        ...mapActions(['fetchRecentScenes']),
        editScene(sceneId) {
            this.$router.push(`/scene/edit/${sceneId}`)
        },
        viewScene() {
            this.$router.push(`/scene/manage`)
        },
        getStateType(state) {
            switch (state) {
                case 1:
                    return 'success'
                case 2:
                    return 'warning'
                case 3:
                    return 'danger'
                default:
                    return 'info'
            }
        },
        getStateText(state) {
            switch (state) {
                case 1:
                    return '运行中'
                case 2:
                    return '已暂停'
                case 3:
                    return '已删除'
                default:
                    return '未知'
            }
        }
    },
    created() {
        this.fetchRecentScenes()
    }
}
</script>

<style scoped>
.overview {
    padding: 20px;
}

.scene-card {
    margin-bottom: 20px;
}

.scene-description {
    color: #666;
    margin: 10px 0;
    height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.scene-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.frequency {
    color: #909399;
    font-size: 12px;
}

.mt-20 {
    margin-top: 20px;
}

.pagination {
    margin-top: 20px;
    text-align: right;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-header h2 {
    margin: 0;
}
</style>