{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js!E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\Overview.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\Overview.vue", "mtime": 1754016998000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgbWFwU3RhdGUsIG1hcEFjdGlvbnMgfSBmcm9tICd2dWV4JzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdPdmVydmlldycsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFsbFNjZW5lczogW10sCiAgICAgIGxpc3RMb2FkaW5nOiBmYWxzZSwKICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgdG90YWw6IDAKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgLi4ubWFwU3RhdGUoWydzY2VuZXMnLCAnbG9hZGluZyddKQogIH0sCiAgbWV0aG9kczogewogICAgLi4ubWFwQWN0aW9ucyhbJ2ZldGNoUmVjZW50U2NlbmVzJ10pLAogICAgZWRpdFNjZW5lKHNjZW5lSWQpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goYC9zY2VuZS9lZGl0LyR7c2NlbmVJZH1gKTsKICAgIH0sCiAgICB2aWV3U2NlbmUoKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKGAvc2NlbmUvbWFuYWdlYCk7CiAgICB9LAogICAgZ2V0U3RhdGVUeXBlKHN0YXRlKSB7CiAgICAgIHN3aXRjaCAoc3RhdGUpIHsKICAgICAgICBjYXNlIDE6CiAgICAgICAgICByZXR1cm4gJ3N1Y2Nlc3MnOwogICAgICAgIGNhc2UgMjoKICAgICAgICAgIHJldHVybiAnd2FybmluZyc7CiAgICAgICAgY2FzZSAzOgogICAgICAgICAgcmV0dXJuICdkYW5nZXInOwogICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICByZXR1cm4gJ2luZm8nOwogICAgICB9CiAgICB9LAogICAgZ2V0U3RhdGVUZXh0KHN0YXRlKSB7CiAgICAgIHN3aXRjaCAoc3RhdGUpIHsKICAgICAgICBjYXNlIDE6CiAgICAgICAgICByZXR1cm4gJ+i/kOihjOS4rSc7CiAgICAgICAgY2FzZSAyOgogICAgICAgICAgcmV0dXJuICflt7LmmoLlgZwnOwogICAgICAgIGNhc2UgMzoKICAgICAgICAgIHJldHVybiAn5bey5Yig6ZmkJzsKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgcmV0dXJuICfmnKrnn6UnOwogICAgICB9CiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5mZXRjaFJlY2VudFNjZW5lcygpOwogIH0KfTs="}, {"version": 3, "names": ["mapState", "mapActions", "name", "data", "allScenes", "listLoading", "currentPage", "pageSize", "total", "computed", "methods", "editScene", "sceneId", "$router", "push", "viewScene", "getStateType", "state", "getStateText", "created", "fetchRecentScenes"], "sources": ["src/views/Overview.vue"], "sourcesContent": ["<template>\n    <div class=\"overview\">\n        <!-- <el-row :gutter=\"20\">\n            <el-col :span=\"24\">\n                <h2>最近运行场景</h2>\n            </el-col>\n        </el-row> -->\n\n        <!-- <el-row :gutter=\"20\" v-loading=\"loading\">\n            <el-col :span=\"8\" v-for=\"scene in scenes\" :key=\"scene.scene_id\">\n                <el-card class=\"scene-card\" shadow=\"hover\">\n                    <div slot=\"header\" class=\"clearfix\">\n                        <span>{{ scene.scene_name }}</span>\n                        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"editScene(scene.scene_id)\">\n                            编辑\n                        </el-button>\n                    </div>\n                    <div class=\"scene-description\">{{ scene.scene_description }}</div>\n                    <div class=\"scene-info\">\n                        <el-tag size=\"small\" :type=\"getStateType(scene.state)\">\n                            {{ getStateText(scene.state) }}\n                        </el-tag>\n                        <span class=\"frequency\">{{ scene.Frequency_of_operation }}</span>\n                    </div>\n                </el-card>\n            </el-col>\n        </el-row> -->\n\n        <el-row :gutter=\"20\" class=\"mt-20\">\n            <el-col :span=\"24\">\n                <div class=\"section-header\">\n                    <h2>最近运行场景</h2>\n                    <el-button type=\"primary\" @click=\"$router.push('/scene/new')\">\n                        新建场景\n                    </el-button>\n                </div>\n            </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n            <el-col :span=\"24\">\n                <el-card>\n                    <el-table :data=\"scenes\" v-loading=\"loading\" style=\"width: 100%\">\n                        <el-table-column prop=\"scene_name\" label=\"场景名称\" />\n                        <el-table-column label=\"运行状态\">\n                            <template slot-scope=\"{ row }\">\n                                <el-tag :type=\"getStateType(row.state)\">\n                                    {{ getStateText(row.state) }}\n                                </el-tag>\n                            </template>\n                        </el-table-column>\n                        <el-table-column prop=\"final_running_time\" label=\"最近运行时间\"/>\n        \n                        <el-table-column label=\"操作\" width=\"100\">\n                            <template slot-scope=\"{ row }\">\n                                <el-button type=\"text\" size=\"small\" @click=\"editScene(row.scene_id)\">\n                                    编辑\n                                </el-button>\n                                <!-- <el-button type=\"text\" size=\"small\" @click=\"viewScene(row.scene_id)\">\n                                    查看\n                                </el-button> -->\n                            </template>\n                        </el-table-column>\n                    </el-table>\n\n                    <!-- <div class=\"pagination\">\n                        <el-pagination @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\"\n                            :current-page=\"currentPage\" :page-sizes=\"[10, 20, 50, 100]\" :page-size=\"pageSize\"\n                            layout=\"total, sizes, prev, pager, next\" :total=\"total\">\n                        </el-pagination>\n                    </div> -->\n                </el-card>\n            </el-col>\n        </el-row>\n\n\n    </div>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\n\nexport default {\n    name: 'Overview',\n    data() {\n        return {\n            allScenes: [],\n            listLoading: false,\n            currentPage: 1,\n            pageSize: 10,\n            total: 0\n        }\n    },\n    computed: {\n        ...mapState(['scenes', 'loading'])\n    },\n    methods: {\n        ...mapActions(['fetchRecentScenes']),\n        editScene(sceneId) {\n            this.$router.push(`/scene/edit/${sceneId}`)\n        },\n        viewScene() {\n            this.$router.push(`/scene/manage`)\n        },\n        getStateType(state) {\n            switch (state) {\n                case 1:\n                    return 'success'\n                case 2:\n                    return 'warning'\n                case 3:\n                    return 'danger'\n                default:\n                    return 'info'\n            }\n        },\n        getStateText(state) {\n            switch (state) {\n                case 1:\n                    return '运行中'\n                case 2:\n                    return '已暂停'\n                case 3:\n                    return '已删除'\n                default:\n                    return '未知'\n            }\n        }\n    },\n    created() {\n        this.fetchRecentScenes()\n    }\n}\n</script>\n\n<style scoped>\n.overview {\n    padding: 20px;\n}\n\n.scene-card {\n    margin-bottom: 20px;\n}\n\n.scene-description {\n    color: #666;\n    margin: 10px 0;\n    height: 40px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n}\n\n.scene-info {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-top: 10px;\n}\n\n.frequency {\n    color: #909399;\n    font-size: 12px;\n}\n\n.mt-20 {\n    margin-top: 20px;\n}\n\n.pagination {\n    margin-top: 20px;\n    text-align: right;\n}\n\n.section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n}\n\n.section-header h2 {\n    margin: 0;\n}\n</style>"], "mappings": "AAgFA,SAAAA,QAAA,EAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MACAC,WAAA;MACAC,WAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAT,QAAA;EACA;EACAU,OAAA;IACA,GAAAT,UAAA;IACAU,UAAAC,OAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,gBAAAF,OAAA;IACA;IACAG,UAAA;MACA,KAAAF,OAAA,CAAAC,IAAA;IACA;IACAE,aAAAC,KAAA;MACA,QAAAA,KAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAC,aAAAD,KAAA;MACA,QAAAA,KAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;EACA;EACAE,QAAA;IACA,KAAAC,iBAAA;EACA;AACA", "ignoreList": []}]}