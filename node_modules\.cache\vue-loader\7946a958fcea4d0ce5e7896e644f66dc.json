{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js!E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\App.vue?vue&type=template&id=7ba5bd90", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\App.vue", "mtime": 1754017590000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754032187340}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "id", "width", "staticClass", "logoImage", "src", "alt", "_v", "mode", "router", "$route", "path", "index", "slot", "staticRenderFns", "_withStripped"], "sources": ["E:/aaaaaaaaa/kh/src/App.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { attrs: { id: \"app\" } },\n    [\n      _c(\n        \"el-container\",\n        [\n          _c(\n            \"el-aside\",\n            { attrs: { width: \"200px\" } },\n            [\n              _c(\"div\", { staticClass: \"logo\" }, [\n                _vm.logoImage\n                  ? _c(\"img\", {\n                      staticClass: \"logo-image\",\n                      attrs: { src: _vm.logoImage, alt: \"Logo\" },\n                    })\n                  : _c(\"i\", { staticClass: \"el-icon-s-data logo-icon\" }),\n                _c(\"h3\", [_vm._v(\"AI自适应系统\")]),\n              ]),\n              _c(\n                \"el-menu\",\n                {\n                  attrs: {\n                    mode: \"vertical\",\n                    router: \"\",\n                    \"default-active\": _vm.$route.path,\n                    \"background-color\": \"#304156\",\n                    \"text-color\": \"#bfcbd9\",\n                    \"active-text-color\": \"#409EFF\",\n                  },\n                },\n                [\n                  _c(\"el-menu-item\", { attrs: { index: \"/\" } }, [\n                    _c(\"i\", { staticClass: \"el-icon-pie-chart\" }),\n                    _c(\"span\", { attrs: { slot: \"title\" }, slot: \"title\" }, [\n                      _vm._v(\"总览\"),\n                    ]),\n                  ]),\n                  _c(\"el-menu-item\", { attrs: { index: \"/scene/new\" } }, [\n                    _c(\"i\", { staticClass: \"el-icon-plus\" }),\n                    _c(\"span\", { attrs: { slot: \"title\" }, slot: \"title\" }, [\n                      _vm._v(\"新建场景\"),\n                    ]),\n                  ]),\n                  _c(\"el-menu-item\", { attrs: { index: \"/scene/manage\" } }, [\n                    _c(\"i\", { staticClass: \"el-icon-setting\" }),\n                    _c(\"span\", { attrs: { slot: \"title\" }, slot: \"title\" }, [\n                      _vm._v(\"场景管理\"),\n                    ]),\n                  ]),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\"el-main\", [_c(\"router-view\")], 1),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAM;EAAE,CAAC,EACxB,CACEH,EAAE,CACA,cAAc,EACd,CACEA,EAAE,CACA,UAAU,EACV;IAAEE,KAAK,EAAE;MAAEE,KAAK,EAAE;IAAQ;EAAE,CAAC,EAC7B,CACEJ,EAAE,CAAC,KAAK,EAAE;IAAEK,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCN,GAAG,CAACO,SAAS,GACTN,EAAE,CAAC,KAAK,EAAE;IACRK,WAAW,EAAE,YAAY;IACzBH,KAAK,EAAE;MAAEK,GAAG,EAAER,GAAG,CAACO,SAAS;MAA<PERSON>,GAAG,EAAE;IAAO;EAC3C,CAAC,CAAC,GACFR,EAAE,CAAC,GAAG,EAAE;IAAEK,WAAW,EAAE;EAA2B,CAAC,CAAC,EACxDL,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACU,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAC9B,CAAC,EACFT,EAAE,CACA,SAAS,EACT;IACEE,KAAK,EAAE;MACLQ,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,EAAE;MACV,gBAAgB,EAAEZ,GAAG,CAACa,MAAM,CAACC,IAAI;MACjC,kBAAkB,EAAE,SAAS;MAC7B,YAAY,EAAE,SAAS;MACvB,mBAAmB,EAAE;IACvB;EACF,CAAC,EACD,CACEb,EAAE,CAAC,cAAc,EAAE;IAAEE,KAAK,EAAE;MAAEY,KAAK,EAAE;IAAI;EAAE,CAAC,EAAE,CAC5Cd,EAAE,CAAC,GAAG,EAAE;IAAEK,WAAW,EAAE;EAAoB,CAAC,CAAC,EAC7CL,EAAE,CAAC,MAAM,EAAE;IAAEE,KAAK,EAAE;MAAEa,IAAI,EAAE;IAAQ,CAAC;IAAEA,IAAI,EAAE;EAAQ,CAAC,EAAE,CACtDhB,GAAG,CAACU,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,CACH,CAAC,EACFT,EAAE,CAAC,cAAc,EAAE;IAAEE,KAAK,EAAE;MAAEY,KAAK,EAAE;IAAa;EAAE,CAAC,EAAE,CACrDd,EAAE,CAAC,GAAG,EAAE;IAAEK,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCL,EAAE,CAAC,MAAM,EAAE;IAAEE,KAAK,EAAE;MAAEa,IAAI,EAAE;IAAQ,CAAC;IAAEA,IAAI,EAAE;EAAQ,CAAC,EAAE,CACtDhB,GAAG,CAACU,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,CACH,CAAC,EACFT,EAAE,CAAC,cAAc,EAAE;IAAEE,KAAK,EAAE;MAAEY,KAAK,EAAE;IAAgB;EAAE,CAAC,EAAE,CACxDd,EAAE,CAAC,GAAG,EAAE;IAAEK,WAAW,EAAE;EAAkB,CAAC,CAAC,EAC3CL,EAAE,CAAC,MAAM,EAAE;IAAEE,KAAK,EAAE;MAAEa,IAAI,EAAE;IAAQ,CAAC;IAAEA,IAAI,EAAE;EAAQ,CAAC,EAAE,CACtDhB,GAAG,CAACU,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,CACH,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDT,EAAE,CAAC,SAAS,EAAE,CAACA,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CACtC,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIgB,eAAe,GAAG,EAAE;AACxBlB,MAAM,CAACmB,aAAa,GAAG,IAAI;AAE3B,SAASnB,MAAM,EAAEkB,eAAe", "ignoreList": []}]}