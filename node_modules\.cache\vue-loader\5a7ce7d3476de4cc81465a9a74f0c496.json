{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\scene\\NewScene.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\scene\\NewScene.vue", "mtime": 1754034401661}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IG1hcFN0YXRlLCBtYXBBY3Rpb25zIH0gZnJvbSAndnVleCcKCmV4cG9ydCBkZWZhdWx0IHsKICAgIG5hbWU6ICdOZXdTY2VuZScsCiAgICBkYXRhKCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIGFjdGl2ZVN0ZXA6IDAsCiAgICAgICAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAgICAgICBmb3JtOiB7CiAgICAgICAgICAgICAgICBsaW5rZWRfcHJvamVjdF90ZWFtX25hbWU6IG51bGwsCiAgICAgICAgICAgICAgICBsaW5rZWRfdGFza190eXBlX2NvZGU6IG51bGwsCiAgICAgICAgICAgICAgICBzY2VuZV9uYW1lOiAnJywKICAgICAgICAgICAgICAgIHNjZW5lX2Rlc2NyaXB0aW9uOiAnJywKICAgICAgICAgICAgICAgIGlucHV0X3BsYXRmb3JtczogW10sCiAgICAgICAgICAgICAgICBvdXRwdXRfcGxhdGZvcm1zOiBbXSwKICAgICAgICAgICAgICAgIGlucHV0X3BsYXRmb3Jtc19kYXRhOiB7fSwKICAgICAgICAgICAgICAgIG91dHB1dF9wbGF0Zm9ybXNfZGF0YToge30sCiAgICAgICAgICAgICAgICBpbnB1dF9kYXRhX29wdGlvbnM6IHt9LAogICAgICAgICAgICAgICAgb3V0cHV0X2RhdGFfb3B0aW9uczoge30sCiAgICAgICAgICAgICAgICBpbnB1dF9lZmZlY3RfcGFyYW1zOiB7fSwKICAgICAgICAgICAgICAgIG91dHB1dF9lZmZlY3RfcGFyYW1zOiB7fSwKICAgICAgICAgICAgICAgIGlucHV0X2VmZmVjdF9wYXJhbXNfY29uZmlnOiB7fSwKICAgICAgICAgICAgICAgIG91dHB1dF9lZmZlY3RfcGFyYW1zX2NvbmZpZzoge30sCiAgICAgICAgICAgICAgICB1cGRhdGVkX3Byb21wdDogJycsCiAgICAgICAgICAgICAgICBzY2VuZV9ydW5uaW5nX2ZyZXF1ZW5jeTogJycsCiAgICAgICAgICAgICAgICBob3VyOiAnJywKICAgICAgICAgICAgICAgIG1vZGFsaXR5OiAnJywKICAgICAgICAgICAgICAgIHBsYXRmb3JtX21vZGFsaXRpZXM6IHt9LCAvLyDmlrDlop7vvJrlubPlj7DlpJrmqKHmgIHphY3nva4KICAgICAgICAgICAgICAgIHBsYXRmb3JtX3B1Ymxpc2hfZm9ybXM6IHt9LCAvLyDmlrDlop7vvJrlubPlj7Dlj5HluIPlvaLmgIHphY3nva4KICAgICAgICAgICAgICAgIGRheTogJycsCiAgICAgICAgICAgICAgICB3ZWVrczogJycsCiAgICAgICAgICAgICAgICBzdG9yZWRfc3RyYXRlZ3lfcmVmcmVzaF9kYXlzOiAwLAogICAgICAgICAgICAgICAgZXhwbG9yZV9zdHJhdGVneV90cmlnZ2VyX2RheXM6IDM2NSwKICAgICAgICAgICAgICAgIHNjZW5lX2J1c2luZXNzX3R5cGU6ICcnLAogICAgICAgICAgICAgICAgYmFzZWxpbmVfZGF0YV9zdGFydF9kYXlzX2FnbzogMzAsCiAgICAgICAgICAgICAgICBiYXNlbGluZV9kYXRhX2V4Y2x1ZGVfcmVjZW50X2RheXM6IDMsCiAgICAgICAgICAgICAgICBtaW5fYmFzZWxpbmVfc2FtcGxlX2NvdW50OiAzLAogICAgICAgICAgICAgICAgYmFzZWxpbmVfcmVmcmVzaF9mcmVxdWVuY3lfZGF5czogNwogICAgICAgICAgICB9LAogICAgICAgICAgICBmcmVxdWVuY3lWYWx1ZTogMzAsCiAgICAgICAgICAgIGZyZXF1ZW5jeVVuaXQ6ICdtaW51dGVzJywKICAgICAgICAgICAgcnVsZXM6IHsKICAgICAgICAgICAgICAgIGxpbmtlZF9wcm9qZWN0X3RlYW1fbmFtZTogWwogICAgICAgICAgICAgICAgICAgIHsgCiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLCAKICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqemhueebrue7hCcsIAogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiBbJ2NoYW5nZScsICdibHVyJ10sCiAgICAgICAgICAgICAgICAgICAgICAgIHZhbGlkYXRvcjogKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCF2YWx1ZSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36YCJ5oup6aG555uu57uEJykpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhbGxiYWNrKCkKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBsaW5rZWRfdGFza190eXBlX2NvZGU6IFsKICAgICAgICAgICAgICAgICAgICB7IAogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwgCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfor7fpgInmi6nku7vliqHnsbvlnosnLCAKICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogWydjaGFuZ2UnLCAnYmx1ciddLAogICAgICAgICAgICAgICAgICAgICAgICB2YWxpZGF0b3I6IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghdmFsdWUpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+mAieaLqeS7u+WKoeexu+WeiycpKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYWxsYmFjaygpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgc2NlbmVfbmFtZTogWwogICAgICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlnLrmma/lkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgIHNjZW5lX2Rlc2NyaXB0aW9uOiBbCiAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1lc3NhZ2U6ICfor7fovpPlhaXlnLrmma/mj4/ov7AnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgIGlucHV0X3BsYXRmb3JtczogWwogICAgICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6novpPlhaXlubPlj7AnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgb3V0cHV0X3BsYXRmb3JtczogWwogICAgICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6novpPlh7rlubPlj7AnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgdXBkYXRlZF9wcm9tcHQ6IFsKICAgICAgICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWlQUnmj5DnpLror40nLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgIHNjZW5lX3J1bm5pbmdfZnJlcXVlbmN5OiBbCiAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+iuvue9rui/kOihjOmikeeOhycsIHRyaWdnZXI6ICdjaGFuZ2UnIH0sCiAgICAgICAgICAgICAgICAgICAgeyAKICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ251bWJlcicsIAogICAgICAgICAgICAgICAgICAgICAgICBtaW46IDMwLCAKICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogJ+i/kOihjOmikeeOh+acgOWwj+mXtOmalOS4ujMw5YiG6ZKfJywgCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICdjaGFuZ2UnIAogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBzdG9yZWRfc3RyYXRlZ3lfcmVmcmVzaF9kYXlzOiBbCiAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeS4quaAp+WMlui/m+WMluetlueVpeabtOaWsOmikeeOhycsIHRyaWdnZXI6ICdibHVyJyB9LAogICAgICAgICAgICAgICAgICAgIHsgdHlwZTogJ251bWJlcicsIG1pbjogMCwgbWF4OiAzNjUsIG1lc3NhZ2U6ICfor7fovpPlhaUwLTM2NeS5i+mXtOeahOWkqeaVsCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgZXhwbG9yZV9zdHJhdGVneV90cmlnZ2VyX2RheXM6IFsKICAgICAgICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWlQUnoh6rooYzmjqLntKLpopHnjocnLCB0cmlnZ2VyOiAnYmx1cicgfSwKICAgICAgICAgICAgICAgICAgICB7IHR5cGU6ICdudW1iZXInLCBtaW46IDEsIG1heDogMzY1LCBtZXNzYWdlOiAn6K+36L6T5YWlMS0zNjXkuYvpl7TnmoTlpKnmlbAnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgIHNjZW5lX2J1c2luZXNzX3R5cGU6IFsKICAgICAgICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5oiW6L6T5YWl5Zy65pmv57G75Z6LJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgIGJhc2VsaW5lX2RhdGFfc3RhcnRfZGF5c19hZ286IFsKICAgICAgICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5L2/55So5pWw5o2u55qE6LW35aeL5aSp5pWwJywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICAgICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAxLCBtYXg6IDM2NSwgbWVzc2FnZTogJ+ivt+i+k+WFpTEtMzY15LmL6Ze055qE5aSp5pWwJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBiYXNlbGluZV9kYXRhX2V4Y2x1ZGVfcmVjZW50X2RheXM6IFsKICAgICAgICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5o6S6Zmk5pyA6L+R55qE5pWw5o2u5aSp5pWwJywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICAgICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAwLCBtYXg6IDM2NSwgbWVzc2FnZTogJ+ivt+i+k+WFpTAtMzDkuYvpl7TnmoTlpKnmlbAnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgIG1pbl9iYXNlbGluZV9zYW1wbGVfY291bnQ6IFsKICAgICAgICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5qC35pys6YeP5pyA5bCP6ZiI5YC8JywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICAgICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAxLCBtYXg6IDEwMDAwLCBtZXNzYWdlOiAn6K+36L6T5YWlMS0xMDAwMOS5i+mXtOeahOaVsOWAvCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgYmFzZWxpbmVfcmVmcmVzaF9mcmVxdWVuY3lfZGF5czogWwogICAgICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXln7rlh4bnur/mm7TmlrDpopHnjocnLCB0cmlnZ2VyOiAnYmx1cicgfSwKICAgICAgICAgICAgICAgICAgICB7IHR5cGU6ICdudW1iZXInLCBtaW46IDEsIG1heDogMzY1LCBtZXNzYWdlOiAn6K+36L6T5YWlMS0zNjXkuYvpl7TnmoTlpKnmlbAnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgICAgICAgICAgXQogICAgICAgICAgICB9LAogICAgICAgICAgICBwcm9qZWN0VGVhbXM6IFtdLAogICAgICAgICAgICB0YXNrVHlwZXM6IFtdLAogICAgICAgICAgICBzZWxlY3RlZElucHV0UGxhdGZvcm1zOiBbXSwKICAgICAgICAgICAgc2VsZWN0ZWRPdXRwdXRQbGF0Zm9ybXM6IFtdLAogICAgICAgICAgICBhZGRPcHRpb25EaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgICAgICAgbmV3T3B0aW9uRm9ybTogewogICAgICAgICAgICAgICAgb3B0aW9uX25hbWU6ICcnLAogICAgICAgICAgICAgICAgb3B0aW9uX2tleTogJycsCiAgICAgICAgICAgICAgICBwbGF0Zm9ybV9pZDogJycKICAgICAgICAgICAgfSwKICAgICAgICAgICAgb3B0aW9uUnVsZXM6IHsKICAgICAgICAgICAgICAgIG9wdGlvbl9uYW1lOiBbCiAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWGheWuueWQjeensCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgb3B0aW9uX2tleTogWwogICAgICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlhoXlrrnmoIfor4bvvIjoi7HmlofvvIknLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgICAgICAgICAgXQogICAgICAgICAgICB9LAogICAgICAgICAgICBhZGRpbmdPcHRpb246IGZhbHNlLAogICAgICAgICAgICBtb2RhbGl0eU9wdGlvbnM6IFtdLAogICAgICAgICAgICBhZGRQcm9qZWN0RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgICAgICAgIGFkZFRhc2tUeXBlRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgICAgICAgIG5ld1Byb2plY3RGb3JtOiB7CiAgICAgICAgICAgICAgICBwcm9qZWN0X25hbWU6ICcnCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIG5ld1Rhc2tUeXBlRm9ybTogewogICAgICAgICAgICAgICAgdGFza190eXBlX25hbWU6ICcnLAogICAgICAgICAgICAgICAgdGFza190eXBlX2Rlc2NyaXB0aW9uOiAnJywKICAgICAgICAgICAgICAgIHJlY29tbWVuZGVkX2VmZmVjdF9wYXJhbV9jb2RlczogJycsCiAgICAgICAgICAgICAgICBlZmZlY3RfcGFyYW1fcmVsYXRpb25zaGlwc19ub3RlOiAnJywKICAgICAgICAgICAgICAgIGlzX2NvbnRlbnRfZnJvbV9leHRlcm5hbDogJzEnLAogICAgICAgICAgICAgICAgaXNfYmlsYXRlcmFsX3ByZWZfY29uc2lkZXJhdGlvbl9uZWVkZWQ6ICcwJywKICAgICAgICAgICAgICAgIGxpbmtlZF9wbGF0Zm9ybV9pZHM6ICcnLAogICAgICAgICAgICAgICAgdGFza190eXBlX3N0YXR1czogMSwKICAgICAgICAgICAgICAgIHRhc2tfdHlwZV9vd25lcjogMgogICAgICAgICAgICB9LAogICAgICAgICAgICBzZWxlY3RlZFBsYXRmb3JtSWRzOiBbXSwKICAgICAgICAgICAgc2VsZWN0ZWRFZmZlY3RQYXJhbXM6IFtdLAogICAgICAgICAgICBhdmFpbGFibGVFZmZlY3RQYXJhbXNGb3JUYXNrVHlwZTogW10sCiAgICAgICAgICAgIHByb2plY3RSdWxlczogewogICAgICAgICAgICAgICAgcHJvamVjdF9uYW1lOiBbCiAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpemhueebruWQjeensCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHRhc2tUeXBlUnVsZXM6IHsKICAgICAgICAgICAgICAgIHRhc2tfdHlwZV9uYW1lOiBbCiAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeS7u+WKoeexu+Wei+WQjeensCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgdGFza190eXBlX2Rlc2NyaXB0aW9uOiBbCiAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeS7u+WKoeexu+Wei+aPj+i/sCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgcmVjb21tZW5kZWRfZWZmZWN0X3BhcmFtX2NvZGVzOiBbCiAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeaOqOiNkOaViOaenOWPguaVsOe8lueggScsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgZWZmZWN0X3BhcmFtX3JlbGF0aW9uc2hpcHNfbm90ZTogWwogICAgICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlkITmjqjojZDlj4LmlbDkuYvpl7TnmoTpgLvovpHlhbPns7vor7TmmI4nLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgICAgICAgICAgXQogICAgICAgICAgICB9LAogICAgICAgICAgICBhZGRpbmdQcm9qZWN0OiBmYWxzZSwKICAgICAgICAgICAgYWRkaW5nVGFza1R5cGU6IGZhbHNlLAogICAgICAgICAgICBzY2VuZVR5cGVzOiBbXSwKICAgICAgICAgICAgbG9hZGluZ0lucHV0UGxhdGZvcm1zOiBmYWxzZSwKICAgICAgICAgICAgbG9hZGluZ091dHB1dFBsYXRmb3JtczogZmFsc2UsCiAgICAgICAgICAgIHRlbXBNb2RhbGl0eVNlbGVjdGlvbjoge30sIC8vIOS4tOaXtuWtmOWCqOaooeaAgemAieaLqQogICAgICAgICAgICAvLyDmqKHmgIHnsbvlnovluLjph48KICAgICAgICAgICAgTU9EQUxJVFlfVFlQRVM6IFsKICAgICAgICAgICAgICAgICfmlbDlrZfkurrop4bpopEnLAogICAgICAgICAgICAgICAgJ+WNoemAmuinhumikScsCiAgICAgICAgICAgICAgICAn6K+t6Z+zJywKICAgICAgICAgICAgICAgICfmuLjmiI8nLAogICAgICAgICAgICAgICAgJ+WbvuaWh+e7k+WQiCcsCiAgICAgICAgICAgICAgICAn5Zu+5paH5o6S54mIJywKICAgICAgICAgICAgICAgICfmkq3lrqLop4bpopEnCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIC8vIOWPkeW4g+W9ouaAgeexu+Wei+W4uOmHjwogICAgICAgICAgICBQVUJMSVNIX0ZPUk1fVFlQRVM6IFsKICAgICAgICAgICAgICAgICdQUFQnLAogICAgICAgICAgICAgICAgJ1dvcmTmlofmoaMnLAogICAgICAgICAgICAgICAgJ1BERicsCiAgICAgICAgICAgICAgICAnRXhjZWzooajmoLwnLAogICAgICAgICAgICAgICAgJ+aWh+acrCcsCiAgICAgICAgICAgICAgICAn5L2c5Lia57uD5Lmg6aKYJywKICAgICAgICAgICAgICAgICfor4TmtYvogIPor5Xpopjnm64nLAogICAgICAgICAgICAgICAgJ+aooeaAgeWOn+eUn+aAgeWxleekuicKICAgICAgICAgICAgXQogICAgICAgIH0KICAgIH0sCiAgICBjb21wdXRlZDogewogICAgICAgIC4uLm1hcFN0YXRlKFsncGxhdGZvcm1zJ10pLAogICAgICAgIGF2YWlsYWJsZVBsYXRmb3JtcygpIHsKICAgICAgICAgICAgaWYgKCF0aGlzLmZvcm0ubGlua2VkX3Rhc2tfdHlwZV9jb2RlKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5wbGF0Zm9ybXMKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgY29uc3Qgc2VsZWN0ZWRUYXNrVHlwZSA9IHRoaXMudGFza1R5cGVzLmZpbmQodGFza1R5cGUgPT4gdGFza1R5cGUudGFza190eXBlX2NvZGUgPT09IHRoaXMuZm9ybS5saW5rZWRfdGFza190eXBlX2NvZGUpCiAgICAgICAgICAgIGlmICghc2VsZWN0ZWRUYXNrVHlwZSB8fCAhc2VsZWN0ZWRUYXNrVHlwZS5saW5rZWRfcGxhdGZvcm1faWRzKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5wbGF0Zm9ybXMKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgY29uc3QgbGlua2VkUGxhdGZvcm1JZHMgPSBzZWxlY3RlZFRhc2tUeXBlLmxpbmtlZF9wbGF0Zm9ybV9pZHMuc3BsaXQoJywnKS5tYXAoaWQgPT4gcGFyc2VJbnQoaWQudHJpbSgpKSkKICAgICAgICAgICAgcmV0dXJuIHRoaXMucGxhdGZvcm1zLmZpbHRlcihwbGF0Zm9ybSA9PiBsaW5rZWRQbGF0Zm9ybUlkcy5pbmNsdWRlcyhwbGF0Zm9ybS5wbGF0Zm9ybV9pZCkpCiAgICAgICAgfSwKICAgICAgICBhdmFpbGFibGVFZmZlY3RQYXJhbXMoKSB7CiAgICAgICAgICAgIGlmICghdGhpcy5mb3JtLmxpbmtlZF90YXNrX3R5cGVfY29kZSkgewogICAgICAgICAgICAgICAgcmV0dXJuIFtdCiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIGNvbnN0IHNlbGVjdGVkVGFza1R5cGUgPSB0aGlzLnRhc2tUeXBlcy5maW5kKHRhc2tUeXBlID0+IHRhc2tUeXBlLnRhc2tfdHlwZV9jb2RlID09PSB0aGlzLmZvcm0ubGlua2VkX3Rhc2tfdHlwZV9jb2RlKQogICAgICAgICAgICBpZiAoIXNlbGVjdGVkVGFza1R5cGUgfHwgIXNlbGVjdGVkVGFza1R5cGUucmVjb21tZW5kZWRfZWZmZWN0X3BhcmFtX2NvZGVzKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gW10KICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgIGNvbnN0IHBhcmFtcyA9IEpTT04ucGFyc2Uoc2VsZWN0ZWRUYXNrVHlwZS5yZWNvbW1lbmRlZF9lZmZlY3RfcGFyYW1fY29kZXMpCiAgICAgICAgICAgICAgICByZXR1cm4gQXJyYXkuaXNBcnJheShwYXJhbXMpID8gcGFyYW1zIDogW10KICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOaViOaenOWPguaVsOWksei0pTonLCBlcnJvcikKICAgICAgICAgICAgICAgIHJldHVybiBbXQogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBpc1BsYXRmb3JtQ29uZmlnTG9hZGluZygpIHsKICAgICAgICAgICAgaWYgKHRoaXMuYWN0aXZlU3RlcCA9PT0gMiAmJiB0aGlzLmxvYWRpbmdJbnB1dFBsYXRmb3JtcykgewogICAgICAgICAgICAgICAgcmV0dXJuIHRydWUKICAgICAgICAgICAgfQogICAgICAgICAgICBpZiAodGhpcy5hY3RpdmVTdGVwID09PSAzICYmIHRoaXMubG9hZGluZ091dHB1dFBsYXRmb3JtcykgewogICAgICAgICAgICAgICAgcmV0dXJuIHRydWUKICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgICB9CiAgICB9LAogICAgbWV0aG9kczogewogICAgICAgIC4uLm1hcEFjdGlvbnMoWydmZXRjaFBsYXRmb3JtcyddKSwKICAgICAgICAvLyDojrflj5blubPlj7Dlj6/nlKjnmoTlj5HluIPlvaLmgIEKICAgICAgICBnZXRBdmFpbGFibGVQdWJsaXNoRm9ybXMocGxhdGZvcm0pIHsKICAgICAgICAgICAgLy8gRUNM5bmz5Y+w5Y+v5Lul6YCJ5oup5omA5pyJ57G75Z6LCiAgICAgICAgICAgIGlmIChwbGF0Zm9ybS5wbGF0Zm9ybV9uYW1lICYmIHBsYXRmb3JtLnBsYXRmb3JtX25hbWUudG9VcHBlckNhc2UoKS5pbmNsdWRlcygnRUNMJykpIHsKICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLlBVQkxJU0hfRk9STV9UWVBFUwogICAgICAgICAgICB9CiAgICAgICAgICAgIC8vIOWFtuS7luW5s+WPsOagueaNruWFt+S9k+mcgOaxgui/lOWbnueJueWumuexu+Wei++8jOi/memHjOaaguaXtui/lOWbnuaJgOacieexu+WeiwogICAgICAgICAgICAvLyDlkI7nu63lj6/ku6XmoLnmja7lubPlj7Dnsbvlnovov5vooYzmm7Tnsr7nu4bnmoTmjqfliLYKICAgICAgICAgICAgcmV0dXJuIHRoaXMuUFVCTElTSF9GT1JNX1RZUEVTCiAgICAgICAgfSwKICAgICAgICAvLyDlpITnkIbmqKHmgIHpgInmi6kKICAgICAgICBoYW5kbGVNb2RhbGl0eVNlbGVjdChwbGF0Zm9ybUlkLCBzZWxlY3RlZE1vZGFsaXR5KSB7CiAgICAgICAgICAgIGlmICghc2VsZWN0ZWRNb2RhbGl0eSkgcmV0dXJuCgogICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5wbGF0Zm9ybV9tb2RhbGl0aWVzW3BsYXRmb3JtSWRdKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLnBsYXRmb3JtX21vZGFsaXRpZXMsIHBsYXRmb3JtSWQsIFtdKQogICAgICAgICAgICB9CgogICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5wbGF0Zm9ybV9tb2RhbGl0aWVzW3BsYXRmb3JtSWRdLmluY2x1ZGVzKHNlbGVjdGVkTW9kYWxpdHkpKSB7CiAgICAgICAgICAgICAgICB0aGlzLmZvcm0ucGxhdGZvcm1fbW9kYWxpdGllc1twbGF0Zm9ybUlkXS5wdXNoKHNlbGVjdGVkTW9kYWxpdHkpCiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC8vIOa4heepuuS4tOaXtumAieaLqQogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy50ZW1wTW9kYWxpdHlTZWxlY3Rpb24sIHBsYXRmb3JtSWQsICcnKQogICAgICAgIH0sCiAgICAgICAgLy8g56e76Zmk5qih5oCBCiAgICAgICAgcmVtb3ZlTW9kYWxpdHkocGxhdGZvcm1JZCwgbW9kYWxpdHkpIHsKICAgICAgICAgICAgaWYgKHRoaXMuZm9ybS5wbGF0Zm9ybV9tb2RhbGl0aWVzW3BsYXRmb3JtSWRdKSB7CiAgICAgICAgICAgICAgICBjb25zdCBpbmRleCA9IHRoaXMuZm9ybS5wbGF0Zm9ybV9tb2RhbGl0aWVzW3BsYXRmb3JtSWRdLmluZGV4T2YobW9kYWxpdHkpCiAgICAgICAgICAgICAgICBpZiAoaW5kZXggPiAtMSkgewogICAgICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5wbGF0Zm9ybV9tb2RhbGl0aWVzW3BsYXRmb3JtSWRdLnNwbGljZShpbmRleCwgMSkKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgLy8g56e76Zmk5Y+R5biD5b2i5oCBCiAgICAgICAgcmVtb3ZlUHVibGlzaEZvcm0ocGxhdGZvcm1JZCkgewogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLnBsYXRmb3JtX3B1Ymxpc2hfZm9ybXMsIHBsYXRmb3JtSWQsICcnKQogICAgICAgIH0sCiAgICAgICAgYXN5bmMgZmV0Y2hQbGF0Zm9ybU9wdGlvbnMocGxhdGZvcm1JZCkgewogICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLiRodHRwLnBvc3QoJycsIHsKICAgICAgICAgICAgICAgICAgICBhcGk6ICcvYXBpL29wdGlvbi9nZXRMaXN0JywKICAgICAgICAgICAgICAgICAgICBwYXJhbTogewogICAgICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybV9pZDogcGxhdGZvcm1JZAogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhIHx8IFtdCiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwbGF0Zm9ybSBvcHRpb25zOicsIGVycm9yKQogICAgICAgICAgICAgICAgcmV0dXJuIFtdCiAgICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGFzeW5jIGZldGNoUGxhdGZvcm1FZmZlY3RQYXJhbXMocGxhdGZvcm1JZCkgewogICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLiRodHRwLnBvc3QoJycsIHsKICAgICAgICAgICAgICAgICAgICBhcGk6ICcvYXBpL2VmZmVjdFBhcmFtQ2F0ZWdvcnkvZ2V0TGlzdCcsCiAgICAgICAgICAgICAgICAgICAgcGFyYW06IHsKICAgICAgICAgICAgICAgICAgICAgICAgcGxhdGZvcm1faWQ6IHBsYXRmb3JtSWQKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuZGF0YSB8fCBbXQogICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgcGxhdGZvcm0gZWZmZWN0IHBhcmFtczonLCBlcnJvcikKICAgICAgICAgICAgICAgIHJldHVybiBbXQogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBnZXRBdmFpbGFibGVFZmZlY3RQYXJhbXNGb3JQbGF0Zm9ybShwbGF0Zm9ybUlkKSB7CiAgICAgICAgICAgIGNvbnN0IHJlY29tbWVuZGVkUGFyYW1zID0gdGhpcy5hdmFpbGFibGVFZmZlY3RQYXJhbXMgfHwgW10KICAgICAgICAgICAgCiAgICAgICAgICAgIGNvbnN0IHBsYXRmb3JtID0gdGhpcy5zZWxlY3RlZElucHV0UGxhdGZvcm1zLmZpbmQocCA9PiBwLnBsYXRmb3JtX2lkID09PSBwbGF0Zm9ybUlkKQogICAgICAgICAgICBjb25zdCBwbGF0Zm9ybVBhcmFtcyA9IHBsYXRmb3JtID8gKHBsYXRmb3JtLmVmZmVjdFBhcmFtcyB8fCBbXSkgOiBbXQogICAgICAgICAgICAKICAgICAgICAgICAgaWYgKHBsYXRmb3JtUGFyYW1zLmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgICAgICAgcmV0dXJuIFtdCiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIHJldHVybiBwbGF0Zm9ybVBhcmFtcy5maWx0ZXIocGFyYW0gPT4gcmVjb21tZW5kZWRQYXJhbXMuaW5jbHVkZXMocGFyYW0uZWZmZWN0X3BhcmFtX25hbWUpKQogICAgICAgIH0sCiAgICAgICAgZ2V0RmllbGRDb21wb25lbnQodHlwZSkgewogICAgICAgICAgICBjb25zdCBjb21wb25lbnRNYXAgPSB7CiAgICAgICAgICAgICAgICAnc3RyaW5nJzogJ2VsLWlucHV0JywKICAgICAgICAgICAgICAgICdwYXNzd29yZCc6ICdlbC1pbnB1dCcsCiAgICAgICAgICAgICAgICAnc2VsZWN0JzogJ2VsLXNlbGVjdCcsCiAgICAgICAgICAgICAgICAnbXVsdGlzZWxlY3QnOiAnZWwtc2VsZWN0JywKICAgICAgICAgICAgICAgICdudW1iZXInOiAnZWwtaW5wdXQtbnVtYmVyJywKICAgICAgICAgICAgICAgICdib29sJzogJ2VsLXN3aXRjaCcsCiAgICAgICAgICAgICAgICAndGV4dGFyZWEnOiAnZWwtaW5wdXQnCiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIGNvbXBvbmVudE1hcFt0eXBlXSB8fCAnZWwtaW5wdXQnCiAgICAgICAgfSwKICAgICAgICBnZXRGaWVsZFByb3BzKGZpZWxkKSB7CiAgICAgICAgICAgIGNvbnN0IHByb3BzID0gewogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6IGDor7fovpPlhaUke2ZpZWxkLmxhYmVsfWAKICAgICAgICAgICAgfQogICAgICAgICAgICBpZiAoZmllbGQuZmllbGRfdHlwZSA9PT0gJ3Bhc3N3b3JkJykgewogICAgICAgICAgICAgICAgcHJvcHMudHlwZSA9ICdwYXNzd29yZCcKICAgICAgICAgICAgfQogICAgICAgICAgICBpZiAoZmllbGQuZmllbGRfdHlwZSA9PT0gJ3RleHRhcmVhJykgewogICAgICAgICAgICAgICAgcHJvcHMudHlwZSA9ICd0ZXh0YXJlYScKICAgICAgICAgICAgICAgIHByb3BzLnJvd3MgPSAzCiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKGZpZWxkLmZpZWxkX3R5cGUgPT09ICdzZWxlY3QnIHx8IGZpZWxkLmZpZWxkX3R5cGUgPT09ICdtdWx0aXNlbGVjdCcpIHsKICAgICAgICAgICAgICAgIHByb3BzLm11bHRpcGxlID0gZmllbGQuZmllbGRfdHlwZSA9PT0gJ211bHRpc2VsZWN0JwogICAgICAgICAgICAgICAgcHJvcHMub3B0aW9ucyA9IGZpZWxkLm9wdGlvbnMgfHwgW10KICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gcHJvcHMKICAgICAgICB9LAogICAgICAgIGFzeW5jIGhhbmRsZUlucHV0UGxhdGZvcm1DaGFuZ2UocGxhdGZvcm1JZHMpIHsKICAgICAgICAgICAgaWYgKHRoaXMubG9hZGluZ0lucHV0UGxhdGZvcm1zKSB7CiAgICAgICAgICAgICAgICByZXR1cm4KICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmxvYWRpbmdJbnB1dFBsYXRmb3JtcyA9IHRydWUKICAgICAgICAgICAgCiAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5wdXRQbGF0Zm9ybXMgPSBbXQogICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICBPYmplY3Qua2V5cyh0aGlzLnJ1bGVzKS5mb3JFYWNoKGtleSA9PiB7CiAgICAgICAgICAgICAgICAgICAgaWYgKGtleS5zdGFydHNXaXRoKCdpbnB1dF9wbGF0Zm9ybXNfZGF0YS4nKSAmJiAha2V5LmVuZHNXaXRoKCcuYWRkaXRpb25hbF9JbmZvcm1hdGlvbicpKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJGRlbGV0ZSh0aGlzLnJ1bGVzLCBrZXkpCiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgY29uc3QgcGxhdGZvcm1zV2l0aERldGFpbHMgPSBbXQogICAgICAgICAgICAgICAgZm9yIChjb25zdCBwbGF0Zm9ybUlkIG9mIHBsYXRmb3JtSWRzKSB7CiAgICAgICAgICAgICAgICAgICAgY29uc3QgcGxhdGZvcm0gPSB0aGlzLnBsYXRmb3Jtcy5maW5kKHAgPT4gcC5wbGF0Zm9ybV9pZCA9PT0gcGxhdGZvcm1JZCkKICAgICAgICAgICAgICAgICAgICBpZiAoIXBsYXRmb3JtKSBjb250aW51ZQogICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRldGFpbFJlc3BvbnNlID0gYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCcnLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcGk6ICcvYXBpL3BsYXRmb3JtL2dldERldGFpbCcsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYXJhbTogeyBwbGF0Zm9ybV9pZDogcGxhdGZvcm1JZCB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBvcHRpb25zID0gYXdhaXQgdGhpcy5mZXRjaFBsYXRmb3JtT3B0aW9ucyhwbGF0Zm9ybUlkKQogICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZWZmZWN0UGFyYW1zID0gYXdhaXQgdGhpcy5mZXRjaFBsYXRmb3JtRWZmZWN0UGFyYW1zKHBsYXRmb3JtSWQpCgogICAgICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybXNXaXRoRGV0YWlscy5wdXNoKHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmRldGFpbFJlc3BvbnNlLmRhdGEuZGF0YSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM6IG9wdGlvbnMsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlZmZlY3RQYXJhbXM6IGVmZmVjdFBhcmFtcwogICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHBsYXRmb3JtIGRldGFpbDonLCBlcnJvcikKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg6I635Y+W5bmz5Y+wJHtwbGF0Zm9ybS5wbGF0Zm9ybV9uYW1lfeivpuaDheWksei0pWApCiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW5wdXRQbGF0Zm9ybXMgPSBwbGF0Zm9ybXNXaXRoRGV0YWlscwogICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IHBsYXRmb3JtSWQgb2YgcGxhdGZvcm1JZHMpIHsKICAgICAgICAgICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5pbnB1dF9wbGF0Zm9ybXNfZGF0YVtwbGF0Zm9ybUlkXSkgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLmlucHV0X3BsYXRmb3Jtc19kYXRhLCBwbGF0Zm9ybUlkLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhZGRpdGlvbmFsX0luZm9ybWF0aW9uOiAnJwogICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgaWYgKCF0aGlzLmZvcm0uaW5wdXRfZGF0YV9vcHRpb25zW3BsYXRmb3JtSWRdKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0uaW5wdXRfZGF0YV9vcHRpb25zLCBwbGF0Zm9ybUlkLCBbXSkKICAgICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICAgIGlmICghdGhpcy5mb3JtLmlucHV0X2VmZmVjdF9wYXJhbXNbcGxhdGZvcm1JZF0pIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5pbnB1dF9lZmZlY3RfcGFyYW1zLCBwbGF0Zm9ybUlkLCBbXSkKICAgICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICAgIGNvbnN0IHBsYXRmb3JtV2l0aERldGFpbHMgPSB0aGlzLnNlbGVjdGVkSW5wdXRQbGF0Zm9ybXMuZmluZChwID0+IHAucGxhdGZvcm1faWQgPT09IHBsYXRmb3JtSWQpCiAgICAgICAgICAgICAgICAgICAgaWYgKHBsYXRmb3JtV2l0aERldGFpbHMgJiYgcGxhdGZvcm1XaXRoRGV0YWlscy5maWVsZHMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgcGxhdGZvcm1XaXRoRGV0YWlscy5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZmllbGQucmVxdWlyZWQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBmaWVsZFByb3AgPSBgaW5wdXRfcGxhdGZvcm1zX2RhdGEuJHtwbGF0Zm9ybUlkfS4ke2ZpZWxkLmZpZWxkX25hbWV9YAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVzLCBmaWVsZFByb3AsIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogYOivt+i+k+WFpSR7ZmllbGQubGFiZWx9YCwgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIGhhbmRsZUlucHV0UGxhdGZvcm1DaGFuZ2U6JywgZXJyb3IpCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflpITnkIbovpPlhaXlubPlj7Dlj5jljJbml7blh7rplJknKQogICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nSW5wdXRQbGF0Zm9ybXMgPSBmYWxzZQogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBhc3luYyBoYW5kbGVPdXRwdXRQbGF0Zm9ybUNoYW5nZShwbGF0Zm9ybUlkcykgewogICAgICAgICAgICBpZiAodGhpcy5sb2FkaW5nT3V0cHV0UGxhdGZvcm1zKSB7CiAgICAgICAgICAgICAgICByZXR1cm4KICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmxvYWRpbmdPdXRwdXRQbGF0Zm9ybXMgPSB0cnVlCiAgICAgICAgICAgIAogICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZE91dHB1dFBsYXRmb3JtcyA9IFtdCiAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgIE9iamVjdC5rZXlzKHRoaXMucnVsZXMpLmZvckVhY2goa2V5ID0+IHsKICAgICAgICAgICAgICAgICAgICBpZiAoa2V5LnN0YXJ0c1dpdGgoJ291dHB1dF9wbGF0Zm9ybXNfZGF0YS4nKSAmJiAha2V5LmVuZHNXaXRoKCcuYWRkaXRpb25hbF9JbmZvcm1hdGlvbicpKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJGRlbGV0ZSh0aGlzLnJ1bGVzLCBrZXkpCiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgY29uc3QgcGxhdGZvcm1zV2l0aERldGFpbHMgPSBbXQogICAgICAgICAgICAgICAgZm9yIChjb25zdCBwbGF0Zm9ybUlkIG9mIHBsYXRmb3JtSWRzKSB7CiAgICAgICAgICAgICAgICAgICAgY29uc3QgcGxhdGZvcm0gPSB0aGlzLnBsYXRmb3Jtcy5maW5kKHAgPT4gcC5wbGF0Zm9ybV9pZCA9PT0gcGxhdGZvcm1JZCkKICAgICAgICAgICAgICAgICAgICBpZiAoIXBsYXRmb3JtKSBjb250aW51ZQogICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRldGFpbFJlc3BvbnNlID0gYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCcnLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcGk6ICcvYXBpL3BsYXRmb3JtL2dldERldGFpbCcsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYXJhbTogeyBwbGF0Zm9ybV9pZDogcGxhdGZvcm1JZCB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBvcHRpb25zID0gYXdhaXQgdGhpcy5mZXRjaFBsYXRmb3JtT3B0aW9ucyhwbGF0Zm9ybUlkKQoKICAgICAgICAgICAgICAgICAgICAgICAgcGxhdGZvcm1zV2l0aERldGFpbHMucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5kZXRhaWxSZXNwb25zZS5kYXRhLmRhdGEsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zOiBvcHRpb25zCiAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgcGxhdGZvcm0gZGV0YWlsOicsIGVycm9yKQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDojrflj5blubPlj7Ake3BsYXRmb3JtLnBsYXRmb3JtX25hbWV96K+m5oOF5aSx6LSlYCkKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRPdXRwdXRQbGF0Zm9ybXMgPSBwbGF0Zm9ybXNXaXRoRGV0YWlscwogICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IHBsYXRmb3JtSWQgb2YgcGxhdGZvcm1JZHMpIHsKICAgICAgICAgICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5vdXRwdXRfcGxhdGZvcm1zX2RhdGFbcGxhdGZvcm1JZF0pIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5vdXRwdXRfcGxhdGZvcm1zX2RhdGEsIHBsYXRmb3JtSWQsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFkZGl0aW9uYWxfSW5mb3JtYXRpb246ICcnCiAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5vdXRwdXRfZGF0YV9vcHRpb25zW3BsYXRmb3JtSWRdKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0ub3V0cHV0X2RhdGFfb3B0aW9ucywgcGxhdGZvcm1JZCwgW10pCiAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICAvLyDliJ3lp4vljJbmlrDnmoTmqKHmgIHlkozlj5HluIPlvaLmgIHlrZfmrrUKICAgICAgICAgICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5wbGF0Zm9ybV9tb2RhbGl0aWVzW3BsYXRmb3JtSWRdKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0ucGxhdGZvcm1fbW9kYWxpdGllcywgcGxhdGZvcm1JZCwgW10pCiAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5wbGF0Zm9ybV9wdWJsaXNoX2Zvcm1zW3BsYXRmb3JtSWRdKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0ucGxhdGZvcm1fcHVibGlzaF9mb3JtcywgcGxhdGZvcm1JZCwgJycpCiAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICBjb25zdCBwbGF0Zm9ybVdpdGhEZXRhaWxzID0gdGhpcy5zZWxlY3RlZE91dHB1dFBsYXRmb3Jtcy5maW5kKHAgPT4gcC5wbGF0Zm9ybV9pZCA9PT0gcGxhdGZvcm1JZCkKICAgICAgICAgICAgICAgICAgICBpZiAocGxhdGZvcm1XaXRoRGV0YWlscyAmJiBwbGF0Zm9ybVdpdGhEZXRhaWxzLmZpZWxkcykgewogICAgICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybVdpdGhEZXRhaWxzLmZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChmaWVsZC5yZXF1aXJlZCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkUHJvcCA9IGBvdXRwdXRfcGxhdGZvcm1zX2RhdGEuJHtwbGF0Zm9ybUlkfS4ke2ZpZWxkLmZpZWxkX25hbWV9YAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVzLCBmaWVsZFByb3AsIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogYOivt+i+k+WFpSR7ZmllbGQubGFiZWx9YCwgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIGhhbmRsZU91dHB1dFBsYXRmb3JtQ2hhbmdlOicsIGVycm9yKQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5aSE55CG6L6T5Ye65bmz5Y+w5Y+Y5YyW5pe25Ye66ZSZJykKICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZ091dHB1dFBsYXRmb3JtcyA9IGZhbHNlCiAgICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIG5leHRTdGVwKCkgewogICAgICAgICAgICBpZiAodGhpcy5pc1BsYXRmb3JtQ29uZmlnTG9hZGluZykgewogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCflubPlj7DphY3nva7mraPlnKjliqDovb3kuK3vvIzor7fnqI3lgJkuLi4nKQogICAgICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIGxldCBmaWVsZHNUb1ZhbGlkYXRlID0gW10KCiAgICAgICAgICAgIHN3aXRjaCAodGhpcy5hY3RpdmVTdGVwKSB7CiAgICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICAgICAgZmllbGRzVG9WYWxpZGF0ZSA9IFsnbGlua2VkX3Byb2plY3RfdGVhbV9uYW1lJywgJ2xpbmtlZF90YXNrX3R5cGVfY29kZScsICdzY2VuZV9idXNpbmVzc190eXBlJywgJ3NjZW5lX25hbWUnLCAnc2NlbmVfZGVzY3JpcHRpb24nXQogICAgICAgICAgICAgICAgICAgIGJyZWFrCiAgICAgICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgICAgICAgZmllbGRzVG9WYWxpZGF0ZSA9IFsnYmFzZWxpbmVfZGF0YV9zdGFydF9kYXlzX2FnbycsICdiYXNlbGluZV9kYXRhX2V4Y2x1ZGVfcmVjZW50X2RheXMnLCAnbWluX2Jhc2VsaW5lX3NhbXBsZV9jb3VudCcsICdiYXNlbGluZV9yZWZyZXNoX2ZyZXF1ZW5jeV9kYXlzJ10KICAgICAgICAgICAgICAgICAgICBicmVhawogICAgICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgICAgICAgIGZpZWxkc1RvVmFsaWRhdGUgPSBbJ2lucHV0X3BsYXRmb3JtcyddCiAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZElucHV0UGxhdGZvcm1zLmZvckVhY2gocGxhdGZvcm0gPT4gewogICAgICAgICAgICAgICAgICAgICAgICBpZiAocGxhdGZvcm0uZmllbGRzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybS5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZpZWxkLnJlcXVpcmVkKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkUHJvcCA9IGBpbnB1dF9wbGF0Zm9ybXNfZGF0YS4ke3BsYXRmb3JtLnBsYXRmb3JtX2lkfS4ke2ZpZWxkLmZpZWxkX25hbWV9YAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZHNUb1ZhbGlkYXRlLnB1c2goZmllbGRQcm9wKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHNlbGVjdGVkUGFyYW1zID0gdGhpcy5mb3JtLmlucHV0X2VmZmVjdF9wYXJhbXNbcGxhdGZvcm0ucGxhdGZvcm1faWRdIHx8IFtdCiAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkUGFyYW1zLmZvckVhY2gocGFyYW1Db2RlID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvbmZpZ1ByZWZpeCA9IGBpbnB1dF9lZmZlY3RfcGFyYW1zX2NvbmZpZy4ke3BsYXRmb3JtLnBsYXRmb3JtX2lkfS4ke3BhcmFtQ29kZX1gCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZHNUb1ZhbGlkYXRlLnB1c2goYCR7Y29uZmlnUHJlZml4fS5jb25maWd1cmVkX2V2YWx1YXRpb25fZGF5c2ApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZHNUb1ZhbGlkYXRlLnB1c2goYCR7Y29uZmlnUHJlZml4fS5kZWZhdWx0X2Jhc2VsaW5lX21lYW5gKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGRzVG9WYWxpZGF0ZS5wdXNoKGAke2NvbmZpZ1ByZWZpeH0uZGVmYXVsdF9iYXNlbGluZV9zdGRkZXZgKQogICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgYnJlYWsKICAgICAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICAgICAgICBmaWVsZHNUb1ZhbGlkYXRlID0gWydvdXRwdXRfcGxhdGZvcm1zJ10KCiAgICAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZE91dHB1dFBsYXRmb3Jtcy5mb3JFYWNoKHBsYXRmb3JtID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHBsYXRmb3JtLmZpZWxkcykgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhdGZvcm0uZmllbGRzLmZvckVhY2goZmllbGQgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChmaWVsZC5yZXF1aXJlZCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBmaWVsZFByb3AgPSBgb3V0cHV0X3BsYXRmb3Jtc19kYXRhLiR7cGxhdGZvcm0ucGxhdGZvcm1faWR9LiR7ZmllbGQuZmllbGRfbmFtZX1gCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkc1RvVmFsaWRhdGUucHVzaChmaWVsZFByb3ApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICAgICAgLy8g6aqM6K+B5qih5oCB6YCJ5oupCiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghdGhpcy5mb3JtLnBsYXRmb3JtX21vZGFsaXRpZXNbcGxhdGZvcm0ucGxhdGZvcm1faWRdIHx8CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmZvcm0ucGxhdGZvcm1fbW9kYWxpdGllc1twbGF0Zm9ybS5wbGF0Zm9ybV9pZF0ubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDor7fkuLrlubPlj7AiJHtwbGF0Zm9ybS5wbGF0Zm9ybV9uYW1lfSLpgInmi6noh7PlsJHkuIDnp43mqKHmgIHnsbvlnotgKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOmqjOivgeWPkeW4g+W9ouaAgemAieaLqQogICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5wbGF0Zm9ybV9wdWJsaXNoX2Zvcm1zW3BsYXRmb3JtLnBsYXRmb3JtX2lkXSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg6K+35Li65bmz5Y+wIiR7cGxhdGZvcm0ucGxhdGZvcm1fbmFtZX0i6YCJ5oup5Y+R5biD5b2i5oCBYCkKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZQogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICBicmVhawogICAgICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgICAgICAgIGZpZWxkc1RvVmFsaWRhdGUgPSBbJ3VwZGF0ZWRfcHJvbXB0JywgJ3NjZW5lX3J1bm5pbmdfZnJlcXVlbmN5JywgJ3N0b3JlZF9zdHJhdGVneV9yZWZyZXNoX2RheXMnLCAnZXhwbG9yZV9zdHJhdGVneV90cmlnZ2VyX2RheXMnXQogICAgICAgICAgICAgICAgICAgIGJyZWFrCiAgICAgICAgICAgICAgICBkZWZhdWx0OgogICAgICAgICAgICAgICAgICAgIGJyZWFrCiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIHRoaXMudmFsaWRhdGVGaWVsZHMoZmllbGRzVG9WYWxpZGF0ZSwgKHZhbGlkKSA9PiB7CiAgICAgICAgICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmFjdGl2ZVN0ZXArKwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgIH0sCiAgICAgICAgdmFsaWRhdGVGaWVsZHMoZmllbGRzLCBjYWxsYmFjaykgewogICAgICAgICAgICBpZiAoZmllbGRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgICAgICAgY2FsbGJhY2sodHJ1ZSkKICAgICAgICAgICAgICAgIHJldHVybgogICAgICAgICAgICB9CgogICAgICAgICAgICBjb25zb2xlLmxvZygn6aqM6K+B5a2X5q61OicsIGZpZWxkcykKICAgICAgICAgICAgY29uc29sZS5sb2coJ+W9k+WJjeihqOWNleaVsOaNrjonLCB0aGlzLmZvcm0pCgogICAgICAgICAgICBjb25zdCB2YWxpZGF0aW9uUHJvbWlzZXMgPSBmaWVsZHMubWFwKGZpZWxkID0+IHsKICAgICAgICAgICAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gewogICAgICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZUZpZWxkKGZpZWxkLCAoZXJyb3JNZXNzYWdlKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDlrZfmrrUgJHtmaWVsZH0g6aqM6K+B57uT5p6cOmAsIGVycm9yTWVzc2FnZSkKICAgICAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZSh7IGZpZWxkLCBlcnJvck1lc3NhZ2UgfSkKICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgfSkKCiAgICAgICAgICAgIFByb21pc2UuYWxsKHZhbGlkYXRpb25Qcm9taXNlcykudGhlbihyZXN1bHRzID0+IHsKICAgICAgICAgICAgICAgIGNvbnN0IGhhc0Vycm9yID0gcmVzdWx0cy5zb21lKHJlc3VsdCA9PiByZXN1bHQuZXJyb3JNZXNzYWdlKQogICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+mqjOivgee7k+aenDonLCByZXN1bHRzLCAn5piv5ZCm5pyJ6ZSZ6K+vOicsIGhhc0Vycm9yKQogICAgICAgICAgICAgICAgY2FsbGJhY2soIWhhc0Vycm9yKQogICAgICAgICAgICB9KQogICAgICAgIH0sCiAgICAgICAgcHJldlN0ZXAoKSB7CiAgICAgICAgICAgIHRoaXMuYWN0aXZlU3RlcC0tCiAgICAgICAgfSwKICAgICAgICBhc3luYyBzdWJtaXRGb3JtKCkgewogICAgICAgICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUoYXN5bmMgdmFsaWQgPT4gewogICAgICAgICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgYWNjb3VudHMgPSBbXQoKICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZWZmZWN0UGFyYW1zID0gW10KCiAgICAgICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgcGxhdGZvcm1JZCBvZiB0aGlzLmZvcm0uaW5wdXRfcGxhdGZvcm1zKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwbGF0Zm9ybURhdGEgPSB0aGlzLmZvcm0uaW5wdXRfcGxhdGZvcm1zX2RhdGFbcGxhdGZvcm1JZF0gfHwge30KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRhdGFPcHRpb25zID0gdGhpcy5mb3JtLmlucHV0X2RhdGFfb3B0aW9uc1twbGF0Zm9ybUlkXSB8fCBbXQoKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjY291bnRzLnB1c2goewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJhdGVfdHlwZTogMSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybV9pZDogcGxhdGZvcm1JZCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogbmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygnc3YtU0UnKS5yZXBsYWNlKCdUJywgJyAnKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhZGRpbmdfZGF0YV90eXBlczogZGF0YU9wdGlvbnMubGVuZ3RoID4gMCA/IGRhdGFPcHRpb25zLmpvaW4oJywnKSA6ICfml6AnLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnBsYXRmb3JtRGF0YQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5mb3JtLmlucHV0X2VmZmVjdF9wYXJhbXNfY29uZmlnW3BsYXRmb3JtSWRdKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgT2JqZWN0LnZhbHVlcyh0aGlzLmZvcm0uaW5wdXRfZWZmZWN0X3BhcmFtc19jb25maWdbcGxhdGZvcm1JZF0pLmZvckVhY2gocGFyYW1Db25maWcgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlZmZlY3RQYXJhbXMucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybV9pZDogcGxhdGZvcm1JZCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnBhcmFtQ29uZmlnCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBwbGF0Zm9ybUlkIG9mIHRoaXMuZm9ybS5vdXRwdXRfcGxhdGZvcm1zKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwbGF0Zm9ybURhdGEgPSB0aGlzLmZvcm0ub3V0cHV0X3BsYXRmb3Jtc19kYXRhW3BsYXRmb3JtSWRdIHx8IHt9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRhT3B0aW9ucyA9IHRoaXMuZm9ybS5vdXRwdXRfZGF0YV9vcHRpb25zW3BsYXRmb3JtSWRdIHx8IFtdCgogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWNjb3VudHMucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3BlcmF0ZV90eXBlOiAyLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYXRmb3JtX2lkOiBwbGF0Zm9ybUlkLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZWF0ZV90aW1lOiBuZXcgRGF0ZSgpLnRvTG9jYWxlU3RyaW5nKCdzdi1TRScpLnJlcGxhY2UoJ1QnLCAnICcpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFkZGluZ19kYXRhX3R5cGVzOiBkYXRhT3B0aW9ucy5sZW5ndGggPiAwID8gZGF0YU9wdGlvbnMuam9pbignLCcpIDogJ+aXoCcsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucGxhdGZvcm1EYXRhCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGlucHV0X3BsYXRmb3JtcywgaW5wdXRfZGF0YV9vcHRpb25zLCBpbnB1dF9wbGF0Zm9ybXNfZGF0YSwgb3V0cHV0X2RhdGFfb3B0aW9ucywgb3V0cHV0X3BsYXRmb3Jtcywgb3V0cHV0X3BsYXRmb3Jtc19kYXRhLCBpbnB1dF9lZmZlY3RfcGFyYW1zLCBvdXRwdXRfZWZmZWN0X3BhcmFtcywgaW5wdXRfZWZmZWN0X3BhcmFtc19jb25maWcsIG91dHB1dF9lZmZlY3RfcGFyYW1zX2NvbmZpZywgcGxhdGZvcm1fbW9kYWxpdGllcywgcGxhdGZvcm1fcHVibGlzaF9mb3JtcywgLi4uc3VibWl0RGF0YSB9ID0gdGhpcy5mb3JtCiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuaW5mbyhpbnB1dF9wbGF0Zm9ybXMsIGlucHV0X2RhdGFfb3B0aW9ucywgaW5wdXRfcGxhdGZvcm1zX2RhdGEsIG91dHB1dF9kYXRhX29wdGlvbnMsIG91dHB1dF9wbGF0Zm9ybXMsIG91dHB1dF9wbGF0Zm9ybXNfZGF0YSwgaW5wdXRfZWZmZWN0X3BhcmFtcywgb3V0cHV0X2VmZmVjdF9wYXJhbXMsIGlucHV0X2VmZmVjdF9wYXJhbXNfY29uZmlnLCBvdXRwdXRfZWZmZWN0X3BhcmFtc19jb25maWcsIHBsYXRmb3JtX21vZGFsaXRpZXMsIHBsYXRmb3JtX3B1Ymxpc2hfZm9ybXMpCgogICAgICAgICAgICAgICAgICAgICAgICBzdWJtaXREYXRhLnN0YXRlID0gMjsKICAgICAgICAgICAgICAgICAgICAgICAgc3VibWl0RGF0YS5jcmVhdGVfdGltZSA9IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoJ3N2LVNFJykucmVwbGFjZSgnVCcsICcgJyk7CiAgICAgICAgICAgICAgICAgICAgICAgIHN1Ym1pdERhdGEuY3JlYXRlX3VzZXJfaWQgPSAwOwogICAgICAgICAgICAgICAgICAgICAgICBzdWJtaXREYXRhLmNvbXBhbnlfaWQgPSAwOwoKICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCcnLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcGk6ICcvYXBpL3NjZW5lL2FkZCcsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBzdWJtaXREYXRhLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWNjb3VudHM6IGFjY291bnRzLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgZWZmZWN0X3BhcmFtczogZWZmZWN0UGFyYW1zLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhdGZvcm1fbW9kYWxpdGllczogcGxhdGZvcm1fbW9kYWxpdGllcywKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYXRmb3JtX3B1Ymxpc2hfZm9ybXM6IHBsYXRmb3JtX3B1Ymxpc2hfZm9ybXMKICAgICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflnLrmma/liJvlu7rmiJDlip8nKQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnLycpCiAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Zy65pmv5Yib5bu65aSx6LSlJykKICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgc2NlbmU6JywgZXJyb3IpCiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgIH0sCiAgICAgICAgc2hvd0FkZE9wdGlvbkRpYWxvZyhwbGF0Zm9ybUlkKSB7CiAgICAgICAgICAgIHRoaXMubmV3T3B0aW9uRm9ybSA9IHsKICAgICAgICAgICAgICAgIG9wdGlvbl9uYW1lOiAnJywKICAgICAgICAgICAgICAgIG9wdGlvbl9rZXk6ICcnLAogICAgICAgICAgICAgICAgcGxhdGZvcm1faWQ6IHBsYXRmb3JtSWQKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmFkZE9wdGlvbkRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMub3B0aW9uRm9ybSAmJiB0aGlzLiRyZWZzLm9wdGlvbkZvcm0uY2xlYXJWYWxpZGF0ZSgpCiAgICAgICAgICAgIH0pCiAgICAgICAgfSwKICAgICAgICBhc3luYyBhZGROZXdPcHRpb24oKSB7CiAgICAgICAgICAgIHRoaXMuJHJlZnMub3B0aW9uRm9ybS52YWxpZGF0ZShhc3luYyB2YWxpZCA9PiB7CiAgICAgICAgICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmFkZGluZ09wdGlvbiA9IHRydWUKICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCcnLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcGk6ICcvYXBpL29wdGlvbi9hZGQnLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YTogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnRoaXMubmV3T3B0aW9uRm9ybSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybV9pZDogdGhpcy5uZXdPcHRpb25Gb3JtLnBsYXRmb3JtX2lkCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5paw5aKe5pWw5o2u57G75Z6L5oiQ5YqfJykKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5hZGRPcHRpb25EaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5uZXdPcHRpb25Gb3JtID0gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgb3B0aW9uX25hbWU6ICcnLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgb3B0aW9uX2tleTogJycsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybV9pZDogJycKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCB0aGlzLmhhbmRsZUlucHV0UGxhdGZvcm1DaGFuZ2UodGhpcy5mb3JtLmlucHV0X3BsYXRmb3JtcykKICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgdGhpcy5oYW5kbGVPdXRwdXRQbGF0Zm9ybUNoYW5nZSh0aGlzLmZvcm0ub3V0cHV0X3BsYXRmb3JtcykKICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlrDlop7mlbDmja7nsbvlnovlpLHotKUnKQogICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhZGRpbmcgbmV3IG9wdGlvbjonLCBlcnJvcikKICAgICAgICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmFkZGluZ09wdGlvbiA9IGZhbHNlCiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgIH0sCiAgICAgICAgYXN5bmMgZmV0Y2hNb2RhbGl0eU9wdGlvbnMoKSB7CiAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuJGh0dHAucG9zdCgnJywgewogICAgICAgICAgICAgICAgICAgIGFwaTogJy9hcGkvZGljdC9nZXRMaXN0JywKICAgICAgICAgICAgICAgICAgICBwYXJhbTogewogICAgICAgICAgICAgICAgICAgICAgICBkaWN0X3R5cGU6ICdtb2RhbGl0eScKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgdGhpcy5tb2RhbGl0eU9wdGlvbnMgPSByZXNwb25zZS5kYXRhLmRhdGEgfHwgW10KICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG1vZGFsaXR5IG9wdGlvbnM6JywgZXJyb3IpCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bmqKHmgIHliJfooajlpLHotKUnKQogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBhc3luYyBmZXRjaFByb2plY3RUZWFtcygpIHsKICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCcnLCB7CiAgICAgICAgICAgICAgICAgICAgYXBpOiAnL2FwaS9wcm9qZWN0VGVhbS9nZXRMaXN0JwogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIHRoaXMucHJvamVjdFRlYW1zID0gcmVzcG9uc2UuZGF0YS5kYXRhIHx8IFtdCiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwcm9qZWN0IHRlYW1zOicsIGVycm9yKQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W6aG555uu57uE5YiX6KGo5aSx6LSlJykKICAgICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgYXN5bmMgZmV0Y2hUYXNrVHlwZXMoKSB7CiAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuJGh0dHAucG9zdCgnJywgewogICAgICAgICAgICAgICAgICAgIGFwaTogJy9hcGkvdGFza1R5cGUvZ2V0TGlzdCcKICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICB0aGlzLnRhc2tUeXBlcyA9IHJlc3BvbnNlLmRhdGEuZGF0YSB8fCBbXQogICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdGFzayB0eXBlczonLCBlcnJvcikKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluS7u+WKoeexu+Wei+WIl+ihqOWksei0pScpCiAgICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGFzeW5jIGZldGNoU2NlbmVUeXBlcygpIHsKICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCcnLCB7CiAgICAgICAgICAgICAgICAgICAgYXBpOiAnL2FwaS9zY2VuZVR5cGUvZ2V0TGlzdCcKICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICB0aGlzLnNjZW5lVHlwZXMgPSByZXNwb25zZS5kYXRhLmRhdGEgfHwgW10KICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHNjZW5lIHR5cGVzOicsIGVycm9yKQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5Zy65pmv57G75Z6L5YiX6KGo5aSx6LSlJykKICAgICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgaGFuZGxlUHJvamVjdFRlYW1DaGFuZ2UoKSB7CiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybS5jbGVhclZhbGlkYXRlKCdsaW5rZWRfcHJvamVjdF90ZWFtX25hbWUnKQogICAgICAgICAgICB9KQogICAgICAgIH0sCiAgICAgICAgaGFuZGxlVGFza1R5cGVDaGFuZ2UoKSB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5pbnB1dF9wbGF0Zm9ybXMgPSBbXQogICAgICAgICAgICB0aGlzLmZvcm0ub3V0cHV0X3BsYXRmb3JtcyA9IFtdCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRJbnB1dFBsYXRmb3JtcyA9IFtdCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRPdXRwdXRQbGF0Zm9ybXMgPSBbXQogICAgICAgICAgICB0aGlzLmZvcm0uaW5wdXRfcGxhdGZvcm1zX2RhdGEgPSB7fQogICAgICAgICAgICB0aGlzLmZvcm0ub3V0cHV0X3BsYXRmb3Jtc19kYXRhID0ge30KICAgICAgICAgICAgdGhpcy5mb3JtLmlucHV0X2RhdGFfb3B0aW9ucyA9IHt9CiAgICAgICAgICAgIHRoaXMuZm9ybS5vdXRwdXRfZGF0YV9vcHRpb25zID0ge30KICAgICAgICAgICAgdGhpcy5mb3JtLmlucHV0X2VmZmVjdF9wYXJhbXMgPSB7fQogICAgICAgICAgICB0aGlzLmZvcm0uaW5wdXRfZWZmZWN0X3BhcmFtc19jb25maWcgPSB7fQogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLmZvcm0uY2xlYXJWYWxpZGF0ZSgnbGlua2VkX3Rhc2tfdHlwZV9jb2RlJykKICAgICAgICAgICAgfSkKICAgICAgICB9LAogICAgICAgIGhhbmRsZUVmZmVjdFBhcmFtc0NoYW5nZShwbGF0Zm9ybUlkKSB7CiAgICAgICAgICAgIGNvbnN0IHNlbGVjdGVkUGFyYW1zID0gdGhpcy5mb3JtLmlucHV0X2VmZmVjdF9wYXJhbXNbcGxhdGZvcm1JZF0gfHwgW10KCiAgICAgICAgICAgIGlmICghdGhpcy5mb3JtLmlucHV0X2VmZmVjdF9wYXJhbXNfY29uZmlnW3BsYXRmb3JtSWRdKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLmlucHV0X2VmZmVjdF9wYXJhbXNfY29uZmlnLCBwbGF0Zm9ybUlkLCB7fSkKICAgICAgICAgICAgfQoKICAgICAgICAgICAgY29uc3QgcGxhdGZvcm0gPSB0aGlzLnNlbGVjdGVkSW5wdXRQbGF0Zm9ybXMuZmluZChwID0+IHAucGxhdGZvcm1faWQgPT09IHBsYXRmb3JtSWQpCiAgICAgICAgICAgIGNvbnN0IHBsYXRmb3JtUGFyYW1zID0gcGxhdGZvcm0gPyAocGxhdGZvcm0uZWZmZWN0UGFyYW1zIHx8IFtdKSA6IFtdCgogICAgICAgICAgICBpZiAocGxhdGZvcm1QYXJhbXMubGVuZ3RoID09IDApIHsKICAgICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgT2JqZWN0LmtleXModGhpcy5ydWxlcykuZm9yRWFjaChrZXkgPT4gewogICAgICAgICAgICAgICAgaWYgKGtleS5zdGFydHNXaXRoKGBpbnB1dF9lZmZlY3RfcGFyYW1zX2NvbmZpZy4ke3BsYXRmb3JtSWR9LmApKSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kZGVsZXRlKHRoaXMucnVsZXMsIGtleSkKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKCiAgICAgICAgICAgIHNlbGVjdGVkUGFyYW1zLmZvckVhY2gocGFyYW0gPT4gewogICAgICAgICAgICAgICAgaWYgKCF0aGlzLmZvcm0uaW5wdXRfZWZmZWN0X3BhcmFtc19jb25maWdbcGxhdGZvcm1JZF1bcGFyYW1dKSB7CgogICAgICAgICAgICAgICAgICAgIGNvbnN0IGVmZmVjdFBhcmFtID0gcGxhdGZvcm1QYXJhbXMuZmluZChwID0+IHAuZWZmZWN0X3BhcmFtX2NvZGUgPT09IHBhcmFtKTsKCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5pbnB1dF9lZmZlY3RfcGFyYW1zX2NvbmZpZ1twbGF0Zm9ybUlkXSwgcGFyYW0sIHsKICAgICAgICAgICAgICAgICAgICAgICAgZWZmZWN0X3BhcmFtX2NvZGU6IGVmZmVjdFBhcmFtLmVmZmVjdF9wYXJhbV9jb2RlLAogICAgICAgICAgICAgICAgICAgICAgICBlZmZlY3RfcGFyYW1fbmFtZTogZWZmZWN0UGFyYW0uZWZmZWN0X3BhcmFtX25hbWUsCiAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyZWRfZXZhbHVhdGlvbl9kYXlzOiAnJywKICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdF9iYXNlbGluZV9tZWFuOiAnJywKICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdF9iYXNlbGluZV9zdGRkZXY6ICcnCiAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBjb25zdCBjb25maWdQcmVmaXggPSBgaW5wdXRfZWZmZWN0X3BhcmFtc19jb25maWcuJHtwbGF0Zm9ybUlkfS4ke3BhcmFtfWAKICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVzLCBgJHtjb25maWdQcmVmaXh9LmNvbmZpZ3VyZWRfZXZhbHVhdGlvbl9kYXlzYCwgWwogICAgICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXmlYjmnpzlrp7njrDlpKnmlbAnLCB0cmlnZ2VyOiAnYmx1cicgfSwKICAgICAgICAgICAgICAgICAgICB7IHBhdHRlcm46IC9eW1xkLFxzXSskLywgbWVzc2FnZTogJ+ivt+i+k+WFpeacieaViOeahOWkqeaVsOagvOW8j++8jOWmgu+8mjMsNSwxMCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMucnVsZXMsIGAke2NvbmZpZ1ByZWZpeH0uZGVmYXVsdF9iYXNlbGluZV9tZWFuYCwgWwogICAgICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlubPlnYflgLwnLCB0cmlnZ2VyOiAnYmx1cicgfSwKICAgICAgICAgICAgICAgICAgICB7IHBhdHRlcm46IC9eKC0/WzEtOV1cZCooXC5cZCpbMS05XSk/KXwoLT8wXC5cZCpbMS05XSkkLywgbWVzc2FnZTogJ+W5s+Wdh+WAvOW/hemhu+aYr+aVsOWtlycsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMucnVsZXMsIGAke2NvbmZpZ1ByZWZpeH0uZGVmYXVsdF9iYXNlbGluZV9zdGRkZXZgLCBbCiAgICAgICAgICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeagh+WHhuW3ricsIHRyaWdnZXI6ICdibHVyJyB9LAogICAgICAgICAgICAgICAgICAgIHsgcGF0dGVybjogL14oLT9bMS05XVxkKihcLlxkKlsxLTldKT8pfCgtPzBcLlxkKlsxLTldKSQvLCBtZXNzYWdlOiAn5qCH5YeG5beu5b+F6aG75piv5pWw5a2XJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIAogICAgICAgICAgICBPYmplY3Qua2V5cyh0aGlzLmZvcm0uaW5wdXRfZWZmZWN0X3BhcmFtc19jb25maWdbcGxhdGZvcm1JZF0pLmZvckVhY2gocGFyYW0gPT4gewogICAgICAgICAgICAgICAgaWYgKCFzZWxlY3RlZFBhcmFtcy5pbmNsdWRlcyhwYXJhbSkpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRkZWxldGUodGhpcy5mb3JtLmlucHV0X2VmZmVjdF9wYXJhbXNfY29uZmlnW3BsYXRmb3JtSWRdLCBwYXJhbSkKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICB9LAogICAgICAgIGdldEVmZmVjdFBhcmFtc1RhYmxlRGF0YShwbGF0Zm9ybUlkKSB7CiAgICAgICAgICAgIGNvbnN0IHNlbGVjdGVkUGFyYW1zID0gdGhpcy5mb3JtLmlucHV0X2VmZmVjdF9wYXJhbXNbcGxhdGZvcm1JZF0gfHwgW10KICAgICAgICAgICAgY29uc3QgY29uZmlnID0gdGhpcy5mb3JtLmlucHV0X2VmZmVjdF9wYXJhbXNfY29uZmlnW3BsYXRmb3JtSWRdIHx8IHt9CiAgICAgICAgICAgIAogICAgICAgICAgICByZXR1cm4gc2VsZWN0ZWRQYXJhbXMubWFwKHBhcmFtQ29kZSA9PiAoewogICAgICAgICAgICAgICAgZWZmZWN0X3BhcmFtX2NvZGU6IHBhcmFtQ29kZSwKICAgICAgICAgICAgICAgIGVmZmVjdF9wYXJhbV9uYW1lOiAoY29uZmlnW3BhcmFtQ29kZV0gJiYgY29uZmlnW3BhcmFtQ29kZV0uZWZmZWN0X3BhcmFtX25hbWUpIHx8ICcnLAogICAgICAgICAgICAgICAgY29uZmlndXJlZF9ldmFsdWF0aW9uX2RheXM6IChjb25maWdbcGFyYW1Db2RlXSAmJiBjb25maWdbcGFyYW1Db2RlXS5jb25maWd1cmVkX2V2YWx1YXRpb25fZGF5cykgfHwgJycsCiAgICAgICAgICAgICAgICBkZWZhdWx0X2Jhc2VsaW5lX21lYW46IChjb25maWdbcGFyYW1Db2RlXSAmJiBjb25maWdbcGFyYW1Db2RlXS5kZWZhdWx0X2Jhc2VsaW5lX21lYW4pIHx8ICcnLAogICAgICAgICAgICAgICAgZGVmYXVsdF9iYXNlbGluZV9zdGRkZXY6IChjb25maWdbcGFyYW1Db2RlXSAmJiBjb25maWdbcGFyYW1Db2RlXS5kZWZhdWx0X2Jhc2VsaW5lX3N0ZGRldikgfHwgJycKICAgICAgICAgICAgfSkpCiAgICAgICAgfSwKICAgICAgICB1cGRhdGVFZmZlY3RQYXJhbUNvbmZpZyhwbGF0Zm9ybUlkLCBwYXJhbU5hbWUsIGZpZWxkLCB2YWx1ZSkgewogICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5pbnB1dF9lZmZlY3RfcGFyYW1zX2NvbmZpZ1twbGF0Zm9ybUlkXSkgewogICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5pbnB1dF9lZmZlY3RfcGFyYW1zX2NvbmZpZywgcGxhdGZvcm1JZCwge30pCiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKCF0aGlzLmZvcm0uaW5wdXRfZWZmZWN0X3BhcmFtc19jb25maWdbcGxhdGZvcm1JZF1bcGFyYW1OYW1lXSkgewogICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5pbnB1dF9lZmZlY3RfcGFyYW1zX2NvbmZpZ1twbGF0Zm9ybUlkXSwgcGFyYW1OYW1lLCB7fSkKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLmlucHV0X2VmZmVjdF9wYXJhbXNfY29uZmlnW3BsYXRmb3JtSWRdW3BhcmFtTmFtZV0sIGZpZWxkLCB2YWx1ZSkKICAgICAgICB9LAogICAgICAgIGdldE1pblZhbHVlKCkgewogICAgICAgICAgICBzd2l0Y2ggKHRoaXMuZnJlcXVlbmN5VW5pdCkgewogICAgICAgICAgICAgICAgY2FzZSAnbWludXRlcyc6CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDMwCiAgICAgICAgICAgICAgICBjYXNlICdob3Vycyc6CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDEKICAgICAgICAgICAgICAgIGNhc2UgJ2RheXMnOgogICAgICAgICAgICAgICAgICAgIHJldHVybiAxCiAgICAgICAgICAgICAgICBkZWZhdWx0OgogICAgICAgICAgICAgICAgICAgIHJldHVybiAxCiAgICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGdldE1heFZhbHVlKCkgewogICAgICAgICAgICBzd2l0Y2ggKHRoaXMuZnJlcXVlbmN5VW5pdCkgewogICAgICAgICAgICAgICAgY2FzZSAnbWludXRlcyc6CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDE0NDAKICAgICAgICAgICAgICAgIGNhc2UgJ2hvdXJzJzoKICAgICAgICAgICAgICAgICAgICByZXR1cm4gMjQKICAgICAgICAgICAgICAgIGNhc2UgJ2RheXMnOgogICAgICAgICAgICAgICAgICAgIHJldHVybiAzNjUKICAgICAgICAgICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDEKICAgICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgZ2V0U3RlcCgpIHsKICAgICAgICAgICAgc3dpdGNoICh0aGlzLmZyZXF1ZW5jeVVuaXQpIHsKICAgICAgICAgICAgICAgIGNhc2UgJ21pbnV0ZXMnOgogICAgICAgICAgICAgICAgICAgIHJldHVybiAzMAogICAgICAgICAgICAgICAgY2FzZSAnaG91cnMnOgogICAgICAgICAgICAgICAgICAgIHJldHVybiAxCiAgICAgICAgICAgICAgICBjYXNlICdkYXlzJzoKICAgICAgICAgICAgICAgICAgICByZXR1cm4gMQogICAgICAgICAgICAgICAgZGVmYXVsdDoKICAgICAgICAgICAgICAgICAgICByZXR1cm4gMQogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBjYWxjdWxhdGVUb3RhbE1pbnV0ZXMoKSB7CiAgICAgICAgICAgIGxldCB0b3RhbE1pbnV0ZXMgPSAwCiAgICAgICAgICAgIHN3aXRjaCAodGhpcy5mcmVxdWVuY3lVbml0KSB7CiAgICAgICAgICAgICAgICBjYXNlICdtaW51dGVzJzoKICAgICAgICAgICAgICAgICAgICB0b3RhbE1pbnV0ZXMgPSB0aGlzLmZyZXF1ZW5jeVZhbHVlCiAgICAgICAgICAgICAgICAgICAgYnJlYWsKICAgICAgICAgICAgICAgIGNhc2UgJ2hvdXJzJzoKICAgICAgICAgICAgICAgICAgICB0b3RhbE1pbnV0ZXMgPSB0aGlzLmZyZXF1ZW5jeVZhbHVlICogNjAKICAgICAgICAgICAgICAgICAgICBicmVhawogICAgICAgICAgICAgICAgY2FzZSAnZGF5cyc6CiAgICAgICAgICAgICAgICAgICAgdG90YWxNaW51dGVzID0gdGhpcy5mcmVxdWVuY3lWYWx1ZSAqIDI0ICogNjAKICAgICAgICAgICAgICAgICAgICBicmVhawogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICBpZiAodG90YWxNaW51dGVzIDwgMzApIHsKICAgICAgICAgICAgICAgIHRvdGFsTWludXRlcyA9IDMwCiAgICAgICAgICAgICAgICB0aGlzLmZyZXF1ZW5jeVZhbHVlID0gMzAKICAgICAgICAgICAgICAgIHRoaXMuZnJlcXVlbmN5VW5pdCA9ICdtaW51dGVzJwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICB0aGlzLmZvcm0uc2NlbmVfcnVubmluZ19mcmVxdWVuY3kgPSB0b3RhbE1pbnV0ZXMKICAgICAgICAgICAgcmV0dXJuIHRvdGFsTWludXRlcwogICAgICAgIH0sCiAgICAgICAgcGFyc2VGcmVxdWVuY3lGcm9tTWludXRlcyhtaW51dGVzKSB7CiAgICAgICAgICAgIGlmICghbWludXRlcyB8fCBtaW51dGVzIDwgMzApIHsKICAgICAgICAgICAgICAgIHRoaXMuZnJlcXVlbmN5VmFsdWUgPSAzMAogICAgICAgICAgICAgICAgdGhpcy5mcmVxdWVuY3lVbml0ID0gJ21pbnV0ZXMnCiAgICAgICAgICAgICAgICByZXR1cm4KICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgaWYgKG1pbnV0ZXMgPj0gMTQ0MCAmJiBtaW51dGVzICUgMTQ0MCA9PT0gMCkgewogICAgICAgICAgICAgICAgdGhpcy5mcmVxdWVuY3lWYWx1ZSA9IG1pbnV0ZXMgLyAxNDQwCiAgICAgICAgICAgICAgICB0aGlzLmZyZXF1ZW5jeVVuaXQgPSAnZGF5cycKICAgICAgICAgICAgfSBlbHNlIGlmIChtaW51dGVzID49IDYwICYmIG1pbnV0ZXMgJSA2MCA9PT0gMCkgewogICAgICAgICAgICAgICAgdGhpcy5mcmVxdWVuY3lWYWx1ZSA9IG1pbnV0ZXMgLyA2MAogICAgICAgICAgICAgICAgdGhpcy5mcmVxdWVuY3lVbml0ID0gJ2hvdXJzJwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgdGhpcy5mcmVxdWVuY3lWYWx1ZSA9IG1pbnV0ZXMKICAgICAgICAgICAgICAgIHRoaXMuZnJlcXVlbmN5VW5pdCA9ICdtaW51dGVzJwogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBoYW5kbGVGcmVxdWVuY3lWYWx1ZUNoYW5nZSgpIHsKICAgICAgICAgICAgdGhpcy5jYWxjdWxhdGVUb3RhbE1pbnV0ZXMoKQogICAgICAgIH0sCiAgICAgICAgaGFuZGxlRnJlcXVlbmN5VW5pdENoYW5nZSgpIHsKICAgICAgICAgICAgY29uc3QgY3VycmVudE1pbnV0ZXMgPSB0aGlzLmNhbGN1bGF0ZVRvdGFsTWludXRlcygpCiAgICAgICAgICAgIAogICAgICAgICAgICB0aGlzLnBhcnNlRnJlcXVlbmN5RnJvbU1pbnV0ZXMoY3VycmVudE1pbnV0ZXMpCiAgICAgICAgICAgIHRoaXMuY2FsY3VsYXRlVG90YWxNaW51dGVzKCkKICAgICAgICB9LAogICAgICAgIHNob3dBZGRQcm9qZWN0RGlhbG9nKCkgewogICAgICAgICAgICB0aGlzLm5ld1Byb2plY3RGb3JtID0gewogICAgICAgICAgICAgICAgcHJvamVjdF9uYW1lOiAnJwogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuYWRkUHJvamVjdERpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMucHJvamVjdEZvcm0gJiYgdGhpcy4kcmVmcy5wcm9qZWN0Rm9ybS5jbGVhclZhbGlkYXRlKCkKICAgICAgICAgICAgfSkKICAgICAgICB9LAogICAgICAgIGFzeW5jIGFkZE5ld1Byb2plY3QoKSB7CiAgICAgICAgICAgIHRoaXMuJHJlZnMucHJvamVjdEZvcm0udmFsaWRhdGUoYXN5bmMgdmFsaWQgPT4gewogICAgICAgICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5hZGRpbmdQcm9qZWN0ID0gdHJ1ZQogICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCB0aGlzLiRodHRwLnBvc3QoJycsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFwaTogJy9hcGkvcHJvamVjdFRlYW0vYWRkJywKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IHRoaXMubmV3UHJvamVjdEZvcm0KICAgICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlrDlop7pobnnm67nu4TmiJDlip8nKQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmFkZFByb2plY3REaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgdGhpcy5mZXRjaFByb2plY3RUZWFtcygpCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5saW5rZWRfcHJvamVjdF90ZWFtX25hbWUgPSB0aGlzLm5ld1Byb2plY3RGb3JtLnByb2plY3RfbmFtZQogICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWsOWinumhueebrue7hOWksei0pScpCiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFkZGluZyBuZXcgcHJvamVjdDonLCBlcnJvcikKICAgICAgICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmFkZGluZ1Byb2plY3QgPSBmYWxzZQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICB9LAogICAgICAgIHNob3dBZGRUYXNrVHlwZURpYWxvZygpIHsKICAgICAgICAgICAgdGhpcy5uZXdUYXNrVHlwZUZvcm0gPSB7CiAgICAgICAgICAgICAgICB0YXNrX3R5cGVfbmFtZTogJycsCiAgICAgICAgICAgICAgICB0YXNrX3R5cGVfZGVzY3JpcHRpb246ICcnLAogICAgICAgICAgICAgICAgcmVjb21tZW5kZWRfZWZmZWN0X3BhcmFtX2NvZGVzOiAnJywKICAgICAgICAgICAgICAgIGVmZmVjdF9wYXJhbV9yZWxhdGlvbnNoaXBzX25vdGU6ICcnLAogICAgICAgICAgICAgICAgaXNfY29udGVudF9mcm9tX2V4dGVybmFsOiAnMScsCiAgICAgICAgICAgICAgICBpc19iaWxhdGVyYWxfcHJlZl9jb25zaWRlcmF0aW9uX25lZWRlZDogJzAnLAogICAgICAgICAgICAgICAgbGlua2VkX3BsYXRmb3JtX2lkczogJycsCiAgICAgICAgICAgICAgICB0YXNrX3R5cGVfc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgdGFza190eXBlX293bmVyOiAyCiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5zZWxlY3RlZFBsYXRmb3JtSWRzID0gW10KICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEVmZmVjdFBhcmFtcyA9IFtdCiAgICAgICAgICAgIHRoaXMuYXZhaWxhYmxlRWZmZWN0UGFyYW1zRm9yVGFza1R5cGUgPSBbXQogICAgICAgICAgICB0aGlzLmFkZFRhc2tUeXBlRGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy50YXNrVHlwZUZvcm0gJiYgdGhpcy4kcmVmcy50YXNrVHlwZUZvcm0uY2xlYXJWYWxpZGF0ZSgpCiAgICAgICAgICAgIH0pCiAgICAgICAgfSwKICAgICAgICBhc3luYyBoYW5kbGVQbGF0Zm9ybVNlbGVjdENoYW5nZShzZWxlY3RlZElkcykgewogICAgICAgICAgICB0aGlzLm5ld1Rhc2tUeXBlRm9ybS5saW5rZWRfcGxhdGZvcm1faWRzID0gc2VsZWN0ZWRJZHMuam9pbignLCcpCiAgICAgICAgICAgIAogICAgICAgICAgICBhd2FpdCB0aGlzLmZldGNoRWZmZWN0UGFyYW1zRm9yVGFza1R5cGUoc2VsZWN0ZWRJZHMpCiAgICAgICAgfSwKICAgICAgICBhc3luYyBmZXRjaEVmZmVjdFBhcmFtc0ZvclRhc2tUeXBlKHBsYXRmb3JtSWRzKSB7CiAgICAgICAgICAgIGlmIChwbGF0Zm9ybUlkcy5sZW5ndGggPT09IDApIHsKICAgICAgICAgICAgICAgIHRoaXMuYXZhaWxhYmxlRWZmZWN0UGFyYW1zRm9yVGFza1R5cGUgPSBbXQogICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEVmZmVjdFBhcmFtcyA9IFtdCiAgICAgICAgICAgICAgICB0aGlzLm5ld1Rhc2tUeXBlRm9ybS5yZWNvbW1lbmRlZF9lZmZlY3RfcGFyYW1fY29kZXMgPSAnJwogICAgICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICBjb25zdCBwbGF0Zm9ybVBhcmFtc0FycmF5cyA9IFtdCiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IHBsYXRmb3JtSWQgb2YgcGxhdGZvcm1JZHMpIHsKICAgICAgICAgICAgICAgICAgICBjb25zdCBwYXJhbXMgPSBhd2FpdCB0aGlzLmZldGNoUGxhdGZvcm1FZmZlY3RQYXJhbXMocGxhdGZvcm1JZCkKICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybVBhcmFtc0FycmF5cy5wdXNoKHBhcmFtcykKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgY29uc3QgYWxsUGFyYW1zID0gW10KICAgICAgICAgICAgICAgIGNvbnN0IHNlZW5Db2RlcyA9IG5ldyBTZXQoKQogICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICBwbGF0Zm9ybVBhcmFtc0FycmF5cy5mb3JFYWNoKHBsYXRmb3JtUGFyYW1zID0+IHsKICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybVBhcmFtcy5mb3JFYWNoKHBhcmFtID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFzZWVuQ29kZXMuaGFzKHBhcmFtLmVmZmVjdF9wYXJhbV9jb2RlKSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VlbkNvZGVzLmFkZChwYXJhbS5lZmZlY3RfcGFyYW1fY29kZSkKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsbFBhcmFtcy5wdXNoKHBhcmFtKQogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgIHRoaXMuYXZhaWxhYmxlRWZmZWN0UGFyYW1zRm9yVGFza1R5cGUgPSBhbGxQYXJhbXMKICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEVmZmVjdFBhcmFtcyA9IFtdCiAgICAgICAgICAgICAgICB0aGlzLm5ld1Rhc2tUeXBlRm9ybS5yZWNvbW1lbmRlZF9lZmZlY3RfcGFyYW1fY29kZXMgPSAnJwogICAgICAgICAgICAgICAgCiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBlZmZlY3QgcGFyYW1zIGZvciB0YXNrIHR5cGU6JywgZXJyb3IpCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bmlYjmnpzlj4LmlbDlpLHotKUnKQogICAgICAgICAgICAgICAgdGhpcy5hdmFpbGFibGVFZmZlY3RQYXJhbXNGb3JUYXNrVHlwZSA9IFtdCiAgICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGhhbmRsZUVmZmVjdFBhcmFtc1NlbGVjdENoYW5nZShzZWxlY3RlZENvZGVzKSB7CiAgICAgICAgICAgIHRoaXMubmV3VGFza1R5cGVGb3JtLnJlY29tbWVuZGVkX2VmZmVjdF9wYXJhbV9jb2RlcyA9IEpTT04uc3RyaW5naWZ5KHNlbGVjdGVkQ29kZXMpCiAgICAgICAgfSwKICAgICAgICBhc3luYyBhZGROZXdUYXNrVHlwZSgpIHsKICAgICAgICAgICAgdGhpcy4kcmVmcy50YXNrVHlwZUZvcm0udmFsaWRhdGUoYXN5bmMgdmFsaWQgPT4gewogICAgICAgICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5hZGRpbmdUYXNrVHlwZSA9IHRydWUKICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCcnLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcGk6ICcvYXBpL3Rhc2tUeXBlL2FkZCcsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiB0aGlzLm5ld1Rhc2tUeXBlRm9ybQogICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWsOWinuS7u+WKoeexu+Wei+aIkOWKnycpCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuYWRkVGFza1R5cGVEaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgdGhpcy5mZXRjaFRhc2tUeXBlcygpCiAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paw5aKe5Lu75Yqh57G75Z6L5aSx6LSlJykKICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWRkaW5nIG5ldyB0YXNrIHR5cGU6JywgZXJyb3IpCiAgICAgICAgICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5hZGRpbmdUYXNrVHlwZSA9IGZhbHNlCiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgIH0KICAgIH0sCiAgICBhc3luYyBjcmVhdGVkKCkgewogICAgICAgIGF3YWl0IHRoaXMuZmV0Y2hQbGF0Zm9ybXMoeyBwYWdlOiAxLCBwYWdlU2l6ZTogMTAwIH0pCiAgICAgICAgYXdhaXQgdGhpcy5mZXRjaFByb2plY3RUZWFtcygpCiAgICAgICAgYXdhaXQgdGhpcy5mZXRjaFRhc2tUeXBlcygpCiAgICAgICAgYXdhaXQgdGhpcy5mZXRjaE1vZGFsaXR5T3B0aW9ucygpCiAgICAgICAgYXdhaXQgdGhpcy5mZXRjaFNjZW5lVHlwZXMoKQogICAgICAgIAogICAgICAgIHRoaXMuY2FsY3VsYXRlVG90YWxNaW51dGVzKCkKICAgIH0KfQo="}, {"version": 3, "sources": ["NewScene.vue"], "names": [], "mappings": ";AAylBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "NewScene.vue", "sourceRoot": "src/views/scene", "sourcesContent": ["<template>\n    <div class=\"new-scene\">\n        <el-steps :active=\"activeStep\" finish-status=\"success\" simple>\n            <el-step title=\"基本信息\" />\n            <el-step title=\"计算基准线配置\" />\n            <el-step title=\"数据输入平台\" />\n            <el-step title=\"数据输出平台\" />\n            <el-step title=\"其他设置\" />\n        </el-steps>\n\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"150px\" class=\"mt-20\" v-loading=\"loading\">\n            <div v-show=\"activeStep === 0\">\n                <el-form-item label=\"场景名称\" prop=\"scene_name\">\n                    <el-input v-model=\"form.scene_name\" placeholder=\"请输入场景名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型\" prop=\"linked_task_type_code\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_task_type_code\" placeholder=\"请选择任务类型\" style=\"flex: 1;\" @change=\"handleTaskTypeChange\">\n                            <el-option\n                                v-for=\"taskType in taskTypes\"\n                                :key=\"taskType.task_type_code\"\n                                :label=\"taskType.task_type_name\"\n                                :value=\"taskType.task_type_code\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddTaskTypeDialog\">\n                            新增任务类型\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景类型\" prop=\"scene_business_type\">\n                    <el-select \n                        v-model=\"form.scene_business_type\" \n                        placeholder=\"请选择或输入场景类型\" \n                        filterable \n                        allow-create \n                        default-first-option\n                        style=\"width: 100%\">\n                        <el-option\n                            v-for=\"sceneType in sceneTypes\"\n                            :key=\"sceneType\"\n                            :label=\"sceneType\"\n                            :value=\"sceneType\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"项目组\" prop=\"linked_project_team_name\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_project_team_name\" placeholder=\"请选择项目组\" style=\"flex: 1;\" @change=\"handleProjectTeamChange\">\n                            <el-option\n                                v-for=\"project in projectTeams\"\n                                :key=\"project.project_id\"\n                                :label=\"project.project_name\"\n                                :value=\"project.project_name\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddProjectDialog\">\n                            新增项目组\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景描述\" prop=\"scene_description\">\n                    <el-input type=\"textarea\" v-model=\"form.scene_description\" placeholder=\"请输入场景描述\"\n                        :rows=\"4\"></el-input>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 1\">\n                <el-form-item label=\"使用数据的起始天数\" prop=\"baseline_data_start_days_ago\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_start_days_ago\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线使用数据的起始天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"排除最近的数据天数\" prop=\"baseline_data_exclude_recent_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_exclude_recent_days\"\n                            :min=\"0\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线排除最近的数据天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"样本量最小阈值\" prop=\"min_baseline_sample_count\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.min_baseline_sample_count\"\n                            :min=\"1\"\n                            :max=\"10000\"\n                            :step=\"1\"\n                            placeholder=\"请输入样本量\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">个</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"一个场景在一次计算基准线时，所需要的最小样本量\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"基准线更新频率\" prop=\"baseline_refresh_frequency_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_refresh_frequency_days\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入频率\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"评估效果的基准线更新的频率\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 2\">\n                <el-form-item label=\"选择输入平台\" prop=\"input_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.input_platforms\" @change=\"handleInputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingInputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedInputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingInputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输入平台配置</h3>\n                    <el-card v-for=\"platform in selectedInputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <div class=\"field-container\">\n                                <component :is=\"getFieldComponent(field.field_type)\"\n                                    v-model=\"form.input_platforms_data[platform.platform_id][field.field_name]\"\n                                    v-bind=\"getFieldProps(field)\"></component>\n                                <div v-if=\"field.description\" class=\"field-description\">{{ field.description }}</div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据类型\" :prop=\"'input_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.input_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\"\n                                    @click=\"showAddOptionDialog(platform.platform_id)\">\n                                    新增数据类型\n                                </el-button>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"效果参数配置\">\n                            <div class=\"effect-params-container\">\n                                <div v-if=\"getAvailableEffectParamsForPlatform(platform.platform_id).length === 0\" class=\"no-effect-params-tip\">\n                                    <el-alert\n                                        title=\"该平台暂无可用的效果参数\"\n                                        type=\"info\"\n                                        :closable=\"false\"\n                                        show-icon>\n                                    </el-alert>\n                                </div>\n                                <el-checkbox-group v-else v-model=\"form.input_effect_params[platform.platform_id]\" @change=\"handleEffectParamsChange(platform.platform_id)\">\n                                    <el-checkbox v-for=\"param in getAvailableEffectParamsForPlatform(platform.platform_id)\" :key=\"param.effect_param_code\" :label=\"param.effect_param_code\" class=\"effect-param-checkbox\">\n                                        {{ param.effect_param_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                \n                                <div v-if=\"form.input_effect_params[platform.platform_id] && form.input_effect_params[platform.platform_id].length > 0\" class=\"effect-params-table\">\n                                    <h4>参数配置详情</h4>\n                                    <el-table :data=\"getEffectParamsTableData(platform.platform_id)\" border>\n                                        <el-table-column prop=\"effect_param_name\" label=\"参数名称\" width=\"120\"></el-table-column>\n                                        <el-table-column prop=\"configured_evaluation_days\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"系统会获取发布时间在T-基线范围内，且已满足各参数的Tij值的样本总数量。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *效果实现天数\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.configured_evaluation_days`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.configured_evaluation_days\" \n                                                        placeholder=\"如：3,5,10\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'configured_evaluation_days', scope.row.configured_evaluation_days)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_mean\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"μi是选取的样本量的效果数据里，该参数的平均值。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算μi，而是会使用您输入的缺省状态下的μi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *平均值\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_mean`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.default_baseline_mean\" \n                                                        placeholder=\"如：0\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_mean', scope.row.default_baseline_mean)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_stddev\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"σi是选取的样本量的效果数据里，该参数的标准差。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算σi，而是会使用您输入的缺省状态下的σi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *标准差\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_stddev`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input\n                                                        v-model=\"scope.row.default_baseline_stddev\" \n                                                        placeholder=\"如：1\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_stddev', scope.row.default_baseline_stddev)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                    </el-table>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.input_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n            </div>\n\n            <div v-show=\"activeStep === 3\">\n                <el-form-item label=\"选择输出平台\" prop=\"output_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.output_platforms\" @change=\"handleOutputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingOutputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedOutputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingOutputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输出平台配置</h3>\n                    <el-card v-for=\"platform in selectedOutputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <div class=\"field-container\">\n                                <component :is=\"getFieldComponent(field.field_type)\"\n                                    v-model=\"form.output_platforms_data[platform.platform_id][field.field_name]\"\n                                    v-bind=\"getFieldProps(field)\"></component>\n                                <div v-if=\"field.description\" class=\"field-description\">{{ field.description }}</div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据内容\" :prop=\"'output_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.output_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"多模态内容\" :prop=\"'platform_modalities.' + platform.platform_id\">\n                            <div class=\"modality-selection-container\">\n                                <div class=\"selection-row\">\n                                    <el-select\n                                        v-model=\"tempModalitySelection[platform.platform_id]\"\n                                        placeholder=\"请选择模态类型\"\n                                        style=\"width: 300px;\"\n                                        @change=\"handleModalitySelect(platform.platform_id, $event)\">\n                                        <el-option\n                                            v-for=\"modality in MODALITY_TYPES\"\n                                            :key=\"modality\"\n                                            :label=\"modality\"\n                                            :value=\"modality\"\n                                            :disabled=\"form.platform_modalities[platform.platform_id] && form.platform_modalities[platform.platform_id].includes(modality)\">\n                                        </el-option>\n                                    </el-select>\n                                    <div class=\"selected-tags\">\n                                        <el-tag\n                                            v-for=\"modality in form.platform_modalities[platform.platform_id] || []\"\n                                            :key=\"modality\"\n                                            closable\n                                            @close=\"removeModality(platform.platform_id, modality)\"\n                                            style=\"margin-right: 8px; margin-bottom: 8px;\">\n                                            {{ modality }}\n                                        </el-tag>\n                                    </div>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"发布形态\" :prop=\"'platform_publish_forms.' + platform.platform_id\">\n                            <div class=\"publish-form-selection-container\">\n                                <div class=\"selection-row\">\n                                    <el-select\n                                        v-model=\"form.platform_publish_forms[platform.platform_id]\"\n                                        placeholder=\"请选择发布形态\"\n                                        style=\"width: 300px;\">\n                                        <el-option\n                                            v-for=\"publishForm in getAvailablePublishForms(platform)\"\n                                            :key=\"publishForm\"\n                                            :label=\"publishForm\"\n                                            :value=\"publishForm\">\n                                        </el-option>\n                                    </el-select>\n                                    <div class=\"selected-tags\">\n                                        <el-tag\n                                            v-if=\"form.platform_publish_forms[platform.platform_id]\"\n                                            closable\n                                            @close=\"removePublishForm(platform.platform_id)\"\n                                            style=\"margin-left: 8px;\">\n                                            {{ form.platform_publish_forms[platform.platform_id] }}\n                                        </el-tag>\n                                    </div>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.output_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n\n                <el-form-item label=\"模态\" prop=\"modality\">\n                    <el-select v-model=\"form.modality\" placeholder=\"请选择模态\">\n                        <el-option\n                            v-for=\"item in modalityOptions\"\n                            :key=\"item.dict_name\"\n                            :label=\"item.dict_name\"\n                            :value=\"item.dict_name\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 4\">\n                <el-form-item label=\"运行频率\" prop=\"scene_running_frequency\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-input-number\n                            v-model=\"frequencyValue\"\n                            :min=\"getMinValue()\"\n                            :max=\"getMaxValue()\"\n                            :step=\"getStep()\"\n                            placeholder=\"请输入数值\"\n                            style=\"width: 150px\"\n                            @change=\"handleFrequencyValueChange\">\n                        </el-input-number>\n                        <el-select \n                            v-model=\"frequencyUnit\" \n                            placeholder=\"请选择单位\"\n                            style=\"width: 120px\"\n                            @change=\"handleFrequencyUnitChange\">\n                            <el-option label=\"分钟\" value=\"minutes\"></el-option>\n                            <el-option label=\"小时\" value=\"hours\"></el-option>\n                            <el-option label=\"天\" value=\"days\"></el-option>\n                        </el-select>\n                        <span style=\"color: #909399; font-size: 12px;\">\n                            (最小间隔30分钟)\n                        </span>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"个性化进化更新频率\" prop=\"stored_strategy_refresh_days\">\n                    <el-input-number\n                        v-model=\"form.stored_strategy_refresh_days\"\n                        :min=\"0\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">建议您设为0</span>\n                </el-form-item>\n                <el-form-item label=\"AI自行探索频率\" width=\"400px\" prop=\"explore_strategy_trigger_days\">\n                    <el-input-number\n                        v-model=\"form.explore_strategy_trigger_days\"\n                        :min=\"1\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">目前我们的探索模式暂未上线，建议您先将Z值设为365天</span>\n                </el-form-item>\n                <el-form-item label=\"AI提示词\" prop=\"updated_prompt\">\n                    <el-input type=\"textarea\" v-model=\"form.updated_prompt\" placeholder=\"请输入AI提示词\" :rows=\"10\"></el-input>\n                </el-form-item>\n            </div>\n\n            <el-form-item class=\"navigation-buttons\">\n                <el-button v-if=\"activeStep > 0\" @click=\"prevStep\">上一步</el-button>\n                <el-button \n                    v-if=\"activeStep < 4\" \n                    type=\"primary\" \n                    @click=\"nextStep\"\n                    :disabled=\"isPlatformConfigLoading\"\n                    :loading=\"isPlatformConfigLoading\">\n                    {{ isPlatformConfigLoading ? '配置加载中...' : '下一步' }}\n                </el-button>\n                <el-button v-if=\"activeStep === 4\" type=\"primary\" @click=\"submitForm\">提交</el-button>\n            </el-form-item>\n        </el-form>\n\n        <el-dialog title=\"新增数据内容\" :visible.sync=\"addOptionDialogVisible\" width=\"500px\">\n            <el-form ref=\"optionForm\" :model=\"newOptionForm\" :rules=\"optionRules\" label-width=\"120px\">\n                <el-form-item label=\"内容名称\" prop=\"option_name\">\n                    <el-input v-model=\"newOptionForm.option_name\" placeholder=\"请输入内容名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"内容标识\" prop=\"option_key\">\n                    <el-input v-model=\"newOptionForm.option_key\" placeholder=\"请输入内容标识（英文）\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addOptionDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewOption\" :loading=\"addingOption\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增项目组\" :visible.sync=\"addProjectDialogVisible\" width=\"500px\">\n            <el-form ref=\"projectForm\" :model=\"newProjectForm\" :rules=\"projectRules\" label-width=\"120px\">\n                <el-form-item label=\"项目名称\" prop=\"project_name\">\n                    <el-input v-model=\"newProjectForm.project_name\" placeholder=\"请输入项目名称\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addProjectDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewProject\" :loading=\"addingProject\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增任务类型\" :visible.sync=\"addTaskTypeDialogVisible\" width=\"600px\">\n            <el-form ref=\"taskTypeForm\" :model=\"newTaskTypeForm\" :rules=\"taskTypeRules\" label-width=\"140px\">\n                <el-form-item label=\"任务类型名称\" prop=\"task_type_name\">\n                    <el-input v-model=\"newTaskTypeForm.task_type_name\" placeholder=\"请输入任务类型名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型描述\" prop=\"task_type_description\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.task_type_description\" placeholder=\"请输入任务类型描述\" :rows=\"3\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"关联平台\" prop=\"linked_platform_ids\">\n                    <el-select \n                        v-model=\"selectedPlatformIds\" \n                        multiple \n                        placeholder=\"请选择关联平台\"\n                        style=\"width: 100%\"\n                        @change=\"handlePlatformSelectChange\">\n                        <el-option\n                            v-for=\"platform in platforms\"\n                            :key=\"platform.platform_id\"\n                            :label=\"platform.platform_name\"\n                            :value=\"platform.platform_id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"推荐效果参数\" prop=\"recommended_effect_param_codes\">\n                    <el-select \n                        v-model=\"selectedEffectParams\" \n                        multiple \n                        placeholder=\"请选择推荐效果参数\"\n                        style=\"width: 100%\"\n                        @change=\"handleEffectParamsSelectChange\">\n                        <el-option\n                            v-for=\"param in availableEffectParamsForTaskType\"\n                            :key=\"param.effect_param_code\"\n                            :label=\"`${param.effect_param_name} (${param.effect_param_code})`\"\n                            :value=\"param.effect_param_name\">\n                        </el-option>\n                    </el-select>\n                    <div v-if=\"availableEffectParamsForTaskType.length === 0 && selectedPlatformIds.length > 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        所选平台暂无可用的效果参数\n                    </div>\n                    <div v-if=\"selectedPlatformIds.length === 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        请先选择关联平台\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"参数关系说明\" prop=\"effect_param_relationships_note\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.effect_param_relationships_note\" placeholder=\"请输入各推荐参数之间的逻辑关系说明\" :rows=\"3\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addTaskTypeDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewTaskType\" :loading=\"addingTaskType\">确定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\n\nexport default {\n    name: 'NewScene',\n    data() {\n        return {\n            activeStep: 0,\n            loading: false,\n            form: {\n                linked_project_team_name: null,\n                linked_task_type_code: null,\n                scene_name: '',\n                scene_description: '',\n                input_platforms: [],\n                output_platforms: [],\n                input_platforms_data: {},\n                output_platforms_data: {},\n                input_data_options: {},\n                output_data_options: {},\n                input_effect_params: {},\n                output_effect_params: {},\n                input_effect_params_config: {},\n                output_effect_params_config: {},\n                updated_prompt: '',\n                scene_running_frequency: '',\n                hour: '',\n                modality: '',\n                platform_modalities: {}, // 新增：平台多模态配置\n                platform_publish_forms: {}, // 新增：平台发布形态配置\n                day: '',\n                weeks: '',\n                stored_strategy_refresh_days: 0,\n                explore_strategy_trigger_days: 365,\n                scene_business_type: '',\n                baseline_data_start_days_ago: 30,\n                baseline_data_exclude_recent_days: 3,\n                min_baseline_sample_count: 3,\n                baseline_refresh_frequency_days: 7\n            },\n            frequencyValue: 30,\n            frequencyUnit: 'minutes',\n            rules: {\n                linked_project_team_name: [\n                    { \n                        required: true, \n                        message: '请选择项目组', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            if (!value) {\n                                callback(new Error('请选择项目组'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                linked_task_type_code: [\n                    { \n                        required: true, \n                        message: '请选择任务类型', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            if (!value) {\n                                callback(new Error('请选择任务类型'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                scene_name: [\n                    { required: true, message: '请输入场景名称', trigger: 'blur' }\n                ],\n                scene_description: [\n                    { required: false, message: '请输入场景描述', trigger: 'blur' }\n                ],\n                input_platforms: [\n                    { required: true, message: '请选择输入平台', trigger: 'change' }\n                ],\n                output_platforms: [\n                    { required: true, message: '请选择输出平台', trigger: 'change' }\n                ],\n                updated_prompt: [\n                    { required: true, message: '请输入AI提示词', trigger: 'blur' }\n                ],\n                scene_running_frequency: [\n                    { required: true, message: '请设置运行频率', trigger: 'change' },\n                    { \n                        type: 'number', \n                        min: 30, \n                        message: '运行频率最小间隔为30分钟', \n                        trigger: 'change' \n                    }\n                ],\n                stored_strategy_refresh_days: [\n                    { required: true, message: '请输入个性化进化策略更新频率', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-365之间的天数', trigger: 'blur' }\n                ],\n                explore_strategy_trigger_days: [\n                    { required: true, message: '请输入AI自行探索频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                scene_business_type: [\n                    { required: true, message: '请选择或输入场景类型', trigger: 'change' }\n                ],\n                baseline_data_start_days_ago: [\n                    { required: true, message: '请输入使用数据的起始天数', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                baseline_data_exclude_recent_days: [\n                    { required: true, message: '请输入排除最近的数据天数', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-30之间的天数', trigger: 'blur' }\n                ],\n                min_baseline_sample_count: [\n                    { required: true, message: '请输入样本量最小阈值', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 10000, message: '请输入1-10000之间的数值', trigger: 'blur' }\n                ],\n                baseline_refresh_frequency_days: [\n                    { required: true, message: '请输入基准线更新频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ]\n            },\n            projectTeams: [],\n            taskTypes: [],\n            selectedInputPlatforms: [],\n            selectedOutputPlatforms: [],\n            addOptionDialogVisible: false,\n            newOptionForm: {\n                option_name: '',\n                option_key: '',\n                platform_id: ''\n            },\n            optionRules: {\n                option_name: [\n                    { required: true, message: '请输入内容名称', trigger: 'blur' }\n                ],\n                option_key: [\n                    { required: true, message: '请输入内容标识（英文）', trigger: 'blur' }\n                ]\n            },\n            addingOption: false,\n            modalityOptions: [],\n            addProjectDialogVisible: false,\n            addTaskTypeDialogVisible: false,\n            newProjectForm: {\n                project_name: ''\n            },\n            newTaskTypeForm: {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            },\n            selectedPlatformIds: [],\n            selectedEffectParams: [],\n            availableEffectParamsForTaskType: [],\n            projectRules: {\n                project_name: [\n                    { required: true, message: '请输入项目名称', trigger: 'blur' }\n                ]\n            },\n            taskTypeRules: {\n                task_type_name: [\n                    { required: true, message: '请输入任务类型名称', trigger: 'blur' }\n                ],\n                task_type_description: [\n                    { required: true, message: '请输入任务类型描述', trigger: 'blur' }\n                ],\n                recommended_effect_param_codes: [\n                    { required: true, message: '请输入推荐效果参数编码', trigger: 'blur' }\n                ],\n                effect_param_relationships_note: [\n                    { required: true, message: '请输入各推荐参数之间的逻辑关系说明', trigger: 'blur' }\n                ]\n            },\n            addingProject: false,\n            addingTaskType: false,\n            sceneTypes: [],\n            loadingInputPlatforms: false,\n            loadingOutputPlatforms: false,\n            tempModalitySelection: {}, // 临时存储模态选择\n            // 模态类型常量\n            MODALITY_TYPES: [\n                '数字人视频',\n                '卡通视频',\n                '语音',\n                '游戏',\n                '图文结合',\n                '图文排版',\n                '播客视频'\n            ],\n            // 发布形态类型常量\n            PUBLISH_FORM_TYPES: [\n                'PPT',\n                'Word文档',\n                'PDF',\n                'Excel表格',\n                '文本',\n                '作业练习题',\n                '评测考试题目',\n                '模态原生态展示'\n            ]\n        }\n    },\n    computed: {\n        ...mapState(['platforms']),\n        availablePlatforms() {\n            if (!this.form.linked_task_type_code) {\n                return this.platforms\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.linked_platform_ids) {\n                return this.platforms\n            }\n            \n            const linkedPlatformIds = selectedTaskType.linked_platform_ids.split(',').map(id => parseInt(id.trim()))\n            return this.platforms.filter(platform => linkedPlatformIds.includes(platform.platform_id))\n        },\n        availableEffectParams() {\n            if (!this.form.linked_task_type_code) {\n                return []\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.recommended_effect_param_codes) {\n                return []\n            }\n            \n            try {\n                const params = JSON.parse(selectedTaskType.recommended_effect_param_codes)\n                return Array.isArray(params) ? params : []\n            } catch (error) {\n                console.error('解析效果参数失败:', error)\n                return []\n            }\n        },\n        isPlatformConfigLoading() {\n            if (this.activeStep === 2 && this.loadingInputPlatforms) {\n                return true\n            }\n            if (this.activeStep === 3 && this.loadingOutputPlatforms) {\n                return true\n            }\n            return false\n        }\n    },\n    methods: {\n        ...mapActions(['fetchPlatforms']),\n        // 获取平台可用的发布形态\n        getAvailablePublishForms(platform) {\n            // ECL平台可以选择所有类型\n            if (platform.platform_name && platform.platform_name.toUpperCase().includes('ECL')) {\n                return this.PUBLISH_FORM_TYPES\n            }\n            // 其他平台根据具体需求返回特定类型，这里暂时返回所有类型\n            // 后续可以根据平台类型进行更精细的控制\n            return this.PUBLISH_FORM_TYPES\n        },\n        // 处理模态选择\n        handleModalitySelect(platformId, selectedModality) {\n            if (!selectedModality) return\n\n            if (!this.form.platform_modalities[platformId]) {\n                this.$set(this.form.platform_modalities, platformId, [])\n            }\n\n            if (!this.form.platform_modalities[platformId].includes(selectedModality)) {\n                this.form.platform_modalities[platformId].push(selectedModality)\n            }\n\n            // 清空临时选择\n            this.$set(this.tempModalitySelection, platformId, '')\n        },\n        // 移除模态\n        removeModality(platformId, modality) {\n            if (this.form.platform_modalities[platformId]) {\n                const index = this.form.platform_modalities[platformId].indexOf(modality)\n                if (index > -1) {\n                    this.form.platform_modalities[platformId].splice(index, 1)\n                }\n            }\n        },\n        // 移除发布形态\n        removePublishForm(platformId) {\n            this.$set(this.form.platform_publish_forms, platformId, '')\n        },\n        async fetchPlatformOptions(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/option/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform options:', error)\n                return []\n            }\n        },\n        async fetchPlatformEffectParams(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/effectParamCategory/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform effect params:', error)\n                return []\n            }\n        },\n        getAvailableEffectParamsForPlatform(platformId) {\n            const recommendedParams = this.availableEffectParams || []\n            \n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n            \n            if (platformParams.length === 0) {\n                return []\n            }\n            \n            return platformParams.filter(param => recommendedParams.includes(param.effect_param_name))\n        },\n        getFieldComponent(type) {\n            const componentMap = {\n                'string': 'el-input',\n                'password': 'el-input',\n                'select': 'el-select',\n                'multiselect': 'el-select',\n                'number': 'el-input-number',\n                'bool': 'el-switch',\n                'textarea': 'el-input'\n            }\n            return componentMap[type] || 'el-input'\n        },\n        getFieldProps(field) {\n            const props = {\n                placeholder: `请输入${field.label}`\n            }\n            if (field.field_type === 'password') {\n                props.type = 'password'\n            }\n            if (field.field_type === 'textarea') {\n                props.type = 'textarea'\n                props.rows = 3\n            }\n            if (field.field_type === 'select' || field.field_type === 'multiselect') {\n                props.multiple = field.field_type === 'multiselect'\n                props.options = field.options || []\n            }\n            return props\n        },\n        async handleInputPlatformChange(platformIds) {\n            if (this.loadingInputPlatforms) {\n                return\n            }\n            this.loadingInputPlatforms = true\n            \n            try {\n                this.selectedInputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('input_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n                        \n                        const effectParams = await this.fetchPlatformEffectParams(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options,\n                            effectParams: effectParams\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedInputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n                    if (!this.form.input_platforms_data[platformId]) {\n                        this.$set(this.form.input_platforms_data, platformId, {\n                            additional_Information: ''\n                        })\n                    }\n\n                    if (!this.form.input_data_options[platformId]) {\n                        this.$set(this.form.input_data_options, platformId, [])\n                    }\n\n                    if (!this.form.input_effect_params[platformId]) {\n                        this.$set(this.form.input_effect_params, platformId, [])\n                    }\n\n                    const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n                    if (platformWithDetails && platformWithDetails.fields) {\n                        platformWithDetails.fields.forEach(field => {\n                            if (field.required) {\n                                const fieldProp = `input_platforms_data.${platformId}.${field.field_name}`\n                                this.$set(this.rules, fieldProp, [\n                                    { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                ])\n                            }\n                        })\n                    }\n                }\n            } catch (error) {\n                console.error('Error in handleInputPlatformChange:', error)\n                this.$message.error('处理输入平台变化时出错')\n            } finally {\n                this.loadingInputPlatforms = false\n            }\n        },\n        async handleOutputPlatformChange(platformIds) {\n            if (this.loadingOutputPlatforms) {\n                return\n            }\n            this.loadingOutputPlatforms = true\n            \n            try {\n                this.selectedOutputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('output_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedOutputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n                    if (!this.form.output_platforms_data[platformId]) {\n                        this.$set(this.form.output_platforms_data, platformId, {\n                            additional_Information: ''\n                        })\n                    }\n\n                    if (!this.form.output_data_options[platformId]) {\n                        this.$set(this.form.output_data_options, platformId, [])\n                    }\n\n                    // 初始化新的模态和发布形态字段\n                    if (!this.form.platform_modalities[platformId]) {\n                        this.$set(this.form.platform_modalities, platformId, [])\n                    }\n\n                    if (!this.form.platform_publish_forms[platformId]) {\n                        this.$set(this.form.platform_publish_forms, platformId, '')\n                    }\n\n                    const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)\n                    if (platformWithDetails && platformWithDetails.fields) {\n                        platformWithDetails.fields.forEach(field => {\n                            if (field.required) {\n                                const fieldProp = `output_platforms_data.${platformId}.${field.field_name}`\n                                this.$set(this.rules, fieldProp, [\n                                    { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                ])\n                            }\n                        })\n                    }\n                }\n            } catch (error) {\n                console.error('Error in handleOutputPlatformChange:', error)\n                this.$message.error('处理输出平台变化时出错')\n            } finally {\n                this.loadingOutputPlatforms = false\n            }\n        },\n        nextStep() {\n            if (this.isPlatformConfigLoading) {\n                this.$message.warning('平台配置正在加载中，请稍候...')\n                return\n            }\n\n            let fieldsToValidate = []\n\n            switch (this.activeStep) {\n                case 0:\n                    fieldsToValidate = ['linked_project_team_name', 'linked_task_type_code', 'scene_business_type', 'scene_name', 'scene_description']\n                    break\n                case 1:\n                    fieldsToValidate = ['baseline_data_start_days_ago', 'baseline_data_exclude_recent_days', 'min_baseline_sample_count', 'baseline_refresh_frequency_days']\n                    break\n                case 2:\n                    fieldsToValidate = ['input_platforms']\n                    \n                    this.selectedInputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `input_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n                        \n                        const selectedParams = this.form.input_effect_params[platform.platform_id] || []\n                        selectedParams.forEach(paramCode => {\n                            const configPrefix = `input_effect_params_config.${platform.platform_id}.${paramCode}`\n                            fieldsToValidate.push(`${configPrefix}.configured_evaluation_days`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_mean`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_stddev`)\n                        })\n                    })\n                    break\n                case 3:\n                    fieldsToValidate = ['output_platforms']\n\n                    this.selectedOutputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `output_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n\n                        // 验证模态选择\n                        if (!this.form.platform_modalities[platform.platform_id] ||\n                            this.form.platform_modalities[platform.platform_id].length === 0) {\n                            this.$message.error(`请为平台\"${platform.platform_name}\"选择至少一种模态类型`)\n                            return false\n                        }\n\n                        // 验证发布形态选择\n                        if (!this.form.platform_publish_forms[platform.platform_id]) {\n                            this.$message.error(`请为平台\"${platform.platform_name}\"选择发布形态`)\n                            return false\n                        }\n                    })\n                    break\n                case 4:\n                    fieldsToValidate = ['updated_prompt', 'scene_running_frequency', 'stored_strategy_refresh_days', 'explore_strategy_trigger_days']\n                    break\n                default:\n                    break\n            }\n\n            this.validateFields(fieldsToValidate, (valid) => {\n                if (valid) {\n                    this.activeStep++\n                }\n            })\n        },\n        validateFields(fields, callback) {\n            if (fields.length === 0) {\n                callback(true)\n                return\n            }\n\n            console.log('验证字段:', fields)\n            console.log('当前表单数据:', this.form)\n\n            const validationPromises = fields.map(field => {\n                return new Promise((resolve) => {\n                    this.$refs.form.validateField(field, (errorMessage) => {\n                        console.log(`字段 ${field} 验证结果:`, errorMessage)\n                        resolve({ field, errorMessage })\n                    })\n                })\n            })\n\n            Promise.all(validationPromises).then(results => {\n                const hasError = results.some(result => result.errorMessage)\n                console.log('验证结果:', results, '是否有错误:', hasError)\n                callback(!hasError)\n            })\n        },\n        prevStep() {\n            this.activeStep--\n        },\n        async submitForm() {\n            this.$refs.form.validate(async valid => {\n                if (valid) {\n                    try {\n                        const accounts = []\n\n                        const effectParams = []\n\n                        for (const platformId of this.form.input_platforms) {\n                            const platformData = this.form.input_platforms_data[platformId] || {}\n                            const dataOptions = this.form.input_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 1,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n\n                            if (this.form.input_effect_params_config[platformId]) {\n                                Object.values(this.form.input_effect_params_config[platformId]).forEach(paramConfig => {\n                                    effectParams.push({\n                                        platform_id: platformId,\n                                        ...paramConfig\n                                    })\n                                })\n                            }\n                        }\n\n                        for (const platformId of this.form.output_platforms) {\n                            const platformData = this.form.output_platforms_data[platformId] || {}\n                            const dataOptions = this.form.output_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 2,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n                        }\n\n                        const { input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, platform_modalities, platform_publish_forms, ...submitData } = this.form\n                        console.info(input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, platform_modalities, platform_publish_forms)\n\n                        submitData.state = 2;\n                        submitData.create_time = new Date().toLocaleString('sv-SE').replace('T', ' ');\n                        submitData.create_user_id = 0;\n                        submitData.company_id = 0;\n\n                        await this.$http.post('', {\n                            api: '/api/scene/add',\n                            data: submitData,\n                            accounts: accounts,\n                            effect_params: effectParams,\n                            platform_modalities: platform_modalities,\n                            platform_publish_forms: platform_publish_forms\n                        })\n                        this.$message.success('场景创建成功')\n                        this.$router.push('/')\n                    } catch (error) {\n                        this.$message.error('场景创建失败')\n                        console.error('Error creating scene:', error)\n                    }\n                }\n            })\n        },\n        showAddOptionDialog(platformId) {\n            this.newOptionForm = {\n                option_name: '',\n                option_key: '',\n                platform_id: platformId\n            }\n            this.addOptionDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.optionForm && this.$refs.optionForm.clearValidate()\n            })\n        },\n        async addNewOption() {\n            this.$refs.optionForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingOption = true\n                        await this.$http.post('', {\n                            api: '/api/option/add',\n                            data: {\n                                ...this.newOptionForm,\n                                platform_id: this.newOptionForm.platform_id\n                            }\n                        })\n                        this.$message.success('新增数据类型成功')\n                        this.addOptionDialogVisible = false\n                        this.newOptionForm = {\n                            option_name: '',\n                            option_key: '',\n                            platform_id: ''\n                        }\n                        await this.handleInputPlatformChange(this.form.input_platforms)\n                        await this.handleOutputPlatformChange(this.form.output_platforms)\n                    } catch (error) {\n                        this.$message.error('新增数据类型失败')\n                        console.error('Error adding new option:', error)\n                    } finally {\n                        this.addingOption = false\n                    }\n                }\n            })\n        },\n        async fetchModalityOptions() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'modality'\n                    }\n                })\n                this.modalityOptions = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching modality options:', error)\n                this.$message.error('获取模态列表失败')\n            }\n        },\n        async fetchProjectTeams() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/projectTeam/getList'\n                })\n                this.projectTeams = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching project teams:', error)\n                this.$message.error('获取项目组列表失败')\n            }\n        },\n        async fetchTaskTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/taskType/getList'\n                })\n                this.taskTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching task types:', error)\n                this.$message.error('获取任务类型列表失败')\n            }\n        },\n        async fetchSceneTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/sceneType/getList'\n                })\n                this.sceneTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching scene types:', error)\n                this.$message.error('获取场景类型列表失败')\n            }\n        },\n        handleProjectTeamChange() {\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_project_team_name')\n            })\n        },\n        handleTaskTypeChange() {\n            this.form.input_platforms = []\n            this.form.output_platforms = []\n            this.selectedInputPlatforms = []\n            this.selectedOutputPlatforms = []\n            this.form.input_platforms_data = {}\n            this.form.output_platforms_data = {}\n            this.form.input_data_options = {}\n            this.form.output_data_options = {}\n            this.form.input_effect_params = {}\n            this.form.input_effect_params_config = {}\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_task_type_code')\n            })\n        },\n        handleEffectParamsChange(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n\n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n\n            if (platformParams.length == 0) {\n                return;\n            }\n\n            Object.keys(this.rules).forEach(key => {\n                if (key.startsWith(`input_effect_params_config.${platformId}.`)) {\n                    this.$delete(this.rules, key)\n                }\n            })\n\n            selectedParams.forEach(param => {\n                if (!this.form.input_effect_params_config[platformId][param]) {\n\n                    const effectParam = platformParams.find(p => p.effect_param_code === param);\n\n                    this.$set(this.form.input_effect_params_config[platformId], param, {\n                        effect_param_code: effectParam.effect_param_code,\n                        effect_param_name: effectParam.effect_param_name,\n                        configured_evaluation_days: '',\n                        default_baseline_mean: '',\n                        default_baseline_stddev: ''\n                    })\n                }\n\n                const configPrefix = `input_effect_params_config.${platformId}.${param}`\n                this.$set(this.rules, `${configPrefix}.configured_evaluation_days`, [\n                    { required: true, message: '请输入效果实现天数', trigger: 'blur' },\n                    { pattern: /^[\\d,\\s]+$/, message: '请输入有效的天数格式，如：3,5,10', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_mean`, [\n                    { required: true, message: '请输入平均值', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '平均值必须是数字', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_stddev`, [\n                    { required: true, message: '请输入标准差', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '标准差必须是数字', trigger: 'blur' }\n                ])\n            })\n            \n            Object.keys(this.form.input_effect_params_config[platformId]).forEach(param => {\n                if (!selectedParams.includes(param)) {\n                    this.$delete(this.form.input_effect_params_config[platformId], param)\n                }\n            })\n        },\n        getEffectParamsTableData(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n            const config = this.form.input_effect_params_config[platformId] || {}\n            \n            return selectedParams.map(paramCode => ({\n                effect_param_code: paramCode,\n                effect_param_name: (config[paramCode] && config[paramCode].effect_param_name) || '',\n                configured_evaluation_days: (config[paramCode] && config[paramCode].configured_evaluation_days) || '',\n                default_baseline_mean: (config[paramCode] && config[paramCode].default_baseline_mean) || '',\n                default_baseline_stddev: (config[paramCode] && config[paramCode].default_baseline_stddev) || ''\n            }))\n        },\n        updateEffectParamConfig(platformId, paramName, field, value) {\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n            if (!this.form.input_effect_params_config[platformId][paramName]) {\n                this.$set(this.form.input_effect_params_config[platformId], paramName, {})\n            }\n            this.$set(this.form.input_effect_params_config[platformId][paramName], field, value)\n        },\n        getMinValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30\n                case 'hours':\n                    return 1\n                case 'days':\n                    return 1\n                default:\n                    return 1\n            }\n        },\n        getMaxValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 1440\n                case 'hours':\n                    return 24\n                case 'days':\n                    return 365\n                default:\n                    return 1\n            }\n        },\n        getStep() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30\n                case 'hours':\n                    return 1\n                case 'days':\n                    return 1\n                default:\n                    return 1\n            }\n        },\n        calculateTotalMinutes() {\n            let totalMinutes = 0\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    totalMinutes = this.frequencyValue\n                    break\n                case 'hours':\n                    totalMinutes = this.frequencyValue * 60\n                    break\n                case 'days':\n                    totalMinutes = this.frequencyValue * 24 * 60\n                    break\n            }\n            \n            if (totalMinutes < 30) {\n                totalMinutes = 30\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n            }\n            \n            this.form.scene_running_frequency = totalMinutes\n            return totalMinutes\n        },\n        parseFrequencyFromMinutes(minutes) {\n            if (!minutes || minutes < 30) {\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n                return\n            }\n            \n            if (minutes >= 1440 && minutes % 1440 === 0) {\n                this.frequencyValue = minutes / 1440\n                this.frequencyUnit = 'days'\n            } else if (minutes >= 60 && minutes % 60 === 0) {\n                this.frequencyValue = minutes / 60\n                this.frequencyUnit = 'hours'\n            } else {\n                this.frequencyValue = minutes\n                this.frequencyUnit = 'minutes'\n            }\n        },\n        handleFrequencyValueChange() {\n            this.calculateTotalMinutes()\n        },\n        handleFrequencyUnitChange() {\n            const currentMinutes = this.calculateTotalMinutes()\n            \n            this.parseFrequencyFromMinutes(currentMinutes)\n            this.calculateTotalMinutes()\n        },\n        showAddProjectDialog() {\n            this.newProjectForm = {\n                project_name: ''\n            }\n            this.addProjectDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.projectForm && this.$refs.projectForm.clearValidate()\n            })\n        },\n        async addNewProject() {\n            this.$refs.projectForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingProject = true\n                        await this.$http.post('', {\n                            api: '/api/projectTeam/add',\n                            data: this.newProjectForm\n                        })\n                        this.$message.success('新增项目组成功')\n                        this.addProjectDialogVisible = false\n                        await this.fetchProjectTeams()\n                        this.form.linked_project_team_name = this.newProjectForm.project_name\n                    } catch (error) {\n                        this.$message.error('新增项目组失败')\n                        console.error('Error adding new project:', error)\n                    } finally {\n                        this.addingProject = false\n                    }\n                }\n            })\n        },\n        showAddTaskTypeDialog() {\n            this.newTaskTypeForm = {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            }\n            this.selectedPlatformIds = []\n            this.selectedEffectParams = []\n            this.availableEffectParamsForTaskType = []\n            this.addTaskTypeDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.taskTypeForm && this.$refs.taskTypeForm.clearValidate()\n            })\n        },\n        async handlePlatformSelectChange(selectedIds) {\n            this.newTaskTypeForm.linked_platform_ids = selectedIds.join(',')\n            \n            await this.fetchEffectParamsForTaskType(selectedIds)\n        },\n        async fetchEffectParamsForTaskType(platformIds) {\n            if (platformIds.length === 0) {\n                this.availableEffectParamsForTaskType = []\n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                return\n            }\n            \n            try {\n                const platformParamsArrays = []\n                for (const platformId of platformIds) {\n                    const params = await this.fetchPlatformEffectParams(platformId)\n                    platformParamsArrays.push(params)\n                }\n                \n                const allParams = []\n                const seenCodes = new Set()\n                \n                platformParamsArrays.forEach(platformParams => {\n                    platformParams.forEach(param => {\n                        if (!seenCodes.has(param.effect_param_code)) {\n                            seenCodes.add(param.effect_param_code)\n                            allParams.push(param)\n                        }\n                    })\n                })\n                \n                this.availableEffectParamsForTaskType = allParams\n                \n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                \n            } catch (error) {\n                console.error('Error fetching effect params for task type:', error)\n                this.$message.error('获取效果参数失败')\n                this.availableEffectParamsForTaskType = []\n            }\n        },\n        handleEffectParamsSelectChange(selectedCodes) {\n            this.newTaskTypeForm.recommended_effect_param_codes = JSON.stringify(selectedCodes)\n        },\n        async addNewTaskType() {\n            this.$refs.taskTypeForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingTaskType = true\n                        await this.$http.post('', {\n                            api: '/api/taskType/add',\n                            data: this.newTaskTypeForm\n                        })\n                        this.$message.success('新增任务类型成功')\n                        this.addTaskTypeDialogVisible = false\n                        await this.fetchTaskTypes()\n                    } catch (error) {\n                        this.$message.error('新增任务类型失败')\n                        console.error('Error adding new task type:', error)\n                    } finally {\n                        this.addingTaskType = false\n                    }\n                }\n            })\n        }\n    },\n    async created() {\n        await this.fetchPlatforms({ page: 1, pageSize: 100 })\n        await this.fetchProjectTeams()\n        await this.fetchTaskTypes()\n        await this.fetchModalityOptions()\n        await this.fetchSceneTypes()\n        \n        this.calculateTotalMinutes()\n    }\n}\n</script>\n\n<style scoped>\n.new-scene {\n    padding: 20px;\n}\n\n.mt-20 {\n    margin-top: 20px;\n}\n\n.el-steps {\n    margin-bottom: 30px;\n}\n\n.platform-configs {\n    margin-top: 20px;\n    margin-bottom: 20px;\n}\n\n.platform-configs h3 {\n    margin-bottom: 16px;\n    color: #303133;\n    font-size: 16px;\n}\n\n.platform-card {\n    margin-bottom: 16px;\n}\n\n.platform-card:last-child {\n    margin-bottom: 0;\n}\n\n.data-options-container {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n}\n\n.checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.platform-selection-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.navigation-buttons {\n    margin-top: 30px;\n}\n\n.field-container {\n    display: flex;\n    flex-direction: column;\n    gap: 4px;\n}\n\n.field-description {\n    font-size: 12px;\n    color: #909399;\n    line-height: 1.4;\n}\n\n.platform-selection-tip {\n    margin-bottom: 16px;\n}\n\n.no-platforms-tip {\n    margin-top: 16px;\n}\n\n.loading-tip {\n    margin-top: 16px;\n}\n\n.effect-params-container {\n    margin-top: 16px;\n}\n\n.effect-param-checkbox {\n    margin-right: 16px;\n    margin-bottom: 8px;\n}\n\n.effect-params-table {\n    margin-top: 16px;\n}\n\n.effect-params-table h4 {\n    margin-bottom: 12px;\n    color: #303133;\n    font-size: 14px;\n}\n\n\n\n.no-effect-params-tip {\n    margin-bottom: 16px;\n}\n\n.table-header-with-tooltip {\n    cursor: help;\n    display: flex;\n    align-items: center;\n    gap: 4px;\n}\n\n.table-header-with-tooltip .el-icon-question {\n    color: #909399;\n    font-size: 14px;\n}\n\n/* 新增样式：多模态和发布形态选择 */\n.modality-selection-container,\n.publish-form-selection-container {\n    width: 100%;\n}\n\n.selection-row {\n    display: flex;\n    align-items: flex-start;\n    gap: 15px;\n    flex-wrap: wrap;\n}\n\n.selected-tags {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 8px;\n    min-height: 32px;\n    align-items: center;\n}\n</style>"]}]}