{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\scene\\NewScene.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\scene\\NewScene.vue", "mtime": 1754016754000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["NewScene.vue"], "names": [], "mappings": ";AAgiBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "NewScene.vue", "sourceRoot": "src/views/scene", "sourcesContent": ["<template>\n    <div class=\"new-scene\">\n        <el-steps :active=\"activeStep\" finish-status=\"success\" simple>\n            <el-step title=\"基本信息\" />\n            <el-step title=\"计算基准线配置\" />\n            <el-step title=\"数据输入平台\" />\n            <el-step title=\"数据输出平台\" />\n            <el-step title=\"其他设置\" />\n        </el-steps>\n\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"150px\" class=\"mt-20\" v-loading=\"loading\">\n            <div v-show=\"activeStep === 0\">\n                <el-form-item label=\"场景名称\" prop=\"scene_name\">\n                    <el-input v-model=\"form.scene_name\" placeholder=\"请输入场景名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型\" prop=\"linked_task_type_code\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_task_type_code\" placeholder=\"请选择任务类型\" style=\"flex: 1;\" @change=\"handleTaskTypeChange\">\n                            <el-option\n                                v-for=\"taskType in taskTypes\"\n                                :key=\"taskType.task_type_code\"\n                                :label=\"taskType.task_type_name\"\n                                :value=\"taskType.task_type_code\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddTaskTypeDialog\">\n                            新增任务类型\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景类型\" prop=\"scene_business_type\">\n                    <el-select \n                        v-model=\"form.scene_business_type\" \n                        placeholder=\"请选择或输入场景类型\" \n                        filterable \n                        allow-create \n                        default-first-option\n                        style=\"width: 100%\">\n                        <el-option\n                            v-for=\"sceneType in sceneTypes\"\n                            :key=\"sceneType\"\n                            :label=\"sceneType\"\n                            :value=\"sceneType\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"项目组\" prop=\"linked_project_team_name\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_project_team_name\" placeholder=\"请选择项目组\" style=\"flex: 1;\" @change=\"handleProjectTeamChange\">\n                            <el-option\n                                v-for=\"project in projectTeams\"\n                                :key=\"project.project_id\"\n                                :label=\"project.project_name\"\n                                :value=\"project.project_name\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddProjectDialog\">\n                            新增项目组\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景描述\" prop=\"scene_description\">\n                    <el-input type=\"textarea\" v-model=\"form.scene_description\" placeholder=\"请输入场景描述\"\n                        :rows=\"4\"></el-input>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 1\">\n                <el-form-item label=\"使用数据的起始天数\" prop=\"baseline_data_start_days_ago\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_start_days_ago\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线使用数据的起始天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"排除最近的数据天数\" prop=\"baseline_data_exclude_recent_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_exclude_recent_days\"\n                            :min=\"0\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线排除最近的数据天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"样本量最小阈值\" prop=\"min_baseline_sample_count\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.min_baseline_sample_count\"\n                            :min=\"1\"\n                            :max=\"10000\"\n                            :step=\"1\"\n                            placeholder=\"请输入样本量\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">个</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"一个场景在一次计算基准线时，所需要的最小样本量\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"基准线更新频率\" prop=\"baseline_refresh_frequency_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_refresh_frequency_days\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入频率\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"评估效果的基准线更新的频率\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 2\">\n                <el-form-item label=\"选择输入平台\" prop=\"input_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.input_platforms\" @change=\"handleInputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingInputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedInputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingInputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输入平台配置</h3>\n                    <el-card v-for=\"platform in selectedInputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <div class=\"field-container\">\n                                <component :is=\"getFieldComponent(field.field_type)\"\n                                    v-model=\"form.input_platforms_data[platform.platform_id][field.field_name]\"\n                                    v-bind=\"getFieldProps(field)\"></component>\n                                <div v-if=\"field.description\" class=\"field-description\">{{ field.description }}</div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据类型\" :prop=\"'input_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.input_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\"\n                                    @click=\"showAddOptionDialog(platform.platform_id)\">\n                                    新增数据类型\n                                </el-button>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"效果参数配置\">\n                            <div class=\"effect-params-container\">\n                                <div v-if=\"getAvailableEffectParamsForPlatform(platform.platform_id).length === 0\" class=\"no-effect-params-tip\">\n                                    <el-alert\n                                        title=\"该平台暂无可用的效果参数\"\n                                        type=\"info\"\n                                        :closable=\"false\"\n                                        show-icon>\n                                    </el-alert>\n                                </div>\n                                <el-checkbox-group v-else v-model=\"form.input_effect_params[platform.platform_id]\" @change=\"handleEffectParamsChange(platform.platform_id)\">\n                                    <el-checkbox v-for=\"param in getAvailableEffectParamsForPlatform(platform.platform_id)\" :key=\"param.effect_param_code\" :label=\"param.effect_param_code\" class=\"effect-param-checkbox\">\n                                        {{ param.effect_param_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                \n                                <div v-if=\"form.input_effect_params[platform.platform_id] && form.input_effect_params[platform.platform_id].length > 0\" class=\"effect-params-table\">\n                                    <h4>参数配置详情</h4>\n                                    <el-table :data=\"getEffectParamsTableData(platform.platform_id)\" border>\n                                        <el-table-column prop=\"effect_param_name\" label=\"参数名称\" width=\"120\"></el-table-column>\n                                        <el-table-column prop=\"configured_evaluation_days\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"系统会获取发布时间在T-基线范围内，且已满足各参数的Tij值的样本总数量。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *效果实现天数\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.configured_evaluation_days`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.configured_evaluation_days\" \n                                                        placeholder=\"如：3,5,10\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'configured_evaluation_days', scope.row.configured_evaluation_days)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_mean\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"μi是选取的样本量的效果数据里，该参数的平均值。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算μi，而是会使用您输入的缺省状态下的μi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *平均值\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_mean`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.default_baseline_mean\" \n                                                        placeholder=\"如：0\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_mean', scope.row.default_baseline_mean)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_stddev\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"σi是选取的样本量的效果数据里，该参数的标准差。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算σi，而是会使用您输入的缺省状态下的σi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *标准差\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_stddev`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input\n                                                        v-model=\"scope.row.default_baseline_stddev\" \n                                                        placeholder=\"如：1\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_stddev', scope.row.default_baseline_stddev)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                    </el-table>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.input_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n            </div>\n\n            <div v-show=\"activeStep === 3\">\n                <el-form-item label=\"选择输出平台\" prop=\"output_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.output_platforms\" @change=\"handleOutputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingOutputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedOutputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingOutputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输出平台配置</h3>\n                    <el-card v-for=\"platform in selectedOutputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <div class=\"field-container\">\n                                <component :is=\"getFieldComponent(field.field_type)\"\n                                    v-model=\"form.output_platforms_data[platform.platform_id][field.field_name]\"\n                                    v-bind=\"getFieldProps(field)\"></component>\n                                <div v-if=\"field.description\" class=\"field-description\">{{ field.description }}</div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据内容\" :prop=\"'output_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.output_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.output_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n\n                <el-form-item label=\"模态\" prop=\"modality\">\n                    <el-select v-model=\"form.modality\" placeholder=\"请选择模态\">\n                        <el-option\n                            v-for=\"item in modalityOptions\"\n                            :key=\"item.dict_name\"\n                            :label=\"item.dict_name\"\n                            :value=\"item.dict_name\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 4\">\n                <el-form-item label=\"运行频率\" prop=\"scene_running_frequency\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-input-number\n                            v-model=\"frequencyValue\"\n                            :min=\"getMinValue()\"\n                            :max=\"getMaxValue()\"\n                            :step=\"getStep()\"\n                            placeholder=\"请输入数值\"\n                            style=\"width: 150px\"\n                            @change=\"handleFrequencyValueChange\">\n                        </el-input-number>\n                        <el-select \n                            v-model=\"frequencyUnit\" \n                            placeholder=\"请选择单位\"\n                            style=\"width: 120px\"\n                            @change=\"handleFrequencyUnitChange\">\n                            <el-option label=\"分钟\" value=\"minutes\"></el-option>\n                            <el-option label=\"小时\" value=\"hours\"></el-option>\n                            <el-option label=\"天\" value=\"days\"></el-option>\n                        </el-select>\n                        <span style=\"color: #909399; font-size: 12px;\">\n                            (最小间隔30分钟)\n                        </span>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"个性化进化更新频率\" prop=\"stored_strategy_refresh_days\">\n                    <el-input-number\n                        v-model=\"form.stored_strategy_refresh_days\"\n                        :min=\"0\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">建议您设为0</span>\n                </el-form-item>\n                <el-form-item label=\"AI自行探索频率\" width=\"400px\" prop=\"explore_strategy_trigger_days\">\n                    <el-input-number\n                        v-model=\"form.explore_strategy_trigger_days\"\n                        :min=\"1\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">目前我们的探索模式暂未上线，建议您先将Z值设为365天</span>\n                </el-form-item>\n                <el-form-item label=\"AI提示词\" prop=\"updated_prompt\">\n                    <el-input type=\"textarea\" v-model=\"form.updated_prompt\" placeholder=\"请输入AI提示词\" :rows=\"10\"></el-input>\n                </el-form-item>\n            </div>\n\n            <el-form-item class=\"navigation-buttons\">\n                <el-button v-if=\"activeStep > 0\" @click=\"prevStep\">上一步</el-button>\n                <el-button \n                    v-if=\"activeStep < 4\" \n                    type=\"primary\" \n                    @click=\"nextStep\"\n                    :disabled=\"isPlatformConfigLoading\"\n                    :loading=\"isPlatformConfigLoading\">\n                    {{ isPlatformConfigLoading ? '配置加载中...' : '下一步' }}\n                </el-button>\n                <el-button v-if=\"activeStep === 4\" type=\"primary\" @click=\"submitForm\">提交</el-button>\n            </el-form-item>\n        </el-form>\n\n        <el-dialog title=\"新增数据内容\" :visible.sync=\"addOptionDialogVisible\" width=\"500px\">\n            <el-form ref=\"optionForm\" :model=\"newOptionForm\" :rules=\"optionRules\" label-width=\"120px\">\n                <el-form-item label=\"内容名称\" prop=\"option_name\">\n                    <el-input v-model=\"newOptionForm.option_name\" placeholder=\"请输入内容名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"内容标识\" prop=\"option_key\">\n                    <el-input v-model=\"newOptionForm.option_key\" placeholder=\"请输入内容标识（英文）\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addOptionDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewOption\" :loading=\"addingOption\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增项目组\" :visible.sync=\"addProjectDialogVisible\" width=\"500px\">\n            <el-form ref=\"projectForm\" :model=\"newProjectForm\" :rules=\"projectRules\" label-width=\"120px\">\n                <el-form-item label=\"项目名称\" prop=\"project_name\">\n                    <el-input v-model=\"newProjectForm.project_name\" placeholder=\"请输入项目名称\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addProjectDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewProject\" :loading=\"addingProject\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增任务类型\" :visible.sync=\"addTaskTypeDialogVisible\" width=\"600px\">\n            <el-form ref=\"taskTypeForm\" :model=\"newTaskTypeForm\" :rules=\"taskTypeRules\" label-width=\"140px\">\n                <el-form-item label=\"任务类型名称\" prop=\"task_type_name\">\n                    <el-input v-model=\"newTaskTypeForm.task_type_name\" placeholder=\"请输入任务类型名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型描述\" prop=\"task_type_description\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.task_type_description\" placeholder=\"请输入任务类型描述\" :rows=\"3\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"关联平台\" prop=\"linked_platform_ids\">\n                    <el-select \n                        v-model=\"selectedPlatformIds\" \n                        multiple \n                        placeholder=\"请选择关联平台\"\n                        style=\"width: 100%\"\n                        @change=\"handlePlatformSelectChange\">\n                        <el-option\n                            v-for=\"platform in platforms\"\n                            :key=\"platform.platform_id\"\n                            :label=\"platform.platform_name\"\n                            :value=\"platform.platform_id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"推荐效果参数\" prop=\"recommended_effect_param_codes\">\n                    <el-select \n                        v-model=\"selectedEffectParams\" \n                        multiple \n                        placeholder=\"请选择推荐效果参数\"\n                        style=\"width: 100%\"\n                        @change=\"handleEffectParamsSelectChange\">\n                        <el-option\n                            v-for=\"param in availableEffectParamsForTaskType\"\n                            :key=\"param.effect_param_code\"\n                            :label=\"`${param.effect_param_name} (${param.effect_param_code})`\"\n                            :value=\"param.effect_param_name\">\n                        </el-option>\n                    </el-select>\n                    <div v-if=\"availableEffectParamsForTaskType.length === 0 && selectedPlatformIds.length > 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        所选平台暂无可用的效果参数\n                    </div>\n                    <div v-if=\"selectedPlatformIds.length === 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        请先选择关联平台\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"参数关系说明\" prop=\"effect_param_relationships_note\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.effect_param_relationships_note\" placeholder=\"请输入各推荐参数之间的逻辑关系说明\" :rows=\"3\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addTaskTypeDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewTaskType\" :loading=\"addingTaskType\">确定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\n\nexport default {\n    name: 'NewScene',\n    data() {\n        return {\n            activeStep: 0,\n            loading: false,\n            form: {\n                linked_project_team_name: null,\n                linked_task_type_code: null,\n                scene_name: '',\n                scene_description: '',\n                input_platforms: [],\n                output_platforms: [],\n                input_platforms_data: {},\n                output_platforms_data: {},\n                input_data_options: {},\n                output_data_options: {},\n                input_effect_params: {},\n                output_effect_params: {},\n                input_effect_params_config: {},\n                output_effect_params_config: {},\n                updated_prompt: '',\n                scene_running_frequency: '',\n                hour: '',\n                modality: '',\n                day: '',\n                weeks: '',\n                stored_strategy_refresh_days: 0,\n                explore_strategy_trigger_days: 365,\n                scene_business_type: '',\n                baseline_data_start_days_ago: 30,\n                baseline_data_exclude_recent_days: 3,\n                min_baseline_sample_count: 3,\n                baseline_refresh_frequency_days: 7\n            },\n            frequencyValue: 30,\n            frequencyUnit: 'minutes',\n            rules: {\n                linked_project_team_name: [\n                    { \n                        required: true, \n                        message: '请选择项目组', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            if (!value) {\n                                callback(new Error('请选择项目组'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                linked_task_type_code: [\n                    { \n                        required: true, \n                        message: '请选择任务类型', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            if (!value) {\n                                callback(new Error('请选择任务类型'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                scene_name: [\n                    { required: true, message: '请输入场景名称', trigger: 'blur' }\n                ],\n                scene_description: [\n                    { required: false, message: '请输入场景描述', trigger: 'blur' }\n                ],\n                input_platforms: [\n                    { required: true, message: '请选择输入平台', trigger: 'change' }\n                ],\n                output_platforms: [\n                    { required: true, message: '请选择输出平台', trigger: 'change' }\n                ],\n                updated_prompt: [\n                    { required: true, message: '请输入AI提示词', trigger: 'blur' }\n                ],\n                scene_running_frequency: [\n                    { required: true, message: '请设置运行频率', trigger: 'change' },\n                    { \n                        type: 'number', \n                        min: 30, \n                        message: '运行频率最小间隔为30分钟', \n                        trigger: 'change' \n                    }\n                ],\n                stored_strategy_refresh_days: [\n                    { required: true, message: '请输入个性化进化策略更新频率', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-365之间的天数', trigger: 'blur' }\n                ],\n                explore_strategy_trigger_days: [\n                    { required: true, message: '请输入AI自行探索频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                scene_business_type: [\n                    { required: true, message: '请选择或输入场景类型', trigger: 'change' }\n                ],\n                baseline_data_start_days_ago: [\n                    { required: true, message: '请输入使用数据的起始天数', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                baseline_data_exclude_recent_days: [\n                    { required: true, message: '请输入排除最近的数据天数', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-30之间的天数', trigger: 'blur' }\n                ],\n                min_baseline_sample_count: [\n                    { required: true, message: '请输入样本量最小阈值', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 10000, message: '请输入1-10000之间的数值', trigger: 'blur' }\n                ],\n                baseline_refresh_frequency_days: [\n                    { required: true, message: '请输入基准线更新频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ]\n            },\n            projectTeams: [],\n            taskTypes: [],\n            selectedInputPlatforms: [],\n            selectedOutputPlatforms: [],\n            addOptionDialogVisible: false,\n            newOptionForm: {\n                option_name: '',\n                option_key: '',\n                platform_id: ''\n            },\n            optionRules: {\n                option_name: [\n                    { required: true, message: '请输入内容名称', trigger: 'blur' }\n                ],\n                option_key: [\n                    { required: true, message: '请输入内容标识（英文）', trigger: 'blur' }\n                ]\n            },\n            addingOption: false,\n            modalityOptions: [],\n            addProjectDialogVisible: false,\n            addTaskTypeDialogVisible: false,\n            newProjectForm: {\n                project_name: ''\n            },\n            newTaskTypeForm: {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            },\n            selectedPlatformIds: [],\n            selectedEffectParams: [],\n            availableEffectParamsForTaskType: [],\n            projectRules: {\n                project_name: [\n                    { required: true, message: '请输入项目名称', trigger: 'blur' }\n                ]\n            },\n            taskTypeRules: {\n                task_type_name: [\n                    { required: true, message: '请输入任务类型名称', trigger: 'blur' }\n                ],\n                task_type_description: [\n                    { required: true, message: '请输入任务类型描述', trigger: 'blur' }\n                ],\n                recommended_effect_param_codes: [\n                    { required: true, message: '请输入推荐效果参数编码', trigger: 'blur' }\n                ],\n                effect_param_relationships_note: [\n                    { required: true, message: '请输入各推荐参数之间的逻辑关系说明', trigger: 'blur' }\n                ]\n            },\n            addingProject: false,\n            addingTaskType: false,\n            sceneTypes: [],\n            loadingInputPlatforms: false,\n            loadingOutputPlatforms: false\n        }\n    },\n    computed: {\n        ...mapState(['platforms']),\n        availablePlatforms() {\n            if (!this.form.linked_task_type_code) {\n                return this.platforms\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.linked_platform_ids) {\n                return this.platforms\n            }\n            \n            const linkedPlatformIds = selectedTaskType.linked_platform_ids.split(',').map(id => parseInt(id.trim()))\n            return this.platforms.filter(platform => linkedPlatformIds.includes(platform.platform_id))\n        },\n        availableEffectParams() {\n            if (!this.form.linked_task_type_code) {\n                return []\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.recommended_effect_param_codes) {\n                return []\n            }\n            \n            try {\n                const params = JSON.parse(selectedTaskType.recommended_effect_param_codes)\n                return Array.isArray(params) ? params : []\n            } catch (error) {\n                console.error('解析效果参数失败:', error)\n                return []\n            }\n        },\n        isPlatformConfigLoading() {\n            if (this.activeStep === 2 && this.loadingInputPlatforms) {\n                return true\n            }\n            if (this.activeStep === 3 && this.loadingOutputPlatforms) {\n                return true\n            }\n            return false\n        }\n    },\n    methods: {\n        ...mapActions(['fetchPlatforms']),\n        async fetchPlatformOptions(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/option/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform options:', error)\n                return []\n            }\n        },\n        async fetchPlatformEffectParams(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/effectParamCategory/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform effect params:', error)\n                return []\n            }\n        },\n        getAvailableEffectParamsForPlatform(platformId) {\n            const recommendedParams = this.availableEffectParams || []\n            \n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n            \n            if (platformParams.length === 0) {\n                return []\n            }\n            \n            return platformParams.filter(param => recommendedParams.includes(param.effect_param_name))\n        },\n        getFieldComponent(type) {\n            const componentMap = {\n                'string': 'el-input',\n                'password': 'el-input',\n                'select': 'el-select',\n                'multiselect': 'el-select',\n                'number': 'el-input-number',\n                'bool': 'el-switch',\n                'textarea': 'el-input'\n            }\n            return componentMap[type] || 'el-input'\n        },\n        getFieldProps(field) {\n            const props = {\n                placeholder: `请输入${field.label}`\n            }\n            if (field.field_type === 'password') {\n                props.type = 'password'\n            }\n            if (field.field_type === 'textarea') {\n                props.type = 'textarea'\n                props.rows = 3\n            }\n            if (field.field_type === 'select' || field.field_type === 'multiselect') {\n                props.multiple = field.field_type === 'multiselect'\n                props.options = field.options || []\n            }\n            return props\n        },\n        async handleInputPlatformChange(platformIds) {\n            if (this.loadingInputPlatforms) {\n                return\n            }\n            this.loadingInputPlatforms = true\n            \n            try {\n                this.selectedInputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('input_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n                        \n                        const effectParams = await this.fetchPlatformEffectParams(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options,\n                            effectParams: effectParams\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedInputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n                    if (!this.form.input_platforms_data[platformId]) {\n                        this.$set(this.form.input_platforms_data, platformId, {\n                            additional_Information: ''\n                        })\n                    }\n\n                    if (!this.form.input_data_options[platformId]) {\n                        this.$set(this.form.input_data_options, platformId, [])\n                    }\n\n                    if (!this.form.input_effect_params[platformId]) {\n                        this.$set(this.form.input_effect_params, platformId, [])\n                    }\n\n                    const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n                    if (platformWithDetails && platformWithDetails.fields) {\n                        platformWithDetails.fields.forEach(field => {\n                            if (field.required) {\n                                const fieldProp = `input_platforms_data.${platformId}.${field.field_name}`\n                                this.$set(this.rules, fieldProp, [\n                                    { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                ])\n                            }\n                        })\n                    }\n                }\n            } catch (error) {\n                console.error('Error in handleInputPlatformChange:', error)\n                this.$message.error('处理输入平台变化时出错')\n            } finally {\n                this.loadingInputPlatforms = false\n            }\n        },\n        async handleOutputPlatformChange(platformIds) {\n            if (this.loadingOutputPlatforms) {\n                return\n            }\n            this.loadingOutputPlatforms = true\n            \n            try {\n                this.selectedOutputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('output_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedOutputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n                    if (!this.form.output_platforms_data[platformId]) {\n                        this.$set(this.form.output_platforms_data, platformId, {\n                            additional_Information: ''\n                        })\n                    }\n\n                    if (!this.form.output_data_options[platformId]) {\n                        this.$set(this.form.output_data_options, platformId, [])\n                    }\n\n                    const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)\n                    if (platformWithDetails && platformWithDetails.fields) {\n                        platformWithDetails.fields.forEach(field => {\n                            if (field.required) {\n                                const fieldProp = `output_platforms_data.${platformId}.${field.field_name}`\n                                this.$set(this.rules, fieldProp, [\n                                    { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                ])\n                            }\n                        })\n                    }\n                }\n            } catch (error) {\n                console.error('Error in handleOutputPlatformChange:', error)\n                this.$message.error('处理输出平台变化时出错')\n            } finally {\n                this.loadingOutputPlatforms = false\n            }\n        },\n        nextStep() {\n            if (this.isPlatformConfigLoading) {\n                this.$message.warning('平台配置正在加载中，请稍候...')\n                return\n            }\n\n            let fieldsToValidate = []\n\n            switch (this.activeStep) {\n                case 0:\n                    fieldsToValidate = ['linked_project_team_name', 'linked_task_type_code', 'scene_business_type', 'scene_name', 'scene_description']\n                    break\n                case 1:\n                    fieldsToValidate = ['baseline_data_start_days_ago', 'baseline_data_exclude_recent_days', 'min_baseline_sample_count', 'baseline_refresh_frequency_days']\n                    break\n                case 2:\n                    fieldsToValidate = ['input_platforms']\n                    \n                    this.selectedInputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `input_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n                        \n                        const selectedParams = this.form.input_effect_params[platform.platform_id] || []\n                        selectedParams.forEach(paramCode => {\n                            const configPrefix = `input_effect_params_config.${platform.platform_id}.${paramCode}`\n                            fieldsToValidate.push(`${configPrefix}.configured_evaluation_days`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_mean`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_stddev`)\n                        })\n                    })\n                    break\n                case 3:\n                    fieldsToValidate = ['output_platforms']\n                    \n                    this.selectedOutputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `output_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n                    })\n                    break\n                case 4:\n                    fieldsToValidate = ['updated_prompt', 'scene_running_frequency', 'stored_strategy_refresh_days', 'explore_strategy_trigger_days']\n                    break\n                default:\n                    break\n            }\n\n            this.validateFields(fieldsToValidate, (valid) => {\n                if (valid) {\n                    this.activeStep++\n                }\n            })\n        },\n        validateFields(fields, callback) {\n            if (fields.length === 0) {\n                callback(true)\n                return\n            }\n\n            console.log('验证字段:', fields)\n            console.log('当前表单数据:', this.form)\n\n            const validationPromises = fields.map(field => {\n                return new Promise((resolve) => {\n                    this.$refs.form.validateField(field, (errorMessage) => {\n                        console.log(`字段 ${field} 验证结果:`, errorMessage)\n                        resolve({ field, errorMessage })\n                    })\n                })\n            })\n\n            Promise.all(validationPromises).then(results => {\n                const hasError = results.some(result => result.errorMessage)\n                console.log('验证结果:', results, '是否有错误:', hasError)\n                callback(!hasError)\n            })\n        },\n        prevStep() {\n            this.activeStep--\n        },\n        async submitForm() {\n            this.$refs.form.validate(async valid => {\n                if (valid) {\n                    try {\n                        const accounts = []\n\n                        const effectParams = []\n\n                        for (const platformId of this.form.input_platforms) {\n                            const platformData = this.form.input_platforms_data[platformId] || {}\n                            const dataOptions = this.form.input_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 1,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n\n                            if (this.form.input_effect_params_config[platformId]) {\n                                Object.values(this.form.input_effect_params_config[platformId]).forEach(paramConfig => {\n                                    effectParams.push({\n                                        platform_id: platformId,\n                                        ...paramConfig\n                                    })\n                                })\n                            }\n                        }\n\n                        for (const platformId of this.form.output_platforms) {\n                            const platformData = this.form.output_platforms_data[platformId] || {}\n                            const dataOptions = this.form.output_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 2,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n                        }\n\n                        const { input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, ...submitData } = this.form\n                        console.info(input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config)\n\n                        submitData.state = 2;\n                        submitData.create_time = new Date().toLocaleString('sv-SE').replace('T', ' ');\n                        submitData.create_user_id = 0;\n                        submitData.company_id = 0;\n\n                        await this.$http.post('', {\n                            api: '/api/scene/add',\n                            data: submitData,\n                            accounts: accounts,\n                            effect_params: effectParams\n                        })\n                        this.$message.success('场景创建成功')\n                        this.$router.push('/')\n                    } catch (error) {\n                        this.$message.error('场景创建失败')\n                        console.error('Error creating scene:', error)\n                    }\n                }\n            })\n        },\n        showAddOptionDialog(platformId) {\n            this.newOptionForm = {\n                option_name: '',\n                option_key: '',\n                platform_id: platformId\n            }\n            this.addOptionDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.optionForm && this.$refs.optionForm.clearValidate()\n            })\n        },\n        async addNewOption() {\n            this.$refs.optionForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingOption = true\n                        await this.$http.post('', {\n                            api: '/api/option/add',\n                            data: {\n                                ...this.newOptionForm,\n                                platform_id: this.newOptionForm.platform_id\n                            }\n                        })\n                        this.$message.success('新增数据类型成功')\n                        this.addOptionDialogVisible = false\n                        this.newOptionForm = {\n                            option_name: '',\n                            option_key: '',\n                            platform_id: ''\n                        }\n                        await this.handleInputPlatformChange(this.form.input_platforms)\n                        await this.handleOutputPlatformChange(this.form.output_platforms)\n                    } catch (error) {\n                        this.$message.error('新增数据类型失败')\n                        console.error('Error adding new option:', error)\n                    } finally {\n                        this.addingOption = false\n                    }\n                }\n            })\n        },\n        async fetchModalityOptions() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'modality'\n                    }\n                })\n                this.modalityOptions = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching modality options:', error)\n                this.$message.error('获取模态列表失败')\n            }\n        },\n        async fetchProjectTeams() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/projectTeam/getList'\n                })\n                this.projectTeams = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching project teams:', error)\n                this.$message.error('获取项目组列表失败')\n            }\n        },\n        async fetchTaskTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/taskType/getList'\n                })\n                this.taskTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching task types:', error)\n                this.$message.error('获取任务类型列表失败')\n            }\n        },\n        async fetchSceneTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/sceneType/getList'\n                })\n                this.sceneTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching scene types:', error)\n                this.$message.error('获取场景类型列表失败')\n            }\n        },\n        handleProjectTeamChange() {\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_project_team_name')\n            })\n        },\n        handleTaskTypeChange() {\n            this.form.input_platforms = []\n            this.form.output_platforms = []\n            this.selectedInputPlatforms = []\n            this.selectedOutputPlatforms = []\n            this.form.input_platforms_data = {}\n            this.form.output_platforms_data = {}\n            this.form.input_data_options = {}\n            this.form.output_data_options = {}\n            this.form.input_effect_params = {}\n            this.form.input_effect_params_config = {}\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_task_type_code')\n            })\n        },\n        handleEffectParamsChange(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n\n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n\n            if (platformParams.length == 0) {\n                return;\n            }\n\n            Object.keys(this.rules).forEach(key => {\n                if (key.startsWith(`input_effect_params_config.${platformId}.`)) {\n                    this.$delete(this.rules, key)\n                }\n            })\n\n            selectedParams.forEach(param => {\n                if (!this.form.input_effect_params_config[platformId][param]) {\n\n                    const effectParam = platformParams.find(p => p.effect_param_code === param);\n\n                    this.$set(this.form.input_effect_params_config[platformId], param, {\n                        effect_param_code: effectParam.effect_param_code,\n                        effect_param_name: effectParam.effect_param_name,\n                        configured_evaluation_days: '',\n                        default_baseline_mean: '',\n                        default_baseline_stddev: ''\n                    })\n                }\n\n                const configPrefix = `input_effect_params_config.${platformId}.${param}`\n                this.$set(this.rules, `${configPrefix}.configured_evaluation_days`, [\n                    { required: true, message: '请输入效果实现天数', trigger: 'blur' },\n                    { pattern: /^[\\d,\\s]+$/, message: '请输入有效的天数格式，如：3,5,10', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_mean`, [\n                    { required: true, message: '请输入平均值', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '平均值必须是数字', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_stddev`, [\n                    { required: true, message: '请输入标准差', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '标准差必须是数字', trigger: 'blur' }\n                ])\n            })\n            \n            Object.keys(this.form.input_effect_params_config[platformId]).forEach(param => {\n                if (!selectedParams.includes(param)) {\n                    this.$delete(this.form.input_effect_params_config[platformId], param)\n                }\n            })\n        },\n        getEffectParamsTableData(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n            const config = this.form.input_effect_params_config[platformId] || {}\n            \n            return selectedParams.map(paramCode => ({\n                effect_param_code: paramCode,\n                effect_param_name: (config[paramCode] && config[paramCode].effect_param_name) || '',\n                configured_evaluation_days: (config[paramCode] && config[paramCode].configured_evaluation_days) || '',\n                default_baseline_mean: (config[paramCode] && config[paramCode].default_baseline_mean) || '',\n                default_baseline_stddev: (config[paramCode] && config[paramCode].default_baseline_stddev) || ''\n            }))\n        },\n        updateEffectParamConfig(platformId, paramName, field, value) {\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n            if (!this.form.input_effect_params_config[platformId][paramName]) {\n                this.$set(this.form.input_effect_params_config[platformId], paramName, {})\n            }\n            this.$set(this.form.input_effect_params_config[platformId][paramName], field, value)\n        },\n        getMinValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30\n                case 'hours':\n                    return 1\n                case 'days':\n                    return 1\n                default:\n                    return 1\n            }\n        },\n        getMaxValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 1440\n                case 'hours':\n                    return 24\n                case 'days':\n                    return 365\n                default:\n                    return 1\n            }\n        },\n        getStep() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30\n                case 'hours':\n                    return 1\n                case 'days':\n                    return 1\n                default:\n                    return 1\n            }\n        },\n        calculateTotalMinutes() {\n            let totalMinutes = 0\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    totalMinutes = this.frequencyValue\n                    break\n                case 'hours':\n                    totalMinutes = this.frequencyValue * 60\n                    break\n                case 'days':\n                    totalMinutes = this.frequencyValue * 24 * 60\n                    break\n            }\n            \n            if (totalMinutes < 30) {\n                totalMinutes = 30\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n            }\n            \n            this.form.scene_running_frequency = totalMinutes\n            return totalMinutes\n        },\n        parseFrequencyFromMinutes(minutes) {\n            if (!minutes || minutes < 30) {\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n                return\n            }\n            \n            if (minutes >= 1440 && minutes % 1440 === 0) {\n                this.frequencyValue = minutes / 1440\n                this.frequencyUnit = 'days'\n            } else if (minutes >= 60 && minutes % 60 === 0) {\n                this.frequencyValue = minutes / 60\n                this.frequencyUnit = 'hours'\n            } else {\n                this.frequencyValue = minutes\n                this.frequencyUnit = 'minutes'\n            }\n        },\n        handleFrequencyValueChange() {\n            this.calculateTotalMinutes()\n        },\n        handleFrequencyUnitChange() {\n            const currentMinutes = this.calculateTotalMinutes()\n            \n            this.parseFrequencyFromMinutes(currentMinutes)\n            this.calculateTotalMinutes()\n        },\n        showAddProjectDialog() {\n            this.newProjectForm = {\n                project_name: ''\n            }\n            this.addProjectDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.projectForm && this.$refs.projectForm.clearValidate()\n            })\n        },\n        async addNewProject() {\n            this.$refs.projectForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingProject = true\n                        await this.$http.post('', {\n                            api: '/api/projectTeam/add',\n                            data: this.newProjectForm\n                        })\n                        this.$message.success('新增项目组成功')\n                        this.addProjectDialogVisible = false\n                        await this.fetchProjectTeams()\n                        this.form.linked_project_team_name = this.newProjectForm.project_name\n                    } catch (error) {\n                        this.$message.error('新增项目组失败')\n                        console.error('Error adding new project:', error)\n                    } finally {\n                        this.addingProject = false\n                    }\n                }\n            })\n        },\n        showAddTaskTypeDialog() {\n            this.newTaskTypeForm = {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            }\n            this.selectedPlatformIds = []\n            this.selectedEffectParams = []\n            this.availableEffectParamsForTaskType = []\n            this.addTaskTypeDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.taskTypeForm && this.$refs.taskTypeForm.clearValidate()\n            })\n        },\n        async handlePlatformSelectChange(selectedIds) {\n            this.newTaskTypeForm.linked_platform_ids = selectedIds.join(',')\n            \n            await this.fetchEffectParamsForTaskType(selectedIds)\n        },\n        async fetchEffectParamsForTaskType(platformIds) {\n            if (platformIds.length === 0) {\n                this.availableEffectParamsForTaskType = []\n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                return\n            }\n            \n            try {\n                const platformParamsArrays = []\n                for (const platformId of platformIds) {\n                    const params = await this.fetchPlatformEffectParams(platformId)\n                    platformParamsArrays.push(params)\n                }\n                \n                const allParams = []\n                const seenCodes = new Set()\n                \n                platformParamsArrays.forEach(platformParams => {\n                    platformParams.forEach(param => {\n                        if (!seenCodes.has(param.effect_param_code)) {\n                            seenCodes.add(param.effect_param_code)\n                            allParams.push(param)\n                        }\n                    })\n                })\n                \n                this.availableEffectParamsForTaskType = allParams\n                \n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                \n            } catch (error) {\n                console.error('Error fetching effect params for task type:', error)\n                this.$message.error('获取效果参数失败')\n                this.availableEffectParamsForTaskType = []\n            }\n        },\n        handleEffectParamsSelectChange(selectedCodes) {\n            this.newTaskTypeForm.recommended_effect_param_codes = JSON.stringify(selectedCodes)\n        },\n        async addNewTaskType() {\n            this.$refs.taskTypeForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingTaskType = true\n                        await this.$http.post('', {\n                            api: '/api/taskType/add',\n                            data: this.newTaskTypeForm\n                        })\n                        this.$message.success('新增任务类型成功')\n                        this.addTaskTypeDialogVisible = false\n                        await this.fetchTaskTypes()\n                    } catch (error) {\n                        this.$message.error('新增任务类型失败')\n                        console.error('Error adding new task type:', error)\n                    } finally {\n                        this.addingTaskType = false\n                    }\n                }\n            })\n        }\n    },\n    async created() {\n        await this.fetchPlatforms({ page: 1, pageSize: 100 })\n        await this.fetchProjectTeams()\n        await this.fetchTaskTypes()\n        await this.fetchModalityOptions()\n        await this.fetchSceneTypes()\n        \n        this.calculateTotalMinutes()\n    }\n}\n</script>\n\n<style scoped>\n.new-scene {\n    padding: 20px;\n}\n\n.mt-20 {\n    margin-top: 20px;\n}\n\n.el-steps {\n    margin-bottom: 30px;\n}\n\n.platform-configs {\n    margin-top: 20px;\n    margin-bottom: 20px;\n}\n\n.platform-configs h3 {\n    margin-bottom: 16px;\n    color: #303133;\n    font-size: 16px;\n}\n\n.platform-card {\n    margin-bottom: 16px;\n}\n\n.platform-card:last-child {\n    margin-bottom: 0;\n}\n\n.data-options-container {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n}\n\n.checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.platform-selection-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.navigation-buttons {\n    margin-top: 30px;\n}\n\n.field-container {\n    display: flex;\n    flex-direction: column;\n    gap: 4px;\n}\n\n.field-description {\n    font-size: 12px;\n    color: #909399;\n    line-height: 1.4;\n}\n\n.platform-selection-tip {\n    margin-bottom: 16px;\n}\n\n.no-platforms-tip {\n    margin-top: 16px;\n}\n\n.loading-tip {\n    margin-top: 16px;\n}\n\n.effect-params-container {\n    margin-top: 16px;\n}\n\n.effect-param-checkbox {\n    margin-right: 16px;\n    margin-bottom: 8px;\n}\n\n.effect-params-table {\n    margin-top: 16px;\n}\n\n.effect-params-table h4 {\n    margin-bottom: 12px;\n    color: #303133;\n    font-size: 14px;\n}\n\n\n\n.no-effect-params-tip {\n    margin-bottom: 16px;\n}\n\n.table-header-with-tooltip {\n    cursor: help;\n    display: flex;\n    align-items: center;\n    gap: 4px;\n}\n\n.table-header-with-tooltip .el-icon-question {\n    color: #909399;\n    font-size: 14px;\n}\n</style>"]}]}