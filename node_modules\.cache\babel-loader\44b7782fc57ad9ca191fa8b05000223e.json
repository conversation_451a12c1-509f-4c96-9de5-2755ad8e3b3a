{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js!E:\\aaaaaaaaa\\kh\\node_modules\\eslint-loader\\index.js??ref--14-0!E:\\aaaaaaaaa\\kh\\src\\main.js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\main.js", "mtime": 1748164658000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\eslint-loader\\index.js", "mtime": 1754032203320}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgQXBwIGZyb20gJy4vQXBwLnZ1ZSc7CmltcG9ydCByb3V0ZXIgZnJvbSAnLi9yb3V0ZXInOwppbXBvcnQgc3RvcmUgZnJvbSAnLi9zdG9yZSc7CmltcG9ydCBFbGVtZW50VUkgZnJvbSAnZWxlbWVudC11aSc7CmltcG9ydCAnZWxlbWVudC11aS9saWIvdGhlbWUtY2hhbGsvaW5kZXguY3NzJzsKaW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJzsKVnVlLnVzZShFbGVtZW50VUkpOwpWdWUuY29uZmlnLnByb2R1Y3Rpb25UaXAgPSBmYWxzZTsKCi8vIENvbmZpZ3VyZSBheGlvcwpheGlvcy5kZWZhdWx0cy5iYXNlVVJMID0gJ2h0dHBzOi8vYXBpLnNvbGlua3VwLmNvbS90cmlnZ2VyL2FwaS8yMzNiZjlkNWNkODI0ZTJiOWI5NmY3ZDk4OWNjOTQyMi9lbmRwb2ludCc7ClZ1ZS5wcm90b3R5cGUuJGh0dHAgPSBheGlvczsKbmV3IFZ1ZSh7CiAgcm91dGVyLAogIHN0b3JlLAogIHJlbmRlcjogaCA9PiBoKEFwcCkKfSkuJG1vdW50KCcjYXBwJyk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "ElementUI", "axios", "use", "config", "productionTip", "defaults", "baseURL", "prototype", "$http", "render", "h", "$mount"], "sources": ["E:/aaaaaaaaa/kh/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\nimport ElementUI from 'element-ui'\nimport 'element-ui/lib/theme-chalk/index.css'\nimport axios from 'axios'\n\nVue.use(ElementUI)\nVue.config.productionTip = false\n\n// Configure axios\naxios.defaults.baseURL = 'https://api.solinkup.com/trigger/api/233bf9d5cd824e2b9b96f7d989cc9422/endpoint'\nVue.prototype.$http = axios\n\nnew Vue({\n    router,\n    store,\n    render: h => h(App)\n}).$mount('#app')"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAC7C,OAAOC,KAAK,MAAM,OAAO;AAEzBL,GAAG,CAACM,GAAG,CAACF,SAAS,CAAC;AAClBJ,GAAG,CAACO,MAAM,CAACC,aAAa,GAAG,KAAK;;AAEhC;AACAH,KAAK,CAACI,QAAQ,CAACC,OAAO,GAAG,gFAAgF;AACzGV,GAAG,CAACW,SAAS,CAACC,KAAK,GAAGP,KAAK;AAE3B,IAAIL,GAAG,CAAC;EACJE,MAAM;EACNC,KAAK;EACLU,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACb,GAAG;AACtB,CAAC,CAAC,CAACc,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}]}