{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\scene\\SceneHistory.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\scene\\SceneHistory.vue", "mtime": 1754016966000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SceneHistory.vue"], "names": [], "mappings": ";AA6FA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SceneHistory.vue", "sourceRoot": "src/views/scene", "sourcesContent": ["<template>\n    <div class=\"scene-history\">\n        <el-card class=\"history-card\">\n            <div slot=\"header\" class=\"header-with-breadcrumb\">\n                <el-breadcrumb separator=\"/\">\n                    <el-breadcrumb-item :to=\"{ path: '/scene/manage' }\">场景管理</el-breadcrumb-item>\n                    <el-breadcrumb-item>{{ sceneName }}</el-breadcrumb-item>\n                    <el-breadcrumb-item>运行记录</el-breadcrumb-item>\n                </el-breadcrumb>\n            </div>\n\n            <div class=\"history-header\">\n                <div class=\"scene-info\">\n                    <h3>{{ sceneName }}</h3>\n                </div>\n                <!-- <div class=\"history-actions\">\n                    <el-button type=\"primary\" size=\"small\" @click=\"openAIOptimize\">\n                        AI优化提示词\n                    </el-button>\n                </div> -->\n            </div>\n\n            <el-table :data=\"historyList\" v-loading=\"historyLoading\">\n                <el-table-column prop=\"note_title\" label=\"标题\" />\n                <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"200\"/>\n                <el-table-column label=\"操作\" width=\"160\">\n                    <template slot-scope=\"{ row }\">\n                        <el-button type=\"text\" @click=\"handleShowDetail(row)\">查看详情</el-button>\n                    </template>\n                </el-table-column>\n            </el-table>\n\n            <div class=\"pagination\">\n                <el-pagination @size-change=\"handleHistorySizeChange\" @current-change=\"handleHistoryCurrentChange\"\n                    :current-page=\"historyCurrentPage\" :page-sizes=\"[10, 15, 20, 30]\" :page-size=\"historyPageSize\"\n                    layout=\"total, sizes, prev, pager, next\" :total=\"historyTotal\" />\n            </div>\n        </el-card>\n\n        <el-dialog title=\"生成内容\" :visible.sync=\"detailDialogVisible\" width=\"80%\">\n            <div style=\"padding: 20px;\">\n                <div class=\"content-section\" v-if=\"detailData.note_title\">\n                    <h3 class=\"section-title\">标题</h3>\n                    <div class=\"section-content\" v-html=\"renderMarkdown(detailData.note_title)\"></div>\n                </div>\n\n                <div class=\"content-section\" v-if=\"detailData.note_text\">\n                    <h3 class=\"section-title\">内容</h3>\n                    <div class=\"section-content\" v-html=\"renderMarkdown(detailData.note_text)\"></div>\n                </div>\n\n                <div class=\"content-section\" v-if=\"detailData.key_words\">\n                    <h3 class=\"section-title\">关键词</h3>\n                    <div class=\"section-content\">\n                        <el-tag \n                            v-for=\"(keyword, index) in parseKeywords(detailData.key_words)\" \n                            :key=\"index\" \n                            type=\"info\" \n                            size=\"small\"\n                            style=\"margin-right: 8px; margin-bottom: 8px;\">\n                            {{ keyword }}\n                        </el-tag>\n                    </div>\n                </div>\n                <div class=\"content-section\" v-if=\"detailData.picture_url\">\n                    <h3 class=\"section-title\">图片</h3>\n                    <div class=\"section-content\">\n                        <div class=\"image-gallery\">\n                            <div \n                                v-for=\"(imageUrl, index) in parsePictureUrls(detailData.picture_url)\" \n                                :key=\"index\" \n                                class=\"image-item\">\n                                <el-image \n                                    :src=\"imageUrl\" \n                                    fit=\"cover\"\n                                    style=\"width: 200px; height: 150px; border-radius: 4px;\"\n                                    :preview-src-list=\"parsePictureUrls(detailData.picture_url)\"\n                                    :initial-index=\"index\">\n                                    <div slot=\"error\" class=\"image-slot\">\n                                        <i class=\"el-icon-picture-outline\"></i>\n                                        <div>加载失败</div>\n                                    </div>\n                                </el-image>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nimport marked from 'marked'\n\nexport default {\n    name: 'SceneHistory',\n    data() {\n        return {\n            sceneId: null,\n            sceneName: '',\n            historyList: [],\n            historyCurrentPage: 1,\n            historyPageSize: 15,\n            historyTotal: 0,\n            historyLoading: false,\n            detailDialogVisible: false,\n            detailData: {\n                ai: '',\n                outline: '',\n                prompt: '',\n                content: ''\n            }\n        }\n    },\n    methods: {\n        async fetchHistory() {\n            this.historyLoading = true\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/scene/getHistoryList',\n                    param: {\n                        scene_id: this.sceneId,\n                        page: this.historyCurrentPage,\n                        pageSize: this.historyPageSize\n                    }\n                })\n                this.historyList = response.data.data\n                this.historyTotal = response.data.total\n            } catch (error) {\n                this.$message.error('获取历史记录失败')\n                console.error('Error fetching history:', error)\n            } finally {\n                this.historyLoading = false\n            }\n        },\n        handleHistorySizeChange(val) {\n            this.historyPageSize = val\n            this.fetchHistory()\n        },\n        handleHistoryCurrentChange(val) {\n            this.historyCurrentPage = val\n            this.fetchHistory()\n        },\n        async openAIOptimize() {\n            const url = `https://acpfbbeg.manus.space/?scene_id=${this.sceneId}`\n            window.open(url, '_blank')\n        },\n        async handleShowDetail(row) {\n            console.info(row)\n            this.detailDialogVisible = true\n            try {\n                this.detailData = row\n            } catch (e) {\n                this.detailData = { ai: '获取失败', outline: '', prompt: '', content: '' }\n            }\n        },\n        renderMarkdown(md) {\n            if (!md) return ''\n            return marked(md)\n        },\n        parseKeywords(keywordsData) {\n            if (!keywordsData) return []\n            \n            try {\n                keywordsData = keywordsData.replace(/'/g, '\"');\n                // 如果已经是数组，直接返回\n                if (Array.isArray(keywordsData)) {\n                    return keywordsData\n                }\n                \n                // 如果是字符串，尝试解析JSON\n                if (typeof keywordsData === 'string') {\n                    const parsed = JSON.parse(keywordsData)\n                    return Array.isArray(parsed) ? parsed : [keywordsData]\n                }\n                \n                return [keywordsData]\n            } catch (error) {\n                console.error('解析关键词失败:', error)\n                // 如果解析失败，尝试按逗号分割\n                return typeof keywordsData === 'string' ? keywordsData.split(',').map(item => item.trim()) : []\n            }\n        },\n        \n        parsePictureUrls(pictureData) {\n            if (!pictureData) return []\n            \n            try {\n                pictureData = pictureData.replace(/'/g, '\"');\n                // 如果已经是数组，直接返回\n                if (Array.isArray(pictureData)) {\n                    return pictureData.filter(url => url && url.trim())\n                }\n                \n                // 如果是字符串，尝试解析JSON\n                if (typeof pictureData === 'string') {\n                    const parsed = JSON.parse(pictureData)\n                    if (Array.isArray(parsed)) {\n                        return parsed.filter(url => url && url.trim())\n                    } else {\n                        return pictureData.trim() ? [pictureData.trim()] : []\n                    }\n                }\n                \n                return []\n            } catch (error) {\n                console.error('解析图片URL失败:', error)\n                // 如果解析失败，尝试按逗号分割\n                return typeof pictureData === 'string' ? \n                    pictureData.split(',').map(item => item.trim()).filter(item => item) : []\n            }\n        }\n    },\n    created() {\n        this.sceneId = this.$route.params.id\n        this.sceneName = this.$route.query.name || '未知场景'\n        this.fetchHistory()\n    }\n}\n</script>\n\n<style scoped>\n.scene-history {\n    padding: 20px;\n}\n\n.history-card {\n    margin-bottom: 20px;\n}\n\n.header-with-breadcrumb {\n    padding: 0;\n}\n\n.pagination {\n    margin-top: 20px;\n    text-align: right;\n}\n\n.el-breadcrumb {\n    line-height: 1;\n}\n\n.history-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n}\n\n.scene-info h3 {\n    margin: 0;\n    font-size: 16px;\n    color: #303133;\n}\n\n.history-actions {\n    display: flex;\n    gap: 10px;\n}\n\n.content-section {\n    margin-bottom: 24px;\n    padding-bottom: 16px;\n    border-bottom: 1px solid #f0f0f0;\n}\n\n.content-section:last-child {\n    border-bottom: none;\n}\n\n.section-title {\n    margin: 0 0 12px 0;\n    color: #303133;\n    font-size: 16px;\n    font-weight: 600;\n}\n\n.section-content {\n    line-height: 1.8;\n    color: #606266;\n}\n\n.image-gallery {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 12px;\n}\n\n.image-item {\n    position: relative;\n}\n\n.image-slot {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    width: 100%;\n    height: 100%;\n    background-color: #f5f7fa;\n    color: #909399;\n    font-size: 14px;\n}\n\n.image-slot i {\n    font-size: 32px;\n    margin-bottom: 8px;\n}\n</style> "]}]}