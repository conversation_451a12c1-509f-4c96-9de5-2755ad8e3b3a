{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\Overview.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\Overview.vue", "mtime": 1754016998000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IG1hcFN0YXRlLCBtYXBBY3Rpb25zIH0gZnJvbSAndnVleCcKCmV4cG9ydCBkZWZhdWx0IHsKICAgIG5hbWU6ICdPdmVydmlldycsCiAgICBkYXRhKCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIGFsbFNjZW5lczogW10sCiAgICAgICAgICAgIGxpc3RMb2FkaW5nOiBmYWxzZSwKICAgICAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgICAgdG90YWw6IDAKICAgICAgICB9CiAgICB9LAogICAgY29tcHV0ZWQ6IHsKICAgICAgICAuLi5tYXBTdGF0ZShbJ3NjZW5lcycsICdsb2FkaW5nJ10pCiAgICB9LAogICAgbWV0aG9kczogewogICAgICAgIC4uLm1hcEFjdGlvbnMoWydmZXRjaFJlY2VudFNjZW5lcyddKSwKICAgICAgICBlZGl0U2NlbmUoc2NlbmVJZCkgewogICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaChgL3NjZW5lL2VkaXQvJHtzY2VuZUlkfWApCiAgICAgICAgfSwKICAgICAgICB2aWV3U2NlbmUoKSB7CiAgICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKGAvc2NlbmUvbWFuYWdlYCkKICAgICAgICB9LAogICAgICAgIGdldFN0YXRlVHlwZShzdGF0ZSkgewogICAgICAgICAgICBzd2l0Y2ggKHN0YXRlKSB7CiAgICAgICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuICdzdWNjZXNzJwogICAgICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgICAgICAgIHJldHVybiAnd2FybmluZycKICAgICAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICAgICAgICByZXR1cm4gJ2RhbmdlcicKICAgICAgICAgICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuICdpbmZvJwogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBnZXRTdGF0ZVRleHQoc3RhdGUpIHsKICAgICAgICAgICAgc3dpdGNoIChzdGF0ZSkgewogICAgICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgICAgICAgIHJldHVybiAn6L+Q6KGM5LitJwogICAgICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgICAgICAgIHJldHVybiAn5bey5pqC5YGcJwogICAgICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgICAgICAgIHJldHVybiAn5bey5Yig6ZmkJwogICAgICAgICAgICAgICAgZGVmYXVsdDoKICAgICAgICAgICAgICAgICAgICByZXR1cm4gJ+acquefpScKICAgICAgICAgICAgfQogICAgICAgIH0KICAgIH0sCiAgICBjcmVhdGVkKCkgewogICAgICAgIHRoaXMuZmV0Y2hSZWNlbnRTY2VuZXMoKQogICAgfQp9Cg=="}, {"version": 3, "sources": ["Overview.vue"], "names": [], "mappings": ";AAgFA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Overview.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n    <div class=\"overview\">\n        <!-- <el-row :gutter=\"20\">\n            <el-col :span=\"24\">\n                <h2>最近运行场景</h2>\n            </el-col>\n        </el-row> -->\n\n        <!-- <el-row :gutter=\"20\" v-loading=\"loading\">\n            <el-col :span=\"8\" v-for=\"scene in scenes\" :key=\"scene.scene_id\">\n                <el-card class=\"scene-card\" shadow=\"hover\">\n                    <div slot=\"header\" class=\"clearfix\">\n                        <span>{{ scene.scene_name }}</span>\n                        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"editScene(scene.scene_id)\">\n                            编辑\n                        </el-button>\n                    </div>\n                    <div class=\"scene-description\">{{ scene.scene_description }}</div>\n                    <div class=\"scene-info\">\n                        <el-tag size=\"small\" :type=\"getStateType(scene.state)\">\n                            {{ getStateText(scene.state) }}\n                        </el-tag>\n                        <span class=\"frequency\">{{ scene.Frequency_of_operation }}</span>\n                    </div>\n                </el-card>\n            </el-col>\n        </el-row> -->\n\n        <el-row :gutter=\"20\" class=\"mt-20\">\n            <el-col :span=\"24\">\n                <div class=\"section-header\">\n                    <h2>最近运行场景</h2>\n                    <el-button type=\"primary\" @click=\"$router.push('/scene/new')\">\n                        新建场景\n                    </el-button>\n                </div>\n            </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n            <el-col :span=\"24\">\n                <el-card>\n                    <el-table :data=\"scenes\" v-loading=\"loading\" style=\"width: 100%\">\n                        <el-table-column prop=\"scene_name\" label=\"场景名称\" />\n                        <el-table-column label=\"运行状态\">\n                            <template slot-scope=\"{ row }\">\n                                <el-tag :type=\"getStateType(row.state)\">\n                                    {{ getStateText(row.state) }}\n                                </el-tag>\n                            </template>\n                        </el-table-column>\n                        <el-table-column prop=\"final_running_time\" label=\"最近运行时间\"/>\n        \n                        <el-table-column label=\"操作\" width=\"100\">\n                            <template slot-scope=\"{ row }\">\n                                <el-button type=\"text\" size=\"small\" @click=\"editScene(row.scene_id)\">\n                                    编辑\n                                </el-button>\n                                <!-- <el-button type=\"text\" size=\"small\" @click=\"viewScene(row.scene_id)\">\n                                    查看\n                                </el-button> -->\n                            </template>\n                        </el-table-column>\n                    </el-table>\n\n                    <!-- <div class=\"pagination\">\n                        <el-pagination @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\"\n                            :current-page=\"currentPage\" :page-sizes=\"[10, 20, 50, 100]\" :page-size=\"pageSize\"\n                            layout=\"total, sizes, prev, pager, next\" :total=\"total\">\n                        </el-pagination>\n                    </div> -->\n                </el-card>\n            </el-col>\n        </el-row>\n\n\n    </div>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\n\nexport default {\n    name: 'Overview',\n    data() {\n        return {\n            allScenes: [],\n            listLoading: false,\n            currentPage: 1,\n            pageSize: 10,\n            total: 0\n        }\n    },\n    computed: {\n        ...mapState(['scenes', 'loading'])\n    },\n    methods: {\n        ...mapActions(['fetchRecentScenes']),\n        editScene(sceneId) {\n            this.$router.push(`/scene/edit/${sceneId}`)\n        },\n        viewScene() {\n            this.$router.push(`/scene/manage`)\n        },\n        getStateType(state) {\n            switch (state) {\n                case 1:\n                    return 'success'\n                case 2:\n                    return 'warning'\n                case 3:\n                    return 'danger'\n                default:\n                    return 'info'\n            }\n        },\n        getStateText(state) {\n            switch (state) {\n                case 1:\n                    return '运行中'\n                case 2:\n                    return '已暂停'\n                case 3:\n                    return '已删除'\n                default:\n                    return '未知'\n            }\n        }\n    },\n    created() {\n        this.fetchRecentScenes()\n    }\n}\n</script>\n\n<style scoped>\n.overview {\n    padding: 20px;\n}\n\n.scene-card {\n    margin-bottom: 20px;\n}\n\n.scene-description {\n    color: #666;\n    margin: 10px 0;\n    height: 40px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n}\n\n.scene-info {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-top: 10px;\n}\n\n.frequency {\n    color: #909399;\n    font-size: 12px;\n}\n\n.mt-20 {\n    margin-top: 20px;\n}\n\n.pagination {\n    margin-top: 20px;\n    text-align: right;\n}\n\n.section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n}\n\n.section-header h2 {\n    margin: 0;\n}\n</style>"]}]}