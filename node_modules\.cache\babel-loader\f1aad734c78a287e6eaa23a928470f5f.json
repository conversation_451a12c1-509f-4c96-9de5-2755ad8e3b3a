{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js!E:\\aaaaaaaaa\\kh\\node_modules\\eslint-loader\\index.js??ref--14-0!E:\\aaaaaaaaa\\kh\\src\\store\\index.js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\store\\index.js", "mtime": 1753800784000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\eslint-loader\\index.js", "mtime": 1754032203320}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "use", "Store", "state", "scenes", "platforms", "currentScene", "loading", "mutations", "SET_SCENES", "SET_PLATFORMS", "SET_CURRENT_SCENE", "scene", "SET_LOADING", "actions", "fetchScenes", "commit", "page", "pageSize", "response", "_vm", "$http", "post", "api", "param", "console", "log", "data", "error", "fetchRecentScenes", "fetchPlatforms"], "sources": ["E:/aaaaaaaaa/kh/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Vuex from 'vuex'\n\nVue.use(Vuex)\n\nexport default new Vuex.Store({\n    state: {\n        scenes: [],\n        platforms: [],\n        currentScene: null,\n        loading: false,\n    },\n    mutations: {\n        SET_SCENES(state, scenes) {\n            state.scenes = scenes\n        },\n        SET_PLATFORMS(state, platforms) {\n            state.platforms = platforms\n        },\n        SET_CURRENT_SCENE(state, scene) {\n            state.currentScene = scene\n        },\n        SET_LOADING(state, loading) {\n            state.loading = loading\n        }\n    },\n    actions: {\n        async fetchScenes({ commit }, { page = 1, pageSize = 15 }) {\n            commit('SET_LOADING', true)\n            try {\n                const response = await this._vm.$http.post('', {\n                    api: '/api/scene/getList',\n                    param: { page, pageSize }\n                })\n                console.log(response)\n                commit('SET_SCENES', response.data.data)\n            } catch (error) {\n                console.error('Error fetching scenes:', error)\n            } finally {\n                commit('SET_LOADING', false)\n            }\n        },\n        async fetchRecentScenes({ commit }) {\n            commit('SET_LOADING', true)\n            try {\n                const response = await this._vm.$http.post('', {\n                    api: '/api/scene/getRecent'\n                })\n                console.log(response)\n                commit('SET_SCENES', response.data.data)\n            } catch (error) {\n                console.error('Error fetching recent scenes:', error)\n            } finally {\n                commit('SET_LOADING', false)\n            }\n        },\n        async fetchPlatforms({ commit }, { page = 1, pageSize = 15 }) {\n            try {\n                const response = await this._vm.$http.post('', {\n                    api: '/api/platform/getList',\n                    param: { page, pageSize }\n                })\n                commit('SET_PLATFORMS', response.data.data)\n            } catch (error) {\n                console.error('Error fetching platforms:', error)\n            }\n        }\n    }\n}) "], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AAEvBD,GAAG,CAACE,GAAG,CAACD,IAAI,CAAC;AAEb,eAAe,IAAIA,IAAI,CAACE,KAAK,CAAC;EAC1BC,KAAK,EAAE;IACHC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAE;EACb,CAAC;EACDC,SAAS,EAAE;IACPC,UAAUA,CAACN,KAAK,EAAEC,MAAM,EAAE;MACtBD,KAAK,CAACC,MAAM,GAAGA,MAAM;IACzB,CAAC;IACDM,aAAaA,CAACP,KAAK,EAAEE,SAAS,EAAE;MAC5BF,KAAK,CAACE,SAAS,GAAGA,SAAS;IAC/B,CAAC;IACDM,iBAAiBA,CAACR,KAAK,EAAES,KAAK,EAAE;MAC5BT,KAAK,CAACG,YAAY,GAAGM,KAAK;IAC9B,CAAC;IACDC,WAAWA,CAACV,KAAK,EAAEI,OAAO,EAAE;MACxBJ,KAAK,CAACI,OAAO,GAAGA,OAAO;IAC3B;EACJ,CAAC;EACDO,OAAO,EAAE;IACL,MAAMC,WAAWA,CAAC;MAAEC;IAAO,CAAC,EAAE;MAAEC,IAAI,GAAG,CAAC;MAAEC,QAAQ,GAAG;IAAG,CAAC,EAAE;MACvDF,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC;MAC3B,IAAI;QACA,MAAMG,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACC,KAAK,CAACC,IAAI,CAAC,EAAE,EAAE;UAC3CC,GAAG,EAAE,oBAAoB;UACzBC,KAAK,EAAE;YAAEP,IAAI;YAAEC;UAAS;QAC5B,CAAC,CAAC;QACFO,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;QACrBH,MAAM,CAAC,YAAY,EAAEG,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAAC;MAC5C,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAClD,CAAC,SAAS;QACNZ,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC;MAChC;IACJ,CAAC;IACD,MAAMa,iBAAiBA,CAAC;MAAEb;IAAO,CAAC,EAAE;MAChCA,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC;MAC3B,IAAI;QACA,MAAMG,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACC,KAAK,CAACC,IAAI,CAAC,EAAE,EAAE;UAC3CC,GAAG,EAAE;QACT,CAAC,CAAC;QACFE,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;QACrBH,MAAM,CAAC,YAAY,EAAEG,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAAC;MAC5C,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZH,OAAO,CAACG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACzD,CAAC,SAAS;QACNZ,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC;MAChC;IACJ,CAAC;IACD,MAAMc,cAAcA,CAAC;MAAEd;IAAO,CAAC,EAAE;MAAEC,IAAI,GAAG,CAAC;MAAEC,QAAQ,GAAG;IAAG,CAAC,EAAE;MAC1D,IAAI;QACA,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACC,KAAK,CAACC,IAAI,CAAC,EAAE,EAAE;UAC3CC,GAAG,EAAE,uBAAuB;UAC5BC,KAAK,EAAE;YAAEP,IAAI;YAAEC;UAAS;QAC5B,CAAC,CAAC;QACFF,MAAM,CAAC,eAAe,EAAEG,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAAC;MAC/C,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACrD;IACJ;EACJ;AACJ,CAAC,CAAC", "ignoreList": []}]}