{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js!E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\scene\\NewScene.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\scene\\NewScene.vue", "mtime": 1754016754000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapState", "mapActions", "name", "data", "activeStep", "loading", "form", "linked_project_team_name", "linked_task_type_code", "scene_name", "scene_description", "input_platforms", "output_platforms", "input_platforms_data", "output_platforms_data", "input_data_options", "output_data_options", "input_effect_params", "output_effect_params", "input_effect_params_config", "output_effect_params_config", "updated_prompt", "scene_running_frequency", "hour", "modality", "day", "weeks", "stored_strategy_refresh_days", "explore_strategy_trigger_days", "scene_business_type", "baseline_data_start_days_ago", "baseline_data_exclude_recent_days", "min_baseline_sample_count", "baseline_refresh_frequency_days", "frequencyValue", "frequencyUnit", "rules", "required", "message", "trigger", "validator", "rule", "value", "callback", "Error", "type", "min", "max", "projectTeams", "taskTypes", "selectedInputPlatforms", "selectedOutputPlatforms", "addOptionDialogVisible", "newOptionForm", "option_name", "option_key", "platform_id", "optionRules", "addingOption", "modalityOptions", "addProjectDialogVisible", "addTaskTypeDialogVisible", "newProjectForm", "project_name", "newTaskTypeForm", "task_type_name", "task_type_description", "recommended_effect_param_codes", "effect_param_relationships_note", "is_content_from_external", "is_bilateral_pref_consideration_needed", "linked_platform_ids", "task_type_status", "task_type_owner", "selectedPlatformIds", "selectedEffectParams", "availableEffectParamsForTaskType", "projectRules", "taskTypeRules", "addingProject", "addingTaskType", "sceneTypes", "loadingInputPlatforms", "loadingOutputPlatforms", "computed", "availablePlatforms", "platforms", "selectedTaskType", "find", "taskType", "task_type_code", "linkedPlatformIds", "split", "map", "id", "parseInt", "trim", "filter", "platform", "includes", "availableEffectParams", "params", "JSON", "parse", "Array", "isArray", "error", "console", "isPlatformConfigLoading", "methods", "fetchPlatformOptions", "platformId", "response", "$http", "post", "api", "param", "fetchPlatformEffectParams", "getAvailableEffectParamsForPlatform", "recommendedParams", "p", "platformParams", "effectParams", "length", "effect_param_name", "getFieldComponent", "componentMap", "getFieldProps", "field", "props", "placeholder", "label", "field_type", "rows", "multiple", "options", "handleInputPlatformChange", "platformIds", "Object", "keys", "for<PERSON>ach", "key", "startsWith", "endsWith", "$delete", "platformsWithDetails", "detailResponse", "push", "$message", "platform_name", "$set", "additional_Information", "platformWithDetails", "fields", "fieldProp", "field_name", "handleOutputPlatformChange", "nextStep", "warning", "fieldsToValidate", "selectedPara<PERSON>", "paramCode", "configPrefix", "validateFields", "valid", "log", "validationPromises", "Promise", "resolve", "$refs", "validateField", "errorMessage", "all", "then", "results", "<PERSON><PERSON><PERSON><PERSON>", "some", "result", "prevStep", "submitForm", "validate", "accounts", "platformData", "dataOptions", "operate_type", "create_time", "Date", "toLocaleString", "replace", "adding_data_types", "join", "values", "paramConfig", "submitData", "info", "state", "create_user_id", "company_id", "effect_params", "success", "$router", "showAddOptionDialog", "$nextTick", "optionForm", "clearValidate", "addNewOption", "fetchModalityOptions", "dict_type", "fetchProjectTeams", "fetchTaskTypes", "fetchSceneTypes", "handleProjectTeamChange", "handleTaskTypeChange", "handleEffectParamsChange", "effectParam", "effect_param_code", "configured_evaluation_days", "default_baseline_mean", "default_baseline_stddev", "pattern", "getEffectParamsTableData", "config", "updateEffectParamConfig", "paramName", "getMinValue", "getMaxValue", "getStep", "calculateTotalMinutes", "totalMinutes", "parseFrequencyFromMinutes", "minutes", "handleFrequencyValueChange", "handleFrequencyUnitChange", "currentMinutes", "showAddProjectDialog", "projectForm", "addNewProject", "showAddTaskTypeDialog", "taskTypeForm", "handlePlatformSelectChange", "selectedIds", "fetchEffectParamsForTaskType", "platformParamsArrays", "allParams", "seenCodes", "Set", "has", "add", "handleEffectParamsSelectChange", "selectedCodes", "stringify", "addNewTaskType", "created", "fetchPlatforms", "page", "pageSize"], "sources": ["src/views/scene/NewScene.vue"], "sourcesContent": ["<template>\n    <div class=\"new-scene\">\n        <el-steps :active=\"activeStep\" finish-status=\"success\" simple>\n            <el-step title=\"基本信息\" />\n            <el-step title=\"计算基准线配置\" />\n            <el-step title=\"数据输入平台\" />\n            <el-step title=\"数据输出平台\" />\n            <el-step title=\"其他设置\" />\n        </el-steps>\n\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"150px\" class=\"mt-20\" v-loading=\"loading\">\n            <div v-show=\"activeStep === 0\">\n                <el-form-item label=\"场景名称\" prop=\"scene_name\">\n                    <el-input v-model=\"form.scene_name\" placeholder=\"请输入场景名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型\" prop=\"linked_task_type_code\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_task_type_code\" placeholder=\"请选择任务类型\" style=\"flex: 1;\" @change=\"handleTaskTypeChange\">\n                            <el-option\n                                v-for=\"taskType in taskTypes\"\n                                :key=\"taskType.task_type_code\"\n                                :label=\"taskType.task_type_name\"\n                                :value=\"taskType.task_type_code\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddTaskTypeDialog\">\n                            新增任务类型\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景类型\" prop=\"scene_business_type\">\n                    <el-select \n                        v-model=\"form.scene_business_type\" \n                        placeholder=\"请选择或输入场景类型\" \n                        filterable \n                        allow-create \n                        default-first-option\n                        style=\"width: 100%\">\n                        <el-option\n                            v-for=\"sceneType in sceneTypes\"\n                            :key=\"sceneType\"\n                            :label=\"sceneType\"\n                            :value=\"sceneType\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"项目组\" prop=\"linked_project_team_name\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_project_team_name\" placeholder=\"请选择项目组\" style=\"flex: 1;\" @change=\"handleProjectTeamChange\">\n                            <el-option\n                                v-for=\"project in projectTeams\"\n                                :key=\"project.project_id\"\n                                :label=\"project.project_name\"\n                                :value=\"project.project_name\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddProjectDialog\">\n                            新增项目组\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景描述\" prop=\"scene_description\">\n                    <el-input type=\"textarea\" v-model=\"form.scene_description\" placeholder=\"请输入场景描述\"\n                        :rows=\"4\"></el-input>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 1\">\n                <el-form-item label=\"使用数据的起始天数\" prop=\"baseline_data_start_days_ago\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_start_days_ago\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线使用数据的起始天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"排除最近的数据天数\" prop=\"baseline_data_exclude_recent_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_exclude_recent_days\"\n                            :min=\"0\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线排除最近的数据天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"样本量最小阈值\" prop=\"min_baseline_sample_count\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.min_baseline_sample_count\"\n                            :min=\"1\"\n                            :max=\"10000\"\n                            :step=\"1\"\n                            placeholder=\"请输入样本量\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">个</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"一个场景在一次计算基准线时，所需要的最小样本量\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"基准线更新频率\" prop=\"baseline_refresh_frequency_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_refresh_frequency_days\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入频率\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"评估效果的基准线更新的频率\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 2\">\n                <el-form-item label=\"选择输入平台\" prop=\"input_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.input_platforms\" @change=\"handleInputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingInputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedInputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingInputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输入平台配置</h3>\n                    <el-card v-for=\"platform in selectedInputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <div class=\"field-container\">\n                                <component :is=\"getFieldComponent(field.field_type)\"\n                                    v-model=\"form.input_platforms_data[platform.platform_id][field.field_name]\"\n                                    v-bind=\"getFieldProps(field)\"></component>\n                                <div v-if=\"field.description\" class=\"field-description\">{{ field.description }}</div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据类型\" :prop=\"'input_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.input_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\"\n                                    @click=\"showAddOptionDialog(platform.platform_id)\">\n                                    新增数据类型\n                                </el-button>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"效果参数配置\">\n                            <div class=\"effect-params-container\">\n                                <div v-if=\"getAvailableEffectParamsForPlatform(platform.platform_id).length === 0\" class=\"no-effect-params-tip\">\n                                    <el-alert\n                                        title=\"该平台暂无可用的效果参数\"\n                                        type=\"info\"\n                                        :closable=\"false\"\n                                        show-icon>\n                                    </el-alert>\n                                </div>\n                                <el-checkbox-group v-else v-model=\"form.input_effect_params[platform.platform_id]\" @change=\"handleEffectParamsChange(platform.platform_id)\">\n                                    <el-checkbox v-for=\"param in getAvailableEffectParamsForPlatform(platform.platform_id)\" :key=\"param.effect_param_code\" :label=\"param.effect_param_code\" class=\"effect-param-checkbox\">\n                                        {{ param.effect_param_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                \n                                <div v-if=\"form.input_effect_params[platform.platform_id] && form.input_effect_params[platform.platform_id].length > 0\" class=\"effect-params-table\">\n                                    <h4>参数配置详情</h4>\n                                    <el-table :data=\"getEffectParamsTableData(platform.platform_id)\" border>\n                                        <el-table-column prop=\"effect_param_name\" label=\"参数名称\" width=\"120\"></el-table-column>\n                                        <el-table-column prop=\"configured_evaluation_days\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"系统会获取发布时间在T-基线范围内，且已满足各参数的Tij值的样本总数量。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *效果实现天数\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.configured_evaluation_days`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.configured_evaluation_days\" \n                                                        placeholder=\"如：3,5,10\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'configured_evaluation_days', scope.row.configured_evaluation_days)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_mean\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"μi是选取的样本量的效果数据里，该参数的平均值。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算μi，而是会使用您输入的缺省状态下的μi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *平均值\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_mean`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.default_baseline_mean\" \n                                                        placeholder=\"如：0\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_mean', scope.row.default_baseline_mean)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_stddev\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"σi是选取的样本量的效果数据里，该参数的标准差。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算σi，而是会使用您输入的缺省状态下的σi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *标准差\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_stddev`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input\n                                                        v-model=\"scope.row.default_baseline_stddev\" \n                                                        placeholder=\"如：1\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_stddev', scope.row.default_baseline_stddev)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                    </el-table>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.input_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n            </div>\n\n            <div v-show=\"activeStep === 3\">\n                <el-form-item label=\"选择输出平台\" prop=\"output_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.output_platforms\" @change=\"handleOutputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingOutputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedOutputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingOutputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输出平台配置</h3>\n                    <el-card v-for=\"platform in selectedOutputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <div class=\"field-container\">\n                                <component :is=\"getFieldComponent(field.field_type)\"\n                                    v-model=\"form.output_platforms_data[platform.platform_id][field.field_name]\"\n                                    v-bind=\"getFieldProps(field)\"></component>\n                                <div v-if=\"field.description\" class=\"field-description\">{{ field.description }}</div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据内容\" :prop=\"'output_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.output_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.output_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n\n                <el-form-item label=\"模态\" prop=\"modality\">\n                    <el-select v-model=\"form.modality\" placeholder=\"请选择模态\">\n                        <el-option\n                            v-for=\"item in modalityOptions\"\n                            :key=\"item.dict_name\"\n                            :label=\"item.dict_name\"\n                            :value=\"item.dict_name\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 4\">\n                <el-form-item label=\"运行频率\" prop=\"scene_running_frequency\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-input-number\n                            v-model=\"frequencyValue\"\n                            :min=\"getMinValue()\"\n                            :max=\"getMaxValue()\"\n                            :step=\"getStep()\"\n                            placeholder=\"请输入数值\"\n                            style=\"width: 150px\"\n                            @change=\"handleFrequencyValueChange\">\n                        </el-input-number>\n                        <el-select \n                            v-model=\"frequencyUnit\" \n                            placeholder=\"请选择单位\"\n                            style=\"width: 120px\"\n                            @change=\"handleFrequencyUnitChange\">\n                            <el-option label=\"分钟\" value=\"minutes\"></el-option>\n                            <el-option label=\"小时\" value=\"hours\"></el-option>\n                            <el-option label=\"天\" value=\"days\"></el-option>\n                        </el-select>\n                        <span style=\"color: #909399; font-size: 12px;\">\n                            (最小间隔30分钟)\n                        </span>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"个性化进化更新频率\" prop=\"stored_strategy_refresh_days\">\n                    <el-input-number\n                        v-model=\"form.stored_strategy_refresh_days\"\n                        :min=\"0\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">建议您设为0</span>\n                </el-form-item>\n                <el-form-item label=\"AI自行探索频率\" width=\"400px\" prop=\"explore_strategy_trigger_days\">\n                    <el-input-number\n                        v-model=\"form.explore_strategy_trigger_days\"\n                        :min=\"1\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">目前我们的探索模式暂未上线，建议您先将Z值设为365天</span>\n                </el-form-item>\n                <el-form-item label=\"AI提示词\" prop=\"updated_prompt\">\n                    <el-input type=\"textarea\" v-model=\"form.updated_prompt\" placeholder=\"请输入AI提示词\" :rows=\"10\"></el-input>\n                </el-form-item>\n            </div>\n\n            <el-form-item class=\"navigation-buttons\">\n                <el-button v-if=\"activeStep > 0\" @click=\"prevStep\">上一步</el-button>\n                <el-button \n                    v-if=\"activeStep < 4\" \n                    type=\"primary\" \n                    @click=\"nextStep\"\n                    :disabled=\"isPlatformConfigLoading\"\n                    :loading=\"isPlatformConfigLoading\">\n                    {{ isPlatformConfigLoading ? '配置加载中...' : '下一步' }}\n                </el-button>\n                <el-button v-if=\"activeStep === 4\" type=\"primary\" @click=\"submitForm\">提交</el-button>\n            </el-form-item>\n        </el-form>\n\n        <el-dialog title=\"新增数据内容\" :visible.sync=\"addOptionDialogVisible\" width=\"500px\">\n            <el-form ref=\"optionForm\" :model=\"newOptionForm\" :rules=\"optionRules\" label-width=\"120px\">\n                <el-form-item label=\"内容名称\" prop=\"option_name\">\n                    <el-input v-model=\"newOptionForm.option_name\" placeholder=\"请输入内容名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"内容标识\" prop=\"option_key\">\n                    <el-input v-model=\"newOptionForm.option_key\" placeholder=\"请输入内容标识（英文）\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addOptionDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewOption\" :loading=\"addingOption\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增项目组\" :visible.sync=\"addProjectDialogVisible\" width=\"500px\">\n            <el-form ref=\"projectForm\" :model=\"newProjectForm\" :rules=\"projectRules\" label-width=\"120px\">\n                <el-form-item label=\"项目名称\" prop=\"project_name\">\n                    <el-input v-model=\"newProjectForm.project_name\" placeholder=\"请输入项目名称\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addProjectDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewProject\" :loading=\"addingProject\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增任务类型\" :visible.sync=\"addTaskTypeDialogVisible\" width=\"600px\">\n            <el-form ref=\"taskTypeForm\" :model=\"newTaskTypeForm\" :rules=\"taskTypeRules\" label-width=\"140px\">\n                <el-form-item label=\"任务类型名称\" prop=\"task_type_name\">\n                    <el-input v-model=\"newTaskTypeForm.task_type_name\" placeholder=\"请输入任务类型名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型描述\" prop=\"task_type_description\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.task_type_description\" placeholder=\"请输入任务类型描述\" :rows=\"3\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"关联平台\" prop=\"linked_platform_ids\">\n                    <el-select \n                        v-model=\"selectedPlatformIds\" \n                        multiple \n                        placeholder=\"请选择关联平台\"\n                        style=\"width: 100%\"\n                        @change=\"handlePlatformSelectChange\">\n                        <el-option\n                            v-for=\"platform in platforms\"\n                            :key=\"platform.platform_id\"\n                            :label=\"platform.platform_name\"\n                            :value=\"platform.platform_id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"推荐效果参数\" prop=\"recommended_effect_param_codes\">\n                    <el-select \n                        v-model=\"selectedEffectParams\" \n                        multiple \n                        placeholder=\"请选择推荐效果参数\"\n                        style=\"width: 100%\"\n                        @change=\"handleEffectParamsSelectChange\">\n                        <el-option\n                            v-for=\"param in availableEffectParamsForTaskType\"\n                            :key=\"param.effect_param_code\"\n                            :label=\"`${param.effect_param_name} (${param.effect_param_code})`\"\n                            :value=\"param.effect_param_name\">\n                        </el-option>\n                    </el-select>\n                    <div v-if=\"availableEffectParamsForTaskType.length === 0 && selectedPlatformIds.length > 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        所选平台暂无可用的效果参数\n                    </div>\n                    <div v-if=\"selectedPlatformIds.length === 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        请先选择关联平台\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"参数关系说明\" prop=\"effect_param_relationships_note\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.effect_param_relationships_note\" placeholder=\"请输入各推荐参数之间的逻辑关系说明\" :rows=\"3\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addTaskTypeDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewTaskType\" :loading=\"addingTaskType\">确定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\n\nexport default {\n    name: 'NewScene',\n    data() {\n        return {\n            activeStep: 0,\n            loading: false,\n            form: {\n                linked_project_team_name: null,\n                linked_task_type_code: null,\n                scene_name: '',\n                scene_description: '',\n                input_platforms: [],\n                output_platforms: [],\n                input_platforms_data: {},\n                output_platforms_data: {},\n                input_data_options: {},\n                output_data_options: {},\n                input_effect_params: {},\n                output_effect_params: {},\n                input_effect_params_config: {},\n                output_effect_params_config: {},\n                updated_prompt: '',\n                scene_running_frequency: '',\n                hour: '',\n                modality: '',\n                day: '',\n                weeks: '',\n                stored_strategy_refresh_days: 0,\n                explore_strategy_trigger_days: 365,\n                scene_business_type: '',\n                baseline_data_start_days_ago: 30,\n                baseline_data_exclude_recent_days: 3,\n                min_baseline_sample_count: 3,\n                baseline_refresh_frequency_days: 7\n            },\n            frequencyValue: 30,\n            frequencyUnit: 'minutes',\n            rules: {\n                linked_project_team_name: [\n                    { \n                        required: true, \n                        message: '请选择项目组', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            if (!value) {\n                                callback(new Error('请选择项目组'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                linked_task_type_code: [\n                    { \n                        required: true, \n                        message: '请选择任务类型', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            if (!value) {\n                                callback(new Error('请选择任务类型'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                scene_name: [\n                    { required: true, message: '请输入场景名称', trigger: 'blur' }\n                ],\n                scene_description: [\n                    { required: false, message: '请输入场景描述', trigger: 'blur' }\n                ],\n                input_platforms: [\n                    { required: true, message: '请选择输入平台', trigger: 'change' }\n                ],\n                output_platforms: [\n                    { required: true, message: '请选择输出平台', trigger: 'change' }\n                ],\n                updated_prompt: [\n                    { required: true, message: '请输入AI提示词', trigger: 'blur' }\n                ],\n                scene_running_frequency: [\n                    { required: true, message: '请设置运行频率', trigger: 'change' },\n                    { \n                        type: 'number', \n                        min: 30, \n                        message: '运行频率最小间隔为30分钟', \n                        trigger: 'change' \n                    }\n                ],\n                stored_strategy_refresh_days: [\n                    { required: true, message: '请输入个性化进化策略更新频率', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-365之间的天数', trigger: 'blur' }\n                ],\n                explore_strategy_trigger_days: [\n                    { required: true, message: '请输入AI自行探索频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                scene_business_type: [\n                    { required: true, message: '请选择或输入场景类型', trigger: 'change' }\n                ],\n                baseline_data_start_days_ago: [\n                    { required: true, message: '请输入使用数据的起始天数', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                baseline_data_exclude_recent_days: [\n                    { required: true, message: '请输入排除最近的数据天数', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-30之间的天数', trigger: 'blur' }\n                ],\n                min_baseline_sample_count: [\n                    { required: true, message: '请输入样本量最小阈值', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 10000, message: '请输入1-10000之间的数值', trigger: 'blur' }\n                ],\n                baseline_refresh_frequency_days: [\n                    { required: true, message: '请输入基准线更新频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ]\n            },\n            projectTeams: [],\n            taskTypes: [],\n            selectedInputPlatforms: [],\n            selectedOutputPlatforms: [],\n            addOptionDialogVisible: false,\n            newOptionForm: {\n                option_name: '',\n                option_key: '',\n                platform_id: ''\n            },\n            optionRules: {\n                option_name: [\n                    { required: true, message: '请输入内容名称', trigger: 'blur' }\n                ],\n                option_key: [\n                    { required: true, message: '请输入内容标识（英文）', trigger: 'blur' }\n                ]\n            },\n            addingOption: false,\n            modalityOptions: [],\n            addProjectDialogVisible: false,\n            addTaskTypeDialogVisible: false,\n            newProjectForm: {\n                project_name: ''\n            },\n            newTaskTypeForm: {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            },\n            selectedPlatformIds: [],\n            selectedEffectParams: [],\n            availableEffectParamsForTaskType: [],\n            projectRules: {\n                project_name: [\n                    { required: true, message: '请输入项目名称', trigger: 'blur' }\n                ]\n            },\n            taskTypeRules: {\n                task_type_name: [\n                    { required: true, message: '请输入任务类型名称', trigger: 'blur' }\n                ],\n                task_type_description: [\n                    { required: true, message: '请输入任务类型描述', trigger: 'blur' }\n                ],\n                recommended_effect_param_codes: [\n                    { required: true, message: '请输入推荐效果参数编码', trigger: 'blur' }\n                ],\n                effect_param_relationships_note: [\n                    { required: true, message: '请输入各推荐参数之间的逻辑关系说明', trigger: 'blur' }\n                ]\n            },\n            addingProject: false,\n            addingTaskType: false,\n            sceneTypes: [],\n            loadingInputPlatforms: false,\n            loadingOutputPlatforms: false\n        }\n    },\n    computed: {\n        ...mapState(['platforms']),\n        availablePlatforms() {\n            if (!this.form.linked_task_type_code) {\n                return this.platforms\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.linked_platform_ids) {\n                return this.platforms\n            }\n            \n            const linkedPlatformIds = selectedTaskType.linked_platform_ids.split(',').map(id => parseInt(id.trim()))\n            return this.platforms.filter(platform => linkedPlatformIds.includes(platform.platform_id))\n        },\n        availableEffectParams() {\n            if (!this.form.linked_task_type_code) {\n                return []\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.recommended_effect_param_codes) {\n                return []\n            }\n            \n            try {\n                const params = JSON.parse(selectedTaskType.recommended_effect_param_codes)\n                return Array.isArray(params) ? params : []\n            } catch (error) {\n                console.error('解析效果参数失败:', error)\n                return []\n            }\n        },\n        isPlatformConfigLoading() {\n            if (this.activeStep === 2 && this.loadingInputPlatforms) {\n                return true\n            }\n            if (this.activeStep === 3 && this.loadingOutputPlatforms) {\n                return true\n            }\n            return false\n        }\n    },\n    methods: {\n        ...mapActions(['fetchPlatforms']),\n        async fetchPlatformOptions(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/option/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform options:', error)\n                return []\n            }\n        },\n        async fetchPlatformEffectParams(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/effectParamCategory/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform effect params:', error)\n                return []\n            }\n        },\n        getAvailableEffectParamsForPlatform(platformId) {\n            const recommendedParams = this.availableEffectParams || []\n            \n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n            \n            if (platformParams.length === 0) {\n                return []\n            }\n            \n            return platformParams.filter(param => recommendedParams.includes(param.effect_param_name))\n        },\n        getFieldComponent(type) {\n            const componentMap = {\n                'string': 'el-input',\n                'password': 'el-input',\n                'select': 'el-select',\n                'multiselect': 'el-select',\n                'number': 'el-input-number',\n                'bool': 'el-switch',\n                'textarea': 'el-input'\n            }\n            return componentMap[type] || 'el-input'\n        },\n        getFieldProps(field) {\n            const props = {\n                placeholder: `请输入${field.label}`\n            }\n            if (field.field_type === 'password') {\n                props.type = 'password'\n            }\n            if (field.field_type === 'textarea') {\n                props.type = 'textarea'\n                props.rows = 3\n            }\n            if (field.field_type === 'select' || field.field_type === 'multiselect') {\n                props.multiple = field.field_type === 'multiselect'\n                props.options = field.options || []\n            }\n            return props\n        },\n        async handleInputPlatformChange(platformIds) {\n            if (this.loadingInputPlatforms) {\n                return\n            }\n            this.loadingInputPlatforms = true\n            \n            try {\n                this.selectedInputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('input_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n                        \n                        const effectParams = await this.fetchPlatformEffectParams(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options,\n                            effectParams: effectParams\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedInputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n                    if (!this.form.input_platforms_data[platformId]) {\n                        this.$set(this.form.input_platforms_data, platformId, {\n                            additional_Information: ''\n                        })\n                    }\n\n                    if (!this.form.input_data_options[platformId]) {\n                        this.$set(this.form.input_data_options, platformId, [])\n                    }\n\n                    if (!this.form.input_effect_params[platformId]) {\n                        this.$set(this.form.input_effect_params, platformId, [])\n                    }\n\n                    const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n                    if (platformWithDetails && platformWithDetails.fields) {\n                        platformWithDetails.fields.forEach(field => {\n                            if (field.required) {\n                                const fieldProp = `input_platforms_data.${platformId}.${field.field_name}`\n                                this.$set(this.rules, fieldProp, [\n                                    { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                ])\n                            }\n                        })\n                    }\n                }\n            } catch (error) {\n                console.error('Error in handleInputPlatformChange:', error)\n                this.$message.error('处理输入平台变化时出错')\n            } finally {\n                this.loadingInputPlatforms = false\n            }\n        },\n        async handleOutputPlatformChange(platformIds) {\n            if (this.loadingOutputPlatforms) {\n                return\n            }\n            this.loadingOutputPlatforms = true\n            \n            try {\n                this.selectedOutputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('output_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedOutputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n                    if (!this.form.output_platforms_data[platformId]) {\n                        this.$set(this.form.output_platforms_data, platformId, {\n                            additional_Information: ''\n                        })\n                    }\n\n                    if (!this.form.output_data_options[platformId]) {\n                        this.$set(this.form.output_data_options, platformId, [])\n                    }\n\n                    const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)\n                    if (platformWithDetails && platformWithDetails.fields) {\n                        platformWithDetails.fields.forEach(field => {\n                            if (field.required) {\n                                const fieldProp = `output_platforms_data.${platformId}.${field.field_name}`\n                                this.$set(this.rules, fieldProp, [\n                                    { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                ])\n                            }\n                        })\n                    }\n                }\n            } catch (error) {\n                console.error('Error in handleOutputPlatformChange:', error)\n                this.$message.error('处理输出平台变化时出错')\n            } finally {\n                this.loadingOutputPlatforms = false\n            }\n        },\n        nextStep() {\n            if (this.isPlatformConfigLoading) {\n                this.$message.warning('平台配置正在加载中，请稍候...')\n                return\n            }\n\n            let fieldsToValidate = []\n\n            switch (this.activeStep) {\n                case 0:\n                    fieldsToValidate = ['linked_project_team_name', 'linked_task_type_code', 'scene_business_type', 'scene_name', 'scene_description']\n                    break\n                case 1:\n                    fieldsToValidate = ['baseline_data_start_days_ago', 'baseline_data_exclude_recent_days', 'min_baseline_sample_count', 'baseline_refresh_frequency_days']\n                    break\n                case 2:\n                    fieldsToValidate = ['input_platforms']\n                    \n                    this.selectedInputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `input_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n                        \n                        const selectedParams = this.form.input_effect_params[platform.platform_id] || []\n                        selectedParams.forEach(paramCode => {\n                            const configPrefix = `input_effect_params_config.${platform.platform_id}.${paramCode}`\n                            fieldsToValidate.push(`${configPrefix}.configured_evaluation_days`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_mean`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_stddev`)\n                        })\n                    })\n                    break\n                case 3:\n                    fieldsToValidate = ['output_platforms']\n                    \n                    this.selectedOutputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `output_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n                    })\n                    break\n                case 4:\n                    fieldsToValidate = ['updated_prompt', 'scene_running_frequency', 'stored_strategy_refresh_days', 'explore_strategy_trigger_days']\n                    break\n                default:\n                    break\n            }\n\n            this.validateFields(fieldsToValidate, (valid) => {\n                if (valid) {\n                    this.activeStep++\n                }\n            })\n        },\n        validateFields(fields, callback) {\n            if (fields.length === 0) {\n                callback(true)\n                return\n            }\n\n            console.log('验证字段:', fields)\n            console.log('当前表单数据:', this.form)\n\n            const validationPromises = fields.map(field => {\n                return new Promise((resolve) => {\n                    this.$refs.form.validateField(field, (errorMessage) => {\n                        console.log(`字段 ${field} 验证结果:`, errorMessage)\n                        resolve({ field, errorMessage })\n                    })\n                })\n            })\n\n            Promise.all(validationPromises).then(results => {\n                const hasError = results.some(result => result.errorMessage)\n                console.log('验证结果:', results, '是否有错误:', hasError)\n                callback(!hasError)\n            })\n        },\n        prevStep() {\n            this.activeStep--\n        },\n        async submitForm() {\n            this.$refs.form.validate(async valid => {\n                if (valid) {\n                    try {\n                        const accounts = []\n\n                        const effectParams = []\n\n                        for (const platformId of this.form.input_platforms) {\n                            const platformData = this.form.input_platforms_data[platformId] || {}\n                            const dataOptions = this.form.input_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 1,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n\n                            if (this.form.input_effect_params_config[platformId]) {\n                                Object.values(this.form.input_effect_params_config[platformId]).forEach(paramConfig => {\n                                    effectParams.push({\n                                        platform_id: platformId,\n                                        ...paramConfig\n                                    })\n                                })\n                            }\n                        }\n\n                        for (const platformId of this.form.output_platforms) {\n                            const platformData = this.form.output_platforms_data[platformId] || {}\n                            const dataOptions = this.form.output_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 2,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n                        }\n\n                        const { input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, ...submitData } = this.form\n                        console.info(input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config)\n\n                        submitData.state = 2;\n                        submitData.create_time = new Date().toLocaleString('sv-SE').replace('T', ' ');\n                        submitData.create_user_id = 0;\n                        submitData.company_id = 0;\n\n                        await this.$http.post('', {\n                            api: '/api/scene/add',\n                            data: submitData,\n                            accounts: accounts,\n                            effect_params: effectParams\n                        })\n                        this.$message.success('场景创建成功')\n                        this.$router.push('/')\n                    } catch (error) {\n                        this.$message.error('场景创建失败')\n                        console.error('Error creating scene:', error)\n                    }\n                }\n            })\n        },\n        showAddOptionDialog(platformId) {\n            this.newOptionForm = {\n                option_name: '',\n                option_key: '',\n                platform_id: platformId\n            }\n            this.addOptionDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.optionForm && this.$refs.optionForm.clearValidate()\n            })\n        },\n        async addNewOption() {\n            this.$refs.optionForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingOption = true\n                        await this.$http.post('', {\n                            api: '/api/option/add',\n                            data: {\n                                ...this.newOptionForm,\n                                platform_id: this.newOptionForm.platform_id\n                            }\n                        })\n                        this.$message.success('新增数据类型成功')\n                        this.addOptionDialogVisible = false\n                        this.newOptionForm = {\n                            option_name: '',\n                            option_key: '',\n                            platform_id: ''\n                        }\n                        await this.handleInputPlatformChange(this.form.input_platforms)\n                        await this.handleOutputPlatformChange(this.form.output_platforms)\n                    } catch (error) {\n                        this.$message.error('新增数据类型失败')\n                        console.error('Error adding new option:', error)\n                    } finally {\n                        this.addingOption = false\n                    }\n                }\n            })\n        },\n        async fetchModalityOptions() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'modality'\n                    }\n                })\n                this.modalityOptions = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching modality options:', error)\n                this.$message.error('获取模态列表失败')\n            }\n        },\n        async fetchProjectTeams() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/projectTeam/getList'\n                })\n                this.projectTeams = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching project teams:', error)\n                this.$message.error('获取项目组列表失败')\n            }\n        },\n        async fetchTaskTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/taskType/getList'\n                })\n                this.taskTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching task types:', error)\n                this.$message.error('获取任务类型列表失败')\n            }\n        },\n        async fetchSceneTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/sceneType/getList'\n                })\n                this.sceneTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching scene types:', error)\n                this.$message.error('获取场景类型列表失败')\n            }\n        },\n        handleProjectTeamChange() {\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_project_team_name')\n            })\n        },\n        handleTaskTypeChange() {\n            this.form.input_platforms = []\n            this.form.output_platforms = []\n            this.selectedInputPlatforms = []\n            this.selectedOutputPlatforms = []\n            this.form.input_platforms_data = {}\n            this.form.output_platforms_data = {}\n            this.form.input_data_options = {}\n            this.form.output_data_options = {}\n            this.form.input_effect_params = {}\n            this.form.input_effect_params_config = {}\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_task_type_code')\n            })\n        },\n        handleEffectParamsChange(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n\n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n\n            if (platformParams.length == 0) {\n                return;\n            }\n\n            Object.keys(this.rules).forEach(key => {\n                if (key.startsWith(`input_effect_params_config.${platformId}.`)) {\n                    this.$delete(this.rules, key)\n                }\n            })\n\n            selectedParams.forEach(param => {\n                if (!this.form.input_effect_params_config[platformId][param]) {\n\n                    const effectParam = platformParams.find(p => p.effect_param_code === param);\n\n                    this.$set(this.form.input_effect_params_config[platformId], param, {\n                        effect_param_code: effectParam.effect_param_code,\n                        effect_param_name: effectParam.effect_param_name,\n                        configured_evaluation_days: '',\n                        default_baseline_mean: '',\n                        default_baseline_stddev: ''\n                    })\n                }\n\n                const configPrefix = `input_effect_params_config.${platformId}.${param}`\n                this.$set(this.rules, `${configPrefix}.configured_evaluation_days`, [\n                    { required: true, message: '请输入效果实现天数', trigger: 'blur' },\n                    { pattern: /^[\\d,\\s]+$/, message: '请输入有效的天数格式，如：3,5,10', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_mean`, [\n                    { required: true, message: '请输入平均值', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '平均值必须是数字', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_stddev`, [\n                    { required: true, message: '请输入标准差', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '标准差必须是数字', trigger: 'blur' }\n                ])\n            })\n            \n            Object.keys(this.form.input_effect_params_config[platformId]).forEach(param => {\n                if (!selectedParams.includes(param)) {\n                    this.$delete(this.form.input_effect_params_config[platformId], param)\n                }\n            })\n        },\n        getEffectParamsTableData(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n            const config = this.form.input_effect_params_config[platformId] || {}\n            \n            return selectedParams.map(paramCode => ({\n                effect_param_code: paramCode,\n                effect_param_name: (config[paramCode] && config[paramCode].effect_param_name) || '',\n                configured_evaluation_days: (config[paramCode] && config[paramCode].configured_evaluation_days) || '',\n                default_baseline_mean: (config[paramCode] && config[paramCode].default_baseline_mean) || '',\n                default_baseline_stddev: (config[paramCode] && config[paramCode].default_baseline_stddev) || ''\n            }))\n        },\n        updateEffectParamConfig(platformId, paramName, field, value) {\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n            if (!this.form.input_effect_params_config[platformId][paramName]) {\n                this.$set(this.form.input_effect_params_config[platformId], paramName, {})\n            }\n            this.$set(this.form.input_effect_params_config[platformId][paramName], field, value)\n        },\n        getMinValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30\n                case 'hours':\n                    return 1\n                case 'days':\n                    return 1\n                default:\n                    return 1\n            }\n        },\n        getMaxValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 1440\n                case 'hours':\n                    return 24\n                case 'days':\n                    return 365\n                default:\n                    return 1\n            }\n        },\n        getStep() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30\n                case 'hours':\n                    return 1\n                case 'days':\n                    return 1\n                default:\n                    return 1\n            }\n        },\n        calculateTotalMinutes() {\n            let totalMinutes = 0\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    totalMinutes = this.frequencyValue\n                    break\n                case 'hours':\n                    totalMinutes = this.frequencyValue * 60\n                    break\n                case 'days':\n                    totalMinutes = this.frequencyValue * 24 * 60\n                    break\n            }\n            \n            if (totalMinutes < 30) {\n                totalMinutes = 30\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n            }\n            \n            this.form.scene_running_frequency = totalMinutes\n            return totalMinutes\n        },\n        parseFrequencyFromMinutes(minutes) {\n            if (!minutes || minutes < 30) {\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n                return\n            }\n            \n            if (minutes >= 1440 && minutes % 1440 === 0) {\n                this.frequencyValue = minutes / 1440\n                this.frequencyUnit = 'days'\n            } else if (minutes >= 60 && minutes % 60 === 0) {\n                this.frequencyValue = minutes / 60\n                this.frequencyUnit = 'hours'\n            } else {\n                this.frequencyValue = minutes\n                this.frequencyUnit = 'minutes'\n            }\n        },\n        handleFrequencyValueChange() {\n            this.calculateTotalMinutes()\n        },\n        handleFrequencyUnitChange() {\n            const currentMinutes = this.calculateTotalMinutes()\n            \n            this.parseFrequencyFromMinutes(currentMinutes)\n            this.calculateTotalMinutes()\n        },\n        showAddProjectDialog() {\n            this.newProjectForm = {\n                project_name: ''\n            }\n            this.addProjectDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.projectForm && this.$refs.projectForm.clearValidate()\n            })\n        },\n        async addNewProject() {\n            this.$refs.projectForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingProject = true\n                        await this.$http.post('', {\n                            api: '/api/projectTeam/add',\n                            data: this.newProjectForm\n                        })\n                        this.$message.success('新增项目组成功')\n                        this.addProjectDialogVisible = false\n                        await this.fetchProjectTeams()\n                        this.form.linked_project_team_name = this.newProjectForm.project_name\n                    } catch (error) {\n                        this.$message.error('新增项目组失败')\n                        console.error('Error adding new project:', error)\n                    } finally {\n                        this.addingProject = false\n                    }\n                }\n            })\n        },\n        showAddTaskTypeDialog() {\n            this.newTaskTypeForm = {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            }\n            this.selectedPlatformIds = []\n            this.selectedEffectParams = []\n            this.availableEffectParamsForTaskType = []\n            this.addTaskTypeDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.taskTypeForm && this.$refs.taskTypeForm.clearValidate()\n            })\n        },\n        async handlePlatformSelectChange(selectedIds) {\n            this.newTaskTypeForm.linked_platform_ids = selectedIds.join(',')\n            \n            await this.fetchEffectParamsForTaskType(selectedIds)\n        },\n        async fetchEffectParamsForTaskType(platformIds) {\n            if (platformIds.length === 0) {\n                this.availableEffectParamsForTaskType = []\n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                return\n            }\n            \n            try {\n                const platformParamsArrays = []\n                for (const platformId of platformIds) {\n                    const params = await this.fetchPlatformEffectParams(platformId)\n                    platformParamsArrays.push(params)\n                }\n                \n                const allParams = []\n                const seenCodes = new Set()\n                \n                platformParamsArrays.forEach(platformParams => {\n                    platformParams.forEach(param => {\n                        if (!seenCodes.has(param.effect_param_code)) {\n                            seenCodes.add(param.effect_param_code)\n                            allParams.push(param)\n                        }\n                    })\n                })\n                \n                this.availableEffectParamsForTaskType = allParams\n                \n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                \n            } catch (error) {\n                console.error('Error fetching effect params for task type:', error)\n                this.$message.error('获取效果参数失败')\n                this.availableEffectParamsForTaskType = []\n            }\n        },\n        handleEffectParamsSelectChange(selectedCodes) {\n            this.newTaskTypeForm.recommended_effect_param_codes = JSON.stringify(selectedCodes)\n        },\n        async addNewTaskType() {\n            this.$refs.taskTypeForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingTaskType = true\n                        await this.$http.post('', {\n                            api: '/api/taskType/add',\n                            data: this.newTaskTypeForm\n                        })\n                        this.$message.success('新增任务类型成功')\n                        this.addTaskTypeDialogVisible = false\n                        await this.fetchTaskTypes()\n                    } catch (error) {\n                        this.$message.error('新增任务类型失败')\n                        console.error('Error adding new task type:', error)\n                    } finally {\n                        this.addingTaskType = false\n                    }\n                }\n            })\n        }\n    },\n    async created() {\n        await this.fetchPlatforms({ page: 1, pageSize: 100 })\n        await this.fetchProjectTeams()\n        await this.fetchTaskTypes()\n        await this.fetchModalityOptions()\n        await this.fetchSceneTypes()\n        \n        this.calculateTotalMinutes()\n    }\n}\n</script>\n\n<style scoped>\n.new-scene {\n    padding: 20px;\n}\n\n.mt-20 {\n    margin-top: 20px;\n}\n\n.el-steps {\n    margin-bottom: 30px;\n}\n\n.platform-configs {\n    margin-top: 20px;\n    margin-bottom: 20px;\n}\n\n.platform-configs h3 {\n    margin-bottom: 16px;\n    color: #303133;\n    font-size: 16px;\n}\n\n.platform-card {\n    margin-bottom: 16px;\n}\n\n.platform-card:last-child {\n    margin-bottom: 0;\n}\n\n.data-options-container {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n}\n\n.checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.platform-selection-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.navigation-buttons {\n    margin-top: 30px;\n}\n\n.field-container {\n    display: flex;\n    flex-direction: column;\n    gap: 4px;\n}\n\n.field-description {\n    font-size: 12px;\n    color: #909399;\n    line-height: 1.4;\n}\n\n.platform-selection-tip {\n    margin-bottom: 16px;\n}\n\n.no-platforms-tip {\n    margin-top: 16px;\n}\n\n.loading-tip {\n    margin-top: 16px;\n}\n\n.effect-params-container {\n    margin-top: 16px;\n}\n\n.effect-param-checkbox {\n    margin-right: 16px;\n    margin-bottom: 8px;\n}\n\n.effect-params-table {\n    margin-top: 16px;\n}\n\n.effect-params-table h4 {\n    margin-bottom: 12px;\n    color: #303133;\n    font-size: 14px;\n}\n\n\n\n.no-effect-params-tip {\n    margin-bottom: 16px;\n}\n\n.table-header-with-tooltip {\n    cursor: help;\n    display: flex;\n    align-items: center;\n    gap: 4px;\n}\n\n.table-header-with-tooltip .el-icon-question {\n    color: #909399;\n    font-size: 14px;\n}\n</style>"], "mappings": "AAgiBA,SAAAA,QAAA,EAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,UAAA;MACAC,OAAA;MACAC,IAAA;QACAC,wBAAA;QACAC,qBAAA;QACAC,UAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,oBAAA;QACAC,0BAAA;QACAC,2BAAA;QACAC,cAAA;QACAC,uBAAA;QACAC,IAAA;QACAC,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,4BAAA;QACAC,6BAAA;QACAC,mBAAA;QACAC,4BAAA;QACAC,iCAAA;QACAC,yBAAA;QACAC,+BAAA;MACA;MACAC,cAAA;MACAC,aAAA;MACAC,KAAA;QACA7B,wBAAA,GACA;UACA8B,QAAA;UACAC,OAAA;UACAC,OAAA;UACAC,SAAA,EAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA,KAAAD,KAAA;cACAC,QAAA,KAAAC,KAAA;YACA;cACAD,QAAA;YACA;UACA;QACA,EACA;QACAnC,qBAAA,GACA;UACA6B,QAAA;UACAC,OAAA;UACAC,OAAA;UACAC,SAAA,EAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA,KAAAD,KAAA;cACAC,QAAA,KAAAC,KAAA;YACA;cACAD,QAAA;YACA;UACA;QACA,EACA;QACAlC,UAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA7B,iBAAA,GACA;UAAA2B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA5B,eAAA,GACA;UAAA0B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA3B,gBAAA,GACA;UAAAyB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,cAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjB,uBAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAM,IAAA;UACAC,GAAA;UACAR,OAAA;UACAC,OAAA;QACA,EACA;QACAZ,4BAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,6BAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,mBAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,4BAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,iCAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,yBAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,+BAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAS,YAAA;MACAC,SAAA;MACAC,sBAAA;MACAC,uBAAA;MACAC,sBAAA;MACAC,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,WAAA;MACA;MACAC,WAAA;QACAH,WAAA,GACA;UAAAjB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAgB,UAAA,GACA;UAAAlB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAmB,YAAA;MACAC,eAAA;MACAC,uBAAA;MACAC,wBAAA;MACAC,cAAA;QACAC,YAAA;MACA;MACAC,eAAA;QACAC,cAAA;QACAC,qBAAA;QACAC,8BAAA;QACAC,+BAAA;QACAC,wBAAA;QACAC,sCAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,eAAA;MACA;MACAC,mBAAA;MACAC,oBAAA;MACAC,gCAAA;MACAC,YAAA;QACAd,YAAA,GACA;UAAA1B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAuC,aAAA;QACAb,cAAA,GACA;UAAA5B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA2B,qBAAA,GACA;UAAA7B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA4B,8BAAA,GACA;UAAA9B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA6B,+BAAA,GACA;UAAA/B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAwC,aAAA;MACAC,cAAA;MACAC,UAAA;MACAC,qBAAA;MACAC,sBAAA;IACA;EACA;EACAC,QAAA;IACA,GAAApF,QAAA;IACAqF,mBAAA;MACA,UAAA/E,IAAA,CAAAE,qBAAA;QACA,YAAA8E,SAAA;MACA;MAEA,MAAAC,gBAAA,QAAAtC,SAAA,CAAAuC,IAAA,CAAAC,QAAA,IAAAA,QAAA,CAAAC,cAAA,UAAApF,IAAA,CAAAE,qBAAA;MACA,KAAA+E,gBAAA,KAAAA,gBAAA,CAAAhB,mBAAA;QACA,YAAAe,SAAA;MACA;MAEA,MAAAK,iBAAA,GAAAJ,gBAAA,CAAAhB,mBAAA,CAAAqB,KAAA,MAAAC,GAAA,CAAAC,EAAA,IAAAC,QAAA,CAAAD,EAAA,CAAAE,IAAA;MACA,YAAAV,SAAA,CAAAW,MAAA,CAAAC,QAAA,IAAAP,iBAAA,CAAAQ,QAAA,CAAAD,QAAA,CAAA1C,WAAA;IACA;IACA4C,sBAAA;MACA,UAAA9F,IAAA,CAAAE,qBAAA;QACA;MACA;MAEA,MAAA+E,gBAAA,QAAAtC,SAAA,CAAAuC,IAAA,CAAAC,QAAA,IAAAA,QAAA,CAAAC,cAAA,UAAApF,IAAA,CAAAE,qBAAA;MACA,KAAA+E,gBAAA,KAAAA,gBAAA,CAAApB,8BAAA;QACA;MACA;MAEA;QACA,MAAAkC,MAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAhB,gBAAA,CAAApB,8BAAA;QACA,OAAAqC,KAAA,CAAAC,OAAA,CAAAJ,MAAA,IAAAA,MAAA;MACA,SAAAK,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA;MACA;IACA;IACAE,wBAAA;MACA,SAAAxG,UAAA,eAAA8E,qBAAA;QACA;MACA;MACA,SAAA9E,UAAA,eAAA+E,sBAAA;QACA;MACA;MACA;IACA;EACA;EACA0B,OAAA;IACA,GAAA5G,UAAA;IACA,MAAA6G,qBAAAC,UAAA;MACA;QACA,MAAAC,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACA5D,WAAA,EAAAuD;UACA;QACA;QACA,OAAAC,QAAA,CAAA7G,IAAA,CAAAA,IAAA;MACA,SAAAuG,KAAA;QACAC,OAAA,CAAAD,KAAA,qCAAAA,KAAA;QACA;MACA;IACA;IACA,MAAAW,0BAAAN,UAAA;MACA;QACA,MAAAC,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACA5D,WAAA,EAAAuD;UACA;QACA;QACA,OAAAC,QAAA,CAAA7G,IAAA,CAAAA,IAAA;MACA,SAAAuG,KAAA;QACAC,OAAA,CAAAD,KAAA,2CAAAA,KAAA;QACA;MACA;IACA;IACAY,oCAAAP,UAAA;MACA,MAAAQ,iBAAA,QAAAnB,qBAAA;MAEA,MAAAF,QAAA,QAAAhD,sBAAA,CAAAsC,IAAA,CAAAgC,CAAA,IAAAA,CAAA,CAAAhE,WAAA,KAAAuD,UAAA;MACA,MAAAU,cAAA,GAAAvB,QAAA,GAAAA,QAAA,CAAAwB,YAAA;MAEA,IAAAD,cAAA,CAAAE,MAAA;QACA;MACA;MAEA,OAAAF,cAAA,CAAAxB,MAAA,CAAAmB,KAAA,IAAAG,iBAAA,CAAApB,QAAA,CAAAiB,KAAA,CAAAQ,iBAAA;IACA;IACAC,kBAAAhF,IAAA;MACA,MAAAiF,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,YAAA,CAAAjF,IAAA;IACA;IACAkF,cAAAC,KAAA;MACA,MAAAC,KAAA;QACAC,WAAA,QAAAF,KAAA,CAAAG,KAAA;MACA;MACA,IAAAH,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAApF,IAAA;MACA;MACA,IAAAmF,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAApF,IAAA;QACAoF,KAAA,CAAAI,IAAA;MACA;MACA,IAAAL,KAAA,CAAAI,UAAA,iBAAAJ,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAK,QAAA,GAAAN,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAM,OAAA,GAAAP,KAAA,CAAAO,OAAA;MACA;MACA,OAAAN,KAAA;IACA;IACA,MAAAO,0BAAAC,WAAA;MACA,SAAAvD,qBAAA;QACA;MACA;MACA,KAAAA,qBAAA;MAEA;QACA,KAAAhC,sBAAA;QAEAwF,MAAA,CAAAC,IAAA,MAAAvG,KAAA,EAAAwG,OAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,UAAA,8BAAAD,GAAA,CAAAE,QAAA;YACA,KAAAC,OAAA,MAAA5G,KAAA,EAAAyG,GAAA;UACA;QACA;QAEA,MAAAI,oBAAA;QACA,WAAAlC,UAAA,IAAA0B,WAAA;UACA,MAAAvC,QAAA,QAAAZ,SAAA,CAAAE,IAAA,CAAAgC,CAAA,IAAAA,CAAA,CAAAhE,WAAA,KAAAuD,UAAA;UACA,KAAAb,QAAA;UAEA;YACA,MAAAgD,cAAA,cAAAjC,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAC,KAAA;gBAAA5D,WAAA,EAAAuD;cAAA;YACA;YAEA,MAAAwB,OAAA,cAAAzB,oBAAA,CAAAC,UAAA;YAEA,MAAAW,YAAA,cAAAL,yBAAA,CAAAN,UAAA;YAEAkC,oBAAA,CAAAE,IAAA;cACA,GAAAD,cAAA,CAAA/I,IAAA,CAAAA,IAAA;cACAoI,OAAA,EAAAA,OAAA;cACAb,YAAA,EAAAA;YACA;UACA,SAAAhB,KAAA;YACAC,OAAA,CAAAD,KAAA,oCAAAA,KAAA;YACA,KAAA0C,QAAA,CAAA1C,KAAA,QAAAR,QAAA,CAAAmD,aAAA;UACA;QACA;QAEA,KAAAnG,sBAAA,GAAA+F,oBAAA;QAEA,WAAAlC,UAAA,IAAA0B,WAAA;UACA,UAAAnI,IAAA,CAAAO,oBAAA,CAAAkG,UAAA;YACA,KAAAuC,IAAA,MAAAhJ,IAAA,CAAAO,oBAAA,EAAAkG,UAAA;cACAwC,sBAAA;YACA;UACA;UAEA,UAAAjJ,IAAA,CAAAS,kBAAA,CAAAgG,UAAA;YACA,KAAAuC,IAAA,MAAAhJ,IAAA,CAAAS,kBAAA,EAAAgG,UAAA;UACA;UAEA,UAAAzG,IAAA,CAAAW,mBAAA,CAAA8F,UAAA;YACA,KAAAuC,IAAA,MAAAhJ,IAAA,CAAAW,mBAAA,EAAA8F,UAAA;UACA;UAEA,MAAAyC,mBAAA,QAAAtG,sBAAA,CAAAsC,IAAA,CAAAgC,CAAA,IAAAA,CAAA,CAAAhE,WAAA,KAAAuD,UAAA;UACA,IAAAyC,mBAAA,IAAAA,mBAAA,CAAAC,MAAA;YACAD,mBAAA,CAAAC,MAAA,CAAAb,OAAA,CAAAZ,KAAA;cACA,IAAAA,KAAA,CAAA3F,QAAA;gBACA,MAAAqH,SAAA,2BAAA3C,UAAA,IAAAiB,KAAA,CAAA2B,UAAA;gBACA,KAAAL,IAAA,MAAAlH,KAAA,EAAAsH,SAAA,GACA;kBAAArH,QAAA;kBAAAC,OAAA,QAAA0F,KAAA,CAAAG,KAAA;kBAAA5F,OAAA;gBAAA,EACA;cACA;YACA;UACA;QACA;MACA,SAAAmE,KAAA;QACAC,OAAA,CAAAD,KAAA,wCAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;MACA;QACA,KAAAxB,qBAAA;MACA;IACA;IACA,MAAA0E,2BAAAnB,WAAA;MACA,SAAAtD,sBAAA;QACA;MACA;MACA,KAAAA,sBAAA;MAEA;QACA,KAAAhC,uBAAA;QAEAuF,MAAA,CAAAC,IAAA,MAAAvG,KAAA,EAAAwG,OAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,UAAA,+BAAAD,GAAA,CAAAE,QAAA;YACA,KAAAC,OAAA,MAAA5G,KAAA,EAAAyG,GAAA;UACA;QACA;QAEA,MAAAI,oBAAA;QACA,WAAAlC,UAAA,IAAA0B,WAAA;UACA,MAAAvC,QAAA,QAAAZ,SAAA,CAAAE,IAAA,CAAAgC,CAAA,IAAAA,CAAA,CAAAhE,WAAA,KAAAuD,UAAA;UACA,KAAAb,QAAA;UAEA;YACA,MAAAgD,cAAA,cAAAjC,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAC,KAAA;gBAAA5D,WAAA,EAAAuD;cAAA;YACA;YAEA,MAAAwB,OAAA,cAAAzB,oBAAA,CAAAC,UAAA;YAEAkC,oBAAA,CAAAE,IAAA;cACA,GAAAD,cAAA,CAAA/I,IAAA,CAAAA,IAAA;cACAoI,OAAA,EAAAA;YACA;UACA,SAAA7B,KAAA;YACAC,OAAA,CAAAD,KAAA,oCAAAA,KAAA;YACA,KAAA0C,QAAA,CAAA1C,KAAA,QAAAR,QAAA,CAAAmD,aAAA;UACA;QACA;QAEA,KAAAlG,uBAAA,GAAA8F,oBAAA;QAEA,WAAAlC,UAAA,IAAA0B,WAAA;UACA,UAAAnI,IAAA,CAAAQ,qBAAA,CAAAiG,UAAA;YACA,KAAAuC,IAAA,MAAAhJ,IAAA,CAAAQ,qBAAA,EAAAiG,UAAA;cACAwC,sBAAA;YACA;UACA;UAEA,UAAAjJ,IAAA,CAAAU,mBAAA,CAAA+F,UAAA;YACA,KAAAuC,IAAA,MAAAhJ,IAAA,CAAAU,mBAAA,EAAA+F,UAAA;UACA;UAEA,MAAAyC,mBAAA,QAAArG,uBAAA,CAAAqC,IAAA,CAAAgC,CAAA,IAAAA,CAAA,CAAAhE,WAAA,KAAAuD,UAAA;UACA,IAAAyC,mBAAA,IAAAA,mBAAA,CAAAC,MAAA;YACAD,mBAAA,CAAAC,MAAA,CAAAb,OAAA,CAAAZ,KAAA;cACA,IAAAA,KAAA,CAAA3F,QAAA;gBACA,MAAAqH,SAAA,4BAAA3C,UAAA,IAAAiB,KAAA,CAAA2B,UAAA;gBACA,KAAAL,IAAA,MAAAlH,KAAA,EAAAsH,SAAA,GACA;kBAAArH,QAAA;kBAAAC,OAAA,QAAA0F,KAAA,CAAAG,KAAA;kBAAA5F,OAAA;gBAAA,EACA;cACA;YACA;UACA;QACA;MACA,SAAAmE,KAAA;QACAC,OAAA,CAAAD,KAAA,yCAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;MACA;QACA,KAAAvB,sBAAA;MACA;IACA;IACA0E,SAAA;MACA,SAAAjD,uBAAA;QACA,KAAAwC,QAAA,CAAAU,OAAA;QACA;MACA;MAEA,IAAAC,gBAAA;MAEA,aAAA3J,UAAA;QACA;UACA2J,gBAAA;UACA;QACA;UACAA,gBAAA;UACA;QACA;UACAA,gBAAA;UAEA,KAAA7G,sBAAA,CAAA0F,OAAA,CAAA1C,QAAA;YACA,IAAAA,QAAA,CAAAuD,MAAA;cACAvD,QAAA,CAAAuD,MAAA,CAAAb,OAAA,CAAAZ,KAAA;gBACA,IAAAA,KAAA,CAAA3F,QAAA;kBACA,MAAAqH,SAAA,2BAAAxD,QAAA,CAAA1C,WAAA,IAAAwE,KAAA,CAAA2B,UAAA;kBACAI,gBAAA,CAAAZ,IAAA,CAAAO,SAAA;gBACA;cACA;YACA;YAEA,MAAAM,cAAA,QAAA1J,IAAA,CAAAW,mBAAA,CAAAiF,QAAA,CAAA1C,WAAA;YACAwG,cAAA,CAAApB,OAAA,CAAAqB,SAAA;cACA,MAAAC,YAAA,iCAAAhE,QAAA,CAAA1C,WAAA,IAAAyG,SAAA;cACAF,gBAAA,CAAAZ,IAAA,IAAAe,YAAA;cACAH,gBAAA,CAAAZ,IAAA,IAAAe,YAAA;cACAH,gBAAA,CAAAZ,IAAA,IAAAe,YAAA;YACA;UACA;UACA;QACA;UACAH,gBAAA;UAEA,KAAA5G,uBAAA,CAAAyF,OAAA,CAAA1C,QAAA;YACA,IAAAA,QAAA,CAAAuD,MAAA;cACAvD,QAAA,CAAAuD,MAAA,CAAAb,OAAA,CAAAZ,KAAA;gBACA,IAAAA,KAAA,CAAA3F,QAAA;kBACA,MAAAqH,SAAA,4BAAAxD,QAAA,CAAA1C,WAAA,IAAAwE,KAAA,CAAA2B,UAAA;kBACAI,gBAAA,CAAAZ,IAAA,CAAAO,SAAA;gBACA;cACA;YACA;UACA;UACA;QACA;UACAK,gBAAA;UACA;QACA;UACA;MACA;MAEA,KAAAI,cAAA,CAAAJ,gBAAA,EAAAK,KAAA;QACA,IAAAA,KAAA;UACA,KAAAhK,UAAA;QACA;MACA;IACA;IACA+J,eAAAV,MAAA,EAAA9G,QAAA;MACA,IAAA8G,MAAA,CAAA9B,MAAA;QACAhF,QAAA;QACA;MACA;MAEAgE,OAAA,CAAA0D,GAAA,UAAAZ,MAAA;MACA9C,OAAA,CAAA0D,GAAA,iBAAA/J,IAAA;MAEA,MAAAgK,kBAAA,GAAAb,MAAA,CAAA5D,GAAA,CAAAmC,KAAA;QACA,WAAAuC,OAAA,CAAAC,OAAA;UACA,KAAAC,KAAA,CAAAnK,IAAA,CAAAoK,aAAA,CAAA1C,KAAA,EAAA2C,YAAA;YACAhE,OAAA,CAAA0D,GAAA,OAAArC,KAAA,UAAA2C,YAAA;YACAH,OAAA;cAAAxC,KAAA;cAAA2C;YAAA;UACA;QACA;MACA;MAEAJ,OAAA,CAAAK,GAAA,CAAAN,kBAAA,EAAAO,IAAA,CAAAC,OAAA;QACA,MAAAC,QAAA,GAAAD,OAAA,CAAAE,IAAA,CAAAC,MAAA,IAAAA,MAAA,CAAAN,YAAA;QACAhE,OAAA,CAAA0D,GAAA,UAAAS,OAAA,YAAAC,QAAA;QACApI,QAAA,EAAAoI,QAAA;MACA;IACA;IACAG,SAAA;MACA,KAAA9K,UAAA;IACA;IACA,MAAA+K,WAAA;MACA,KAAAV,KAAA,CAAAnK,IAAA,CAAA8K,QAAA,OAAAhB,KAAA;QACA,IAAAA,KAAA;UACA;YACA,MAAAiB,QAAA;YAEA,MAAA3D,YAAA;YAEA,WAAAX,UAAA,SAAAzG,IAAA,CAAAK,eAAA;cACA,MAAA2K,YAAA,QAAAhL,IAAA,CAAAO,oBAAA,CAAAkG,UAAA;cACA,MAAAwE,WAAA,QAAAjL,IAAA,CAAAS,kBAAA,CAAAgG,UAAA;cAEAsE,QAAA,CAAAlC,IAAA;gBACAqC,YAAA;gBACAhI,WAAA,EAAAuD,UAAA;gBACA0E,WAAA,MAAAC,IAAA,GAAAC,cAAA,UAAAC,OAAA;gBACAC,iBAAA,EAAAN,WAAA,CAAA5D,MAAA,OAAA4D,WAAA,CAAAO,IAAA;gBACA,GAAAR;cACA;cAEA,SAAAhL,IAAA,CAAAa,0BAAA,CAAA4F,UAAA;gBACA2B,MAAA,CAAAqD,MAAA,MAAAzL,IAAA,CAAAa,0BAAA,CAAA4F,UAAA,GAAA6B,OAAA,CAAAoD,WAAA;kBACAtE,YAAA,CAAAyB,IAAA;oBACA3F,WAAA,EAAAuD,UAAA;oBACA,GAAAiF;kBACA;gBACA;cACA;YACA;YAEA,WAAAjF,UAAA,SAAAzG,IAAA,CAAAM,gBAAA;cACA,MAAA0K,YAAA,QAAAhL,IAAA,CAAAQ,qBAAA,CAAAiG,UAAA;cACA,MAAAwE,WAAA,QAAAjL,IAAA,CAAAU,mBAAA,CAAA+F,UAAA;cAEAsE,QAAA,CAAAlC,IAAA;gBACAqC,YAAA;gBACAhI,WAAA,EAAAuD,UAAA;gBACA0E,WAAA,MAAAC,IAAA,GAAAC,cAAA,UAAAC,OAAA;gBACAC,iBAAA,EAAAN,WAAA,CAAA5D,MAAA,OAAA4D,WAAA,CAAAO,IAAA;gBACA,GAAAR;cACA;YACA;YAEA;cAAA3K,eAAA;cAAAI,kBAAA;cAAAF,oBAAA;cAAAG,mBAAA;cAAAJ,gBAAA;cAAAE,qBAAA;cAAAG,mBAAA;cAAAC,oBAAA;cAAAC,0BAAA;cAAAC,2BAAA;cAAA,GAAA6K;YAAA,SAAA3L,IAAA;YACAqG,OAAA,CAAAuF,IAAA,CAAAvL,eAAA,EAAAI,kBAAA,EAAAF,oBAAA,EAAAG,mBAAA,EAAAJ,gBAAA,EAAAE,qBAAA,EAAAG,mBAAA,EAAAC,oBAAA,EAAAC,0BAAA,EAAAC,2BAAA;YAEA6K,UAAA,CAAAE,KAAA;YACAF,UAAA,CAAAR,WAAA,OAAAC,IAAA,GAAAC,cAAA,UAAAC,OAAA;YACAK,UAAA,CAAAG,cAAA;YACAH,UAAA,CAAAI,UAAA;YAEA,WAAApF,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAhH,IAAA,EAAA8L,UAAA;cACAZ,QAAA,EAAAA,QAAA;cACAiB,aAAA,EAAA5E;YACA;YACA,KAAA0B,QAAA,CAAAmD,OAAA;YACA,KAAAC,OAAA,CAAArD,IAAA;UACA,SAAAzC,KAAA;YACA,KAAA0C,QAAA,CAAA1C,KAAA;YACAC,OAAA,CAAAD,KAAA,0BAAAA,KAAA;UACA;QACA;MACA;IACA;IACA+F,oBAAA1F,UAAA;MACA,KAAA1D,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,WAAA,EAAAuD;MACA;MACA,KAAA3D,sBAAA;MACA,KAAAsJ,SAAA;QACA,KAAAjC,KAAA,CAAAkC,UAAA,SAAAlC,KAAA,CAAAkC,UAAA,CAAAC,aAAA;MACA;IACA;IACA,MAAAC,aAAA;MACA,KAAApC,KAAA,CAAAkC,UAAA,CAAAvB,QAAA,OAAAhB,KAAA;QACA,IAAAA,KAAA;UACA;YACA,KAAA1G,YAAA;YACA,WAAAuD,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAhH,IAAA;gBACA,QAAAkD,aAAA;gBACAG,WAAA,OAAAH,aAAA,CAAAG;cACA;YACA;YACA,KAAA4F,QAAA,CAAAmD,OAAA;YACA,KAAAnJ,sBAAA;YACA,KAAAC,aAAA;cACAC,WAAA;cACAC,UAAA;cACAC,WAAA;YACA;YACA,WAAAgF,yBAAA,MAAAlI,IAAA,CAAAK,eAAA;YACA,WAAAiJ,0BAAA,MAAAtJ,IAAA,CAAAM,gBAAA;UACA,SAAA8F,KAAA;YACA,KAAA0C,QAAA,CAAA1C,KAAA;YACAC,OAAA,CAAAD,KAAA,6BAAAA,KAAA;UACA;YACA,KAAAhD,YAAA;UACA;QACA;MACA;IACA;IACA,MAAAoJ,qBAAA;MACA;QACA,MAAA9F,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACA2F,SAAA;UACA;QACA;QACA,KAAApJ,eAAA,GAAAqD,QAAA,CAAA7G,IAAA,CAAAA,IAAA;MACA,SAAAuG,KAAA;QACAC,OAAA,CAAAD,KAAA,qCAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;MACA;IACA;IACA,MAAAsG,kBAAA;MACA;QACA,MAAAhG,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;QACA;QACA,KAAAnE,YAAA,GAAAgE,QAAA,CAAA7G,IAAA,CAAAA,IAAA;MACA,SAAAuG,KAAA;QACAC,OAAA,CAAAD,KAAA,kCAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;MACA;IACA;IACA,MAAAuG,eAAA;MACA;QACA,MAAAjG,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;QACA;QACA,KAAAlE,SAAA,GAAA+D,QAAA,CAAA7G,IAAA,CAAAA,IAAA;MACA,SAAAuG,KAAA;QACAC,OAAA,CAAAD,KAAA,+BAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;MACA;IACA;IACA,MAAAwG,gBAAA;MACA;QACA,MAAAlG,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;QACA;QACA,KAAAlC,UAAA,GAAA+B,QAAA,CAAA7G,IAAA,CAAAA,IAAA;MACA,SAAAuG,KAAA;QACAC,OAAA,CAAAD,KAAA,gCAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;MACA;IACA;IACAyG,wBAAA;MACA,KAAAT,SAAA;QACA,KAAAjC,KAAA,CAAAnK,IAAA,CAAAsM,aAAA;MACA;IACA;IACAQ,qBAAA;MACA,KAAA9M,IAAA,CAAAK,eAAA;MACA,KAAAL,IAAA,CAAAM,gBAAA;MACA,KAAAsC,sBAAA;MACA,KAAAC,uBAAA;MACA,KAAA7C,IAAA,CAAAO,oBAAA;MACA,KAAAP,IAAA,CAAAQ,qBAAA;MACA,KAAAR,IAAA,CAAAS,kBAAA;MACA,KAAAT,IAAA,CAAAU,mBAAA;MACA,KAAAV,IAAA,CAAAW,mBAAA;MACA,KAAAX,IAAA,CAAAa,0BAAA;MACA,KAAAuL,SAAA;QACA,KAAAjC,KAAA,CAAAnK,IAAA,CAAAsM,aAAA;MACA;IACA;IACAS,yBAAAtG,UAAA;MACA,MAAAiD,cAAA,QAAA1J,IAAA,CAAAW,mBAAA,CAAA8F,UAAA;MAEA,UAAAzG,IAAA,CAAAa,0BAAA,CAAA4F,UAAA;QACA,KAAAuC,IAAA,MAAAhJ,IAAA,CAAAa,0BAAA,EAAA4F,UAAA;MACA;MAEA,MAAAb,QAAA,QAAAhD,sBAAA,CAAAsC,IAAA,CAAAgC,CAAA,IAAAA,CAAA,CAAAhE,WAAA,KAAAuD,UAAA;MACA,MAAAU,cAAA,GAAAvB,QAAA,GAAAA,QAAA,CAAAwB,YAAA;MAEA,IAAAD,cAAA,CAAAE,MAAA;QACA;MACA;MAEAe,MAAA,CAAAC,IAAA,MAAAvG,KAAA,EAAAwG,OAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,UAAA,+BAAA/B,UAAA;UACA,KAAAiC,OAAA,MAAA5G,KAAA,EAAAyG,GAAA;QACA;MACA;MAEAmB,cAAA,CAAApB,OAAA,CAAAxB,KAAA;QACA,UAAA9G,IAAA,CAAAa,0BAAA,CAAA4F,UAAA,EAAAK,KAAA;UAEA,MAAAkG,WAAA,GAAA7F,cAAA,CAAAjC,IAAA,CAAAgC,CAAA,IAAAA,CAAA,CAAA+F,iBAAA,KAAAnG,KAAA;UAEA,KAAAkC,IAAA,MAAAhJ,IAAA,CAAAa,0BAAA,CAAA4F,UAAA,GAAAK,KAAA;YACAmG,iBAAA,EAAAD,WAAA,CAAAC,iBAAA;YACA3F,iBAAA,EAAA0F,WAAA,CAAA1F,iBAAA;YACA4F,0BAAA;YACAC,qBAAA;YACAC,uBAAA;UACA;QACA;QAEA,MAAAxD,YAAA,iCAAAnD,UAAA,IAAAK,KAAA;QACA,KAAAkC,IAAA,MAAAlH,KAAA,KAAA8H,YAAA,gCACA;UAAA7H,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAoL,OAAA;UAAArL,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,KAAA+G,IAAA,MAAAlH,KAAA,KAAA8H,YAAA,2BACA;UAAA7H,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAoL,OAAA;UAAArL,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,KAAA+G,IAAA,MAAAlH,KAAA,KAAA8H,YAAA,6BACA;UAAA7H,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAoL,OAAA;UAAArL,OAAA;UAAAC,OAAA;QAAA,EACA;MACA;MAEAmG,MAAA,CAAAC,IAAA,MAAArI,IAAA,CAAAa,0BAAA,CAAA4F,UAAA,GAAA6B,OAAA,CAAAxB,KAAA;QACA,KAAA4C,cAAA,CAAA7D,QAAA,CAAAiB,KAAA;UACA,KAAA4B,OAAA,MAAA1I,IAAA,CAAAa,0BAAA,CAAA4F,UAAA,GAAAK,KAAA;QACA;MACA;IACA;IACAwG,yBAAA7G,UAAA;MACA,MAAAiD,cAAA,QAAA1J,IAAA,CAAAW,mBAAA,CAAA8F,UAAA;MACA,MAAA8G,MAAA,QAAAvN,IAAA,CAAAa,0BAAA,CAAA4F,UAAA;MAEA,OAAAiD,cAAA,CAAAnE,GAAA,CAAAoE,SAAA;QACAsD,iBAAA,EAAAtD,SAAA;QACArC,iBAAA,EAAAiG,MAAA,CAAA5D,SAAA,KAAA4D,MAAA,CAAA5D,SAAA,EAAArC,iBAAA;QACA4F,0BAAA,EAAAK,MAAA,CAAA5D,SAAA,KAAA4D,MAAA,CAAA5D,SAAA,EAAAuD,0BAAA;QACAC,qBAAA,EAAAI,MAAA,CAAA5D,SAAA,KAAA4D,MAAA,CAAA5D,SAAA,EAAAwD,qBAAA;QACAC,uBAAA,EAAAG,MAAA,CAAA5D,SAAA,KAAA4D,MAAA,CAAA5D,SAAA,EAAAyD,uBAAA;MACA;IACA;IACAI,wBAAA/G,UAAA,EAAAgH,SAAA,EAAA/F,KAAA,EAAAtF,KAAA;MACA,UAAApC,IAAA,CAAAa,0BAAA,CAAA4F,UAAA;QACA,KAAAuC,IAAA,MAAAhJ,IAAA,CAAAa,0BAAA,EAAA4F,UAAA;MACA;MACA,UAAAzG,IAAA,CAAAa,0BAAA,CAAA4F,UAAA,EAAAgH,SAAA;QACA,KAAAzE,IAAA,MAAAhJ,IAAA,CAAAa,0BAAA,CAAA4F,UAAA,GAAAgH,SAAA;MACA;MACA,KAAAzE,IAAA,MAAAhJ,IAAA,CAAAa,0BAAA,CAAA4F,UAAA,EAAAgH,SAAA,GAAA/F,KAAA,EAAAtF,KAAA;IACA;IACAsL,YAAA;MACA,aAAA7L,aAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACA8L,YAAA;MACA,aAAA9L,aAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACA+L,QAAA;MACA,aAAA/L,aAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAgM,sBAAA;MACA,IAAAC,YAAA;MACA,aAAAjM,aAAA;QACA;UACAiM,YAAA,QAAAlM,cAAA;UACA;QACA;UACAkM,YAAA,QAAAlM,cAAA;UACA;QACA;UACAkM,YAAA,QAAAlM,cAAA;UACA;MACA;MAEA,IAAAkM,YAAA;QACAA,YAAA;QACA,KAAAlM,cAAA;QACA,KAAAC,aAAA;MACA;MAEA,KAAA7B,IAAA,CAAAgB,uBAAA,GAAA8M,YAAA;MACA,OAAAA,YAAA;IACA;IACAC,0BAAAC,OAAA;MACA,KAAAA,OAAA,IAAAA,OAAA;QACA,KAAApM,cAAA;QACA,KAAAC,aAAA;QACA;MACA;MAEA,IAAAmM,OAAA,YAAAA,OAAA;QACA,KAAApM,cAAA,GAAAoM,OAAA;QACA,KAAAnM,aAAA;MACA,WAAAmM,OAAA,UAAAA,OAAA;QACA,KAAApM,cAAA,GAAAoM,OAAA;QACA,KAAAnM,aAAA;MACA;QACA,KAAAD,cAAA,GAAAoM,OAAA;QACA,KAAAnM,aAAA;MACA;IACA;IACAoM,2BAAA;MACA,KAAAJ,qBAAA;IACA;IACAK,0BAAA;MACA,MAAAC,cAAA,QAAAN,qBAAA;MAEA,KAAAE,yBAAA,CAAAI,cAAA;MACA,KAAAN,qBAAA;IACA;IACAO,qBAAA;MACA,KAAA5K,cAAA;QACAC,YAAA;MACA;MACA,KAAAH,uBAAA;MACA,KAAA8I,SAAA;QACA,KAAAjC,KAAA,CAAAkE,WAAA,SAAAlE,KAAA,CAAAkE,WAAA,CAAA/B,aAAA;MACA;IACA;IACA,MAAAgC,cAAA;MACA,KAAAnE,KAAA,CAAAkE,WAAA,CAAAvD,QAAA,OAAAhB,KAAA;QACA,IAAAA,KAAA;UACA;YACA,KAAArF,aAAA;YACA,WAAAkC,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAhH,IAAA,OAAA2D;YACA;YACA,KAAAsF,QAAA,CAAAmD,OAAA;YACA,KAAA3I,uBAAA;YACA,WAAAoJ,iBAAA;YACA,KAAA1M,IAAA,CAAAC,wBAAA,QAAAuD,cAAA,CAAAC,YAAA;UACA,SAAA2C,KAAA;YACA,KAAA0C,QAAA,CAAA1C,KAAA;YACAC,OAAA,CAAAD,KAAA,8BAAAA,KAAA;UACA;YACA,KAAA3B,aAAA;UACA;QACA;MACA;IACA;IACA8J,sBAAA;MACA,KAAA7K,eAAA;QACAC,cAAA;QACAC,qBAAA;QACAC,8BAAA;QACAC,+BAAA;QACAC,wBAAA;QACAC,sCAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,eAAA;MACA;MACA,KAAAC,mBAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,gCAAA;MACA,KAAAf,wBAAA;MACA,KAAA6I,SAAA;QACA,KAAAjC,KAAA,CAAAqE,YAAA,SAAArE,KAAA,CAAAqE,YAAA,CAAAlC,aAAA;MACA;IACA;IACA,MAAAmC,2BAAAC,WAAA;MACA,KAAAhL,eAAA,CAAAO,mBAAA,GAAAyK,WAAA,CAAAlD,IAAA;MAEA,WAAAmD,4BAAA,CAAAD,WAAA;IACA;IACA,MAAAC,6BAAAxG,WAAA;MACA,IAAAA,WAAA,CAAAd,MAAA;QACA,KAAA/C,gCAAA;QACA,KAAAD,oBAAA;QACA,KAAAX,eAAA,CAAAG,8BAAA;QACA;MACA;MAEA;QACA,MAAA+K,oBAAA;QACA,WAAAnI,UAAA,IAAA0B,WAAA;UACA,MAAApC,MAAA,cAAAgB,yBAAA,CAAAN,UAAA;UACAmI,oBAAA,CAAA/F,IAAA,CAAA9C,MAAA;QACA;QAEA,MAAA8I,SAAA;QACA,MAAAC,SAAA,OAAAC,GAAA;QAEAH,oBAAA,CAAAtG,OAAA,CAAAnB,cAAA;UACAA,cAAA,CAAAmB,OAAA,CAAAxB,KAAA;YACA,KAAAgI,SAAA,CAAAE,GAAA,CAAAlI,KAAA,CAAAmG,iBAAA;cACA6B,SAAA,CAAAG,GAAA,CAAAnI,KAAA,CAAAmG,iBAAA;cACA4B,SAAA,CAAAhG,IAAA,CAAA/B,KAAA;YACA;UACA;QACA;QAEA,KAAAxC,gCAAA,GAAAuK,SAAA;QAEA,KAAAxK,oBAAA;QACA,KAAAX,eAAA,CAAAG,8BAAA;MAEA,SAAAuC,KAAA;QACAC,OAAA,CAAAD,KAAA,gDAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA;QACA,KAAA9B,gCAAA;MACA;IACA;IACA4K,+BAAAC,aAAA;MACA,KAAAzL,eAAA,CAAAG,8BAAA,GAAAmC,IAAA,CAAAoJ,SAAA,CAAAD,aAAA;IACA;IACA,MAAAE,eAAA;MACA,KAAAlF,KAAA,CAAAqE,YAAA,CAAA1D,QAAA,OAAAhB,KAAA;QACA,IAAAA,KAAA;UACA;YACA,KAAApF,cAAA;YACA,WAAAiC,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAhH,IAAA,OAAA6D;YACA;YACA,KAAAoF,QAAA,CAAAmD,OAAA;YACA,KAAA1I,wBAAA;YACA,WAAAoJ,cAAA;UACA,SAAAvG,KAAA;YACA,KAAA0C,QAAA,CAAA1C,KAAA;YACAC,OAAA,CAAAD,KAAA,gCAAAA,KAAA;UACA;YACA,KAAA1B,cAAA;UACA;QACA;MACA;IACA;EACA;EACA,MAAA4K,QAAA;IACA,WAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;IAAA;IACA,WAAA/C,iBAAA;IACA,WAAAC,cAAA;IACA,WAAAH,oBAAA;IACA,WAAAI,eAAA;IAEA,KAAAiB,qBAAA;EACA;AACA", "ignoreList": []}]}