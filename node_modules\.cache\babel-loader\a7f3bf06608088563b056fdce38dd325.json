{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js!E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\scene\\NewScene.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\scene\\NewScene.vue", "mtime": 1754034401661}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapState", "mapActions", "name", "data", "activeStep", "loading", "form", "linked_project_team_name", "linked_task_type_code", "scene_name", "scene_description", "input_platforms", "output_platforms", "input_platforms_data", "output_platforms_data", "input_data_options", "output_data_options", "input_effect_params", "output_effect_params", "input_effect_params_config", "output_effect_params_config", "updated_prompt", "scene_running_frequency", "hour", "modality", "platform_modalities", "platform_publish_forms", "day", "weeks", "stored_strategy_refresh_days", "explore_strategy_trigger_days", "scene_business_type", "baseline_data_start_days_ago", "baseline_data_exclude_recent_days", "min_baseline_sample_count", "baseline_refresh_frequency_days", "frequencyValue", "frequencyUnit", "rules", "required", "message", "trigger", "validator", "rule", "value", "callback", "Error", "type", "min", "max", "projectTeams", "taskTypes", "selectedInputPlatforms", "selectedOutputPlatforms", "addOptionDialogVisible", "newOptionForm", "option_name", "option_key", "platform_id", "optionRules", "addingOption", "modalityOptions", "addProjectDialogVisible", "addTaskTypeDialogVisible", "newProjectForm", "project_name", "newTaskTypeForm", "task_type_name", "task_type_description", "recommended_effect_param_codes", "effect_param_relationships_note", "is_content_from_external", "is_bilateral_pref_consideration_needed", "linked_platform_ids", "task_type_status", "task_type_owner", "selectedPlatformIds", "selectedEffectParams", "availableEffectParamsForTaskType", "projectRules", "taskTypeRules", "addingProject", "addingTaskType", "sceneTypes", "loadingInputPlatforms", "loadingOutputPlatforms", "tempModalitySelection", "MODALITY_TYPES", "PUBLISH_FORM_TYPES", "computed", "availablePlatforms", "platforms", "selectedTaskType", "find", "taskType", "task_type_code", "linkedPlatformIds", "split", "map", "id", "parseInt", "trim", "filter", "platform", "includes", "availableEffectParams", "params", "JSON", "parse", "Array", "isArray", "error", "console", "isPlatformConfigLoading", "methods", "getAvailablePublishForms", "platform_name", "toUpperCase", "handleModalitySelect", "platformId", "selectedModality", "$set", "push", "removeModality", "index", "indexOf", "splice", "removePublishForm", "fetchPlatformOptions", "response", "$http", "post", "api", "param", "fetchPlatformEffectParams", "getAvailableEffectParamsForPlatform", "recommendedParams", "p", "platformParams", "effectParams", "length", "effect_param_name", "getFieldComponent", "componentMap", "getFieldProps", "field", "props", "placeholder", "label", "field_type", "rows", "multiple", "options", "handleInputPlatformChange", "platformIds", "Object", "keys", "for<PERSON>ach", "key", "startsWith", "endsWith", "$delete", "platformsWithDetails", "detailResponse", "$message", "additional_Information", "platformWithDetails", "fields", "fieldProp", "field_name", "handleOutputPlatformChange", "nextStep", "warning", "fieldsToValidate", "selectedPara<PERSON>", "paramCode", "configPrefix", "validateFields", "valid", "log", "validationPromises", "Promise", "resolve", "$refs", "validateField", "errorMessage", "all", "then", "results", "<PERSON><PERSON><PERSON><PERSON>", "some", "result", "prevStep", "submitForm", "validate", "accounts", "platformData", "dataOptions", "operate_type", "create_time", "Date", "toLocaleString", "replace", "adding_data_types", "join", "values", "paramConfig", "submitData", "info", "state", "create_user_id", "company_id", "effect_params", "success", "$router", "showAddOptionDialog", "$nextTick", "optionForm", "clearValidate", "addNewOption", "fetchModalityOptions", "dict_type", "fetchProjectTeams", "fetchTaskTypes", "fetchSceneTypes", "handleProjectTeamChange", "handleTaskTypeChange", "handleEffectParamsChange", "effectParam", "effect_param_code", "configured_evaluation_days", "default_baseline_mean", "default_baseline_stddev", "pattern", "getEffectParamsTableData", "config", "updateEffectParamConfig", "paramName", "getMinValue", "getMaxValue", "getStep", "calculateTotalMinutes", "totalMinutes", "parseFrequencyFromMinutes", "minutes", "handleFrequencyValueChange", "handleFrequencyUnitChange", "currentMinutes", "showAddProjectDialog", "projectForm", "addNewProject", "showAddTaskTypeDialog", "taskTypeForm", "handlePlatformSelectChange", "selectedIds", "fetchEffectParamsForTaskType", "platformParamsArrays", "allParams", "seenCodes", "Set", "has", "add", "handleEffectParamsSelectChange", "selectedCodes", "stringify", "addNewTaskType", "created", "fetchPlatforms", "page", "pageSize"], "sources": ["src/views/scene/NewScene.vue"], "sourcesContent": ["<template>\n    <div class=\"new-scene\">\n        <el-steps :active=\"activeStep\" finish-status=\"success\" simple>\n            <el-step title=\"基本信息\" />\n            <el-step title=\"计算基准线配置\" />\n            <el-step title=\"数据输入平台\" />\n            <el-step title=\"数据输出平台\" />\n            <el-step title=\"其他设置\" />\n        </el-steps>\n\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"150px\" class=\"mt-20\" v-loading=\"loading\">\n            <div v-show=\"activeStep === 0\">\n                <el-form-item label=\"场景名称\" prop=\"scene_name\">\n                    <el-input v-model=\"form.scene_name\" placeholder=\"请输入场景名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型\" prop=\"linked_task_type_code\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_task_type_code\" placeholder=\"请选择任务类型\" style=\"flex: 1;\" @change=\"handleTaskTypeChange\">\n                            <el-option\n                                v-for=\"taskType in taskTypes\"\n                                :key=\"taskType.task_type_code\"\n                                :label=\"taskType.task_type_name\"\n                                :value=\"taskType.task_type_code\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddTaskTypeDialog\">\n                            新增任务类型\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景类型\" prop=\"scene_business_type\">\n                    <el-select \n                        v-model=\"form.scene_business_type\" \n                        placeholder=\"请选择或输入场景类型\" \n                        filterable \n                        allow-create \n                        default-first-option\n                        style=\"width: 100%\">\n                        <el-option\n                            v-for=\"sceneType in sceneTypes\"\n                            :key=\"sceneType\"\n                            :label=\"sceneType\"\n                            :value=\"sceneType\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"项目组\" prop=\"linked_project_team_name\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_project_team_name\" placeholder=\"请选择项目组\" style=\"flex: 1;\" @change=\"handleProjectTeamChange\">\n                            <el-option\n                                v-for=\"project in projectTeams\"\n                                :key=\"project.project_id\"\n                                :label=\"project.project_name\"\n                                :value=\"project.project_name\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddProjectDialog\">\n                            新增项目组\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景描述\" prop=\"scene_description\">\n                    <el-input type=\"textarea\" v-model=\"form.scene_description\" placeholder=\"请输入场景描述\"\n                        :rows=\"4\"></el-input>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 1\">\n                <el-form-item label=\"使用数据的起始天数\" prop=\"baseline_data_start_days_ago\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_start_days_ago\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线使用数据的起始天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"排除最近的数据天数\" prop=\"baseline_data_exclude_recent_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_exclude_recent_days\"\n                            :min=\"0\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线排除最近的数据天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"样本量最小阈值\" prop=\"min_baseline_sample_count\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.min_baseline_sample_count\"\n                            :min=\"1\"\n                            :max=\"10000\"\n                            :step=\"1\"\n                            placeholder=\"请输入样本量\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">个</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"一个场景在一次计算基准线时，所需要的最小样本量\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"基准线更新频率\" prop=\"baseline_refresh_frequency_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_refresh_frequency_days\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入频率\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"评估效果的基准线更新的频率\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 2\">\n                <el-form-item label=\"选择输入平台\" prop=\"input_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.input_platforms\" @change=\"handleInputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingInputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedInputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingInputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输入平台配置</h3>\n                    <el-card v-for=\"platform in selectedInputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <div class=\"field-container\">\n                                <component :is=\"getFieldComponent(field.field_type)\"\n                                    v-model=\"form.input_platforms_data[platform.platform_id][field.field_name]\"\n                                    v-bind=\"getFieldProps(field)\"></component>\n                                <div v-if=\"field.description\" class=\"field-description\">{{ field.description }}</div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据类型\" :prop=\"'input_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.input_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\"\n                                    @click=\"showAddOptionDialog(platform.platform_id)\">\n                                    新增数据类型\n                                </el-button>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"效果参数配置\">\n                            <div class=\"effect-params-container\">\n                                <div v-if=\"getAvailableEffectParamsForPlatform(platform.platform_id).length === 0\" class=\"no-effect-params-tip\">\n                                    <el-alert\n                                        title=\"该平台暂无可用的效果参数\"\n                                        type=\"info\"\n                                        :closable=\"false\"\n                                        show-icon>\n                                    </el-alert>\n                                </div>\n                                <el-checkbox-group v-else v-model=\"form.input_effect_params[platform.platform_id]\" @change=\"handleEffectParamsChange(platform.platform_id)\">\n                                    <el-checkbox v-for=\"param in getAvailableEffectParamsForPlatform(platform.platform_id)\" :key=\"param.effect_param_code\" :label=\"param.effect_param_code\" class=\"effect-param-checkbox\">\n                                        {{ param.effect_param_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                \n                                <div v-if=\"form.input_effect_params[platform.platform_id] && form.input_effect_params[platform.platform_id].length > 0\" class=\"effect-params-table\">\n                                    <h4>参数配置详情</h4>\n                                    <el-table :data=\"getEffectParamsTableData(platform.platform_id)\" border>\n                                        <el-table-column prop=\"effect_param_name\" label=\"参数名称\" width=\"120\"></el-table-column>\n                                        <el-table-column prop=\"configured_evaluation_days\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"系统会获取发布时间在T-基线范围内，且已满足各参数的Tij值的样本总数量。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *效果实现天数\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.configured_evaluation_days`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.configured_evaluation_days\" \n                                                        placeholder=\"如：3,5,10\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'configured_evaluation_days', scope.row.configured_evaluation_days)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_mean\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"μi是选取的样本量的效果数据里，该参数的平均值。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算μi，而是会使用您输入的缺省状态下的μi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *平均值\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_mean`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.default_baseline_mean\" \n                                                        placeholder=\"如：0\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_mean', scope.row.default_baseline_mean)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_stddev\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"σi是选取的样本量的效果数据里，该参数的标准差。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算σi，而是会使用您输入的缺省状态下的σi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *标准差\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_stddev`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input\n                                                        v-model=\"scope.row.default_baseline_stddev\" \n                                                        placeholder=\"如：1\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_stddev', scope.row.default_baseline_stddev)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                    </el-table>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.input_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n            </div>\n\n            <div v-show=\"activeStep === 3\">\n                <el-form-item label=\"选择输出平台\" prop=\"output_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.output_platforms\" @change=\"handleOutputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingOutputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedOutputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingOutputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输出平台配置</h3>\n                    <el-card v-for=\"platform in selectedOutputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <div class=\"field-container\">\n                                <component :is=\"getFieldComponent(field.field_type)\"\n                                    v-model=\"form.output_platforms_data[platform.platform_id][field.field_name]\"\n                                    v-bind=\"getFieldProps(field)\"></component>\n                                <div v-if=\"field.description\" class=\"field-description\">{{ field.description }}</div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据内容\" :prop=\"'output_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.output_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"多模态内容\" :prop=\"'platform_modalities.' + platform.platform_id\">\n                            <div class=\"modality-selection-container\">\n                                <div class=\"selection-row\">\n                                    <el-select\n                                        v-model=\"tempModalitySelection[platform.platform_id]\"\n                                        placeholder=\"请选择模态类型\"\n                                        style=\"width: 300px;\"\n                                        @change=\"handleModalitySelect(platform.platform_id, $event)\">\n                                        <el-option\n                                            v-for=\"modality in MODALITY_TYPES\"\n                                            :key=\"modality\"\n                                            :label=\"modality\"\n                                            :value=\"modality\"\n                                            :disabled=\"form.platform_modalities[platform.platform_id] && form.platform_modalities[platform.platform_id].includes(modality)\">\n                                        </el-option>\n                                    </el-select>\n                                    <div class=\"selected-tags\">\n                                        <el-tag\n                                            v-for=\"modality in form.platform_modalities[platform.platform_id] || []\"\n                                            :key=\"modality\"\n                                            closable\n                                            @close=\"removeModality(platform.platform_id, modality)\"\n                                            style=\"margin-right: 8px; margin-bottom: 8px;\">\n                                            {{ modality }}\n                                        </el-tag>\n                                    </div>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"发布形态\" :prop=\"'platform_publish_forms.' + platform.platform_id\">\n                            <div class=\"publish-form-selection-container\">\n                                <div class=\"selection-row\">\n                                    <el-select\n                                        v-model=\"form.platform_publish_forms[platform.platform_id]\"\n                                        placeholder=\"请选择发布形态\"\n                                        style=\"width: 300px;\">\n                                        <el-option\n                                            v-for=\"publishForm in getAvailablePublishForms(platform)\"\n                                            :key=\"publishForm\"\n                                            :label=\"publishForm\"\n                                            :value=\"publishForm\">\n                                        </el-option>\n                                    </el-select>\n                                    <div class=\"selected-tags\">\n                                        <el-tag\n                                            v-if=\"form.platform_publish_forms[platform.platform_id]\"\n                                            closable\n                                            @close=\"removePublishForm(platform.platform_id)\"\n                                            style=\"margin-left: 8px;\">\n                                            {{ form.platform_publish_forms[platform.platform_id] }}\n                                        </el-tag>\n                                    </div>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.output_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n\n                <el-form-item label=\"模态\" prop=\"modality\">\n                    <el-select v-model=\"form.modality\" placeholder=\"请选择模态\">\n                        <el-option\n                            v-for=\"item in modalityOptions\"\n                            :key=\"item.dict_name\"\n                            :label=\"item.dict_name\"\n                            :value=\"item.dict_name\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 4\">\n                <el-form-item label=\"运行频率\" prop=\"scene_running_frequency\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-input-number\n                            v-model=\"frequencyValue\"\n                            :min=\"getMinValue()\"\n                            :max=\"getMaxValue()\"\n                            :step=\"getStep()\"\n                            placeholder=\"请输入数值\"\n                            style=\"width: 150px\"\n                            @change=\"handleFrequencyValueChange\">\n                        </el-input-number>\n                        <el-select \n                            v-model=\"frequencyUnit\" \n                            placeholder=\"请选择单位\"\n                            style=\"width: 120px\"\n                            @change=\"handleFrequencyUnitChange\">\n                            <el-option label=\"分钟\" value=\"minutes\"></el-option>\n                            <el-option label=\"小时\" value=\"hours\"></el-option>\n                            <el-option label=\"天\" value=\"days\"></el-option>\n                        </el-select>\n                        <span style=\"color: #909399; font-size: 12px;\">\n                            (最小间隔30分钟)\n                        </span>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"个性化进化更新频率\" prop=\"stored_strategy_refresh_days\">\n                    <el-input-number\n                        v-model=\"form.stored_strategy_refresh_days\"\n                        :min=\"0\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">建议您设为0</span>\n                </el-form-item>\n                <el-form-item label=\"AI自行探索频率\" width=\"400px\" prop=\"explore_strategy_trigger_days\">\n                    <el-input-number\n                        v-model=\"form.explore_strategy_trigger_days\"\n                        :min=\"1\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">目前我们的探索模式暂未上线，建议您先将Z值设为365天</span>\n                </el-form-item>\n                <el-form-item label=\"AI提示词\" prop=\"updated_prompt\">\n                    <el-input type=\"textarea\" v-model=\"form.updated_prompt\" placeholder=\"请输入AI提示词\" :rows=\"10\"></el-input>\n                </el-form-item>\n            </div>\n\n            <el-form-item class=\"navigation-buttons\">\n                <el-button v-if=\"activeStep > 0\" @click=\"prevStep\">上一步</el-button>\n                <el-button \n                    v-if=\"activeStep < 4\" \n                    type=\"primary\" \n                    @click=\"nextStep\"\n                    :disabled=\"isPlatformConfigLoading\"\n                    :loading=\"isPlatformConfigLoading\">\n                    {{ isPlatformConfigLoading ? '配置加载中...' : '下一步' }}\n                </el-button>\n                <el-button v-if=\"activeStep === 4\" type=\"primary\" @click=\"submitForm\">提交</el-button>\n            </el-form-item>\n        </el-form>\n\n        <el-dialog title=\"新增数据内容\" :visible.sync=\"addOptionDialogVisible\" width=\"500px\">\n            <el-form ref=\"optionForm\" :model=\"newOptionForm\" :rules=\"optionRules\" label-width=\"120px\">\n                <el-form-item label=\"内容名称\" prop=\"option_name\">\n                    <el-input v-model=\"newOptionForm.option_name\" placeholder=\"请输入内容名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"内容标识\" prop=\"option_key\">\n                    <el-input v-model=\"newOptionForm.option_key\" placeholder=\"请输入内容标识（英文）\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addOptionDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewOption\" :loading=\"addingOption\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增项目组\" :visible.sync=\"addProjectDialogVisible\" width=\"500px\">\n            <el-form ref=\"projectForm\" :model=\"newProjectForm\" :rules=\"projectRules\" label-width=\"120px\">\n                <el-form-item label=\"项目名称\" prop=\"project_name\">\n                    <el-input v-model=\"newProjectForm.project_name\" placeholder=\"请输入项目名称\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addProjectDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewProject\" :loading=\"addingProject\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增任务类型\" :visible.sync=\"addTaskTypeDialogVisible\" width=\"600px\">\n            <el-form ref=\"taskTypeForm\" :model=\"newTaskTypeForm\" :rules=\"taskTypeRules\" label-width=\"140px\">\n                <el-form-item label=\"任务类型名称\" prop=\"task_type_name\">\n                    <el-input v-model=\"newTaskTypeForm.task_type_name\" placeholder=\"请输入任务类型名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型描述\" prop=\"task_type_description\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.task_type_description\" placeholder=\"请输入任务类型描述\" :rows=\"3\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"关联平台\" prop=\"linked_platform_ids\">\n                    <el-select \n                        v-model=\"selectedPlatformIds\" \n                        multiple \n                        placeholder=\"请选择关联平台\"\n                        style=\"width: 100%\"\n                        @change=\"handlePlatformSelectChange\">\n                        <el-option\n                            v-for=\"platform in platforms\"\n                            :key=\"platform.platform_id\"\n                            :label=\"platform.platform_name\"\n                            :value=\"platform.platform_id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"推荐效果参数\" prop=\"recommended_effect_param_codes\">\n                    <el-select \n                        v-model=\"selectedEffectParams\" \n                        multiple \n                        placeholder=\"请选择推荐效果参数\"\n                        style=\"width: 100%\"\n                        @change=\"handleEffectParamsSelectChange\">\n                        <el-option\n                            v-for=\"param in availableEffectParamsForTaskType\"\n                            :key=\"param.effect_param_code\"\n                            :label=\"`${param.effect_param_name} (${param.effect_param_code})`\"\n                            :value=\"param.effect_param_name\">\n                        </el-option>\n                    </el-select>\n                    <div v-if=\"availableEffectParamsForTaskType.length === 0 && selectedPlatformIds.length > 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        所选平台暂无可用的效果参数\n                    </div>\n                    <div v-if=\"selectedPlatformIds.length === 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        请先选择关联平台\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"参数关系说明\" prop=\"effect_param_relationships_note\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.effect_param_relationships_note\" placeholder=\"请输入各推荐参数之间的逻辑关系说明\" :rows=\"3\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addTaskTypeDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewTaskType\" :loading=\"addingTaskType\">确定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\n\nexport default {\n    name: 'NewScene',\n    data() {\n        return {\n            activeStep: 0,\n            loading: false,\n            form: {\n                linked_project_team_name: null,\n                linked_task_type_code: null,\n                scene_name: '',\n                scene_description: '',\n                input_platforms: [],\n                output_platforms: [],\n                input_platforms_data: {},\n                output_platforms_data: {},\n                input_data_options: {},\n                output_data_options: {},\n                input_effect_params: {},\n                output_effect_params: {},\n                input_effect_params_config: {},\n                output_effect_params_config: {},\n                updated_prompt: '',\n                scene_running_frequency: '',\n                hour: '',\n                modality: '',\n                platform_modalities: {}, // 新增：平台多模态配置\n                platform_publish_forms: {}, // 新增：平台发布形态配置\n                day: '',\n                weeks: '',\n                stored_strategy_refresh_days: 0,\n                explore_strategy_trigger_days: 365,\n                scene_business_type: '',\n                baseline_data_start_days_ago: 30,\n                baseline_data_exclude_recent_days: 3,\n                min_baseline_sample_count: 3,\n                baseline_refresh_frequency_days: 7\n            },\n            frequencyValue: 30,\n            frequencyUnit: 'minutes',\n            rules: {\n                linked_project_team_name: [\n                    { \n                        required: true, \n                        message: '请选择项目组', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            if (!value) {\n                                callback(new Error('请选择项目组'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                linked_task_type_code: [\n                    { \n                        required: true, \n                        message: '请选择任务类型', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            if (!value) {\n                                callback(new Error('请选择任务类型'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                scene_name: [\n                    { required: true, message: '请输入场景名称', trigger: 'blur' }\n                ],\n                scene_description: [\n                    { required: false, message: '请输入场景描述', trigger: 'blur' }\n                ],\n                input_platforms: [\n                    { required: true, message: '请选择输入平台', trigger: 'change' }\n                ],\n                output_platforms: [\n                    { required: true, message: '请选择输出平台', trigger: 'change' }\n                ],\n                updated_prompt: [\n                    { required: true, message: '请输入AI提示词', trigger: 'blur' }\n                ],\n                scene_running_frequency: [\n                    { required: true, message: '请设置运行频率', trigger: 'change' },\n                    { \n                        type: 'number', \n                        min: 30, \n                        message: '运行频率最小间隔为30分钟', \n                        trigger: 'change' \n                    }\n                ],\n                stored_strategy_refresh_days: [\n                    { required: true, message: '请输入个性化进化策略更新频率', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-365之间的天数', trigger: 'blur' }\n                ],\n                explore_strategy_trigger_days: [\n                    { required: true, message: '请输入AI自行探索频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                scene_business_type: [\n                    { required: true, message: '请选择或输入场景类型', trigger: 'change' }\n                ],\n                baseline_data_start_days_ago: [\n                    { required: true, message: '请输入使用数据的起始天数', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                baseline_data_exclude_recent_days: [\n                    { required: true, message: '请输入排除最近的数据天数', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-30之间的天数', trigger: 'blur' }\n                ],\n                min_baseline_sample_count: [\n                    { required: true, message: '请输入样本量最小阈值', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 10000, message: '请输入1-10000之间的数值', trigger: 'blur' }\n                ],\n                baseline_refresh_frequency_days: [\n                    { required: true, message: '请输入基准线更新频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ]\n            },\n            projectTeams: [],\n            taskTypes: [],\n            selectedInputPlatforms: [],\n            selectedOutputPlatforms: [],\n            addOptionDialogVisible: false,\n            newOptionForm: {\n                option_name: '',\n                option_key: '',\n                platform_id: ''\n            },\n            optionRules: {\n                option_name: [\n                    { required: true, message: '请输入内容名称', trigger: 'blur' }\n                ],\n                option_key: [\n                    { required: true, message: '请输入内容标识（英文）', trigger: 'blur' }\n                ]\n            },\n            addingOption: false,\n            modalityOptions: [],\n            addProjectDialogVisible: false,\n            addTaskTypeDialogVisible: false,\n            newProjectForm: {\n                project_name: ''\n            },\n            newTaskTypeForm: {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            },\n            selectedPlatformIds: [],\n            selectedEffectParams: [],\n            availableEffectParamsForTaskType: [],\n            projectRules: {\n                project_name: [\n                    { required: true, message: '请输入项目名称', trigger: 'blur' }\n                ]\n            },\n            taskTypeRules: {\n                task_type_name: [\n                    { required: true, message: '请输入任务类型名称', trigger: 'blur' }\n                ],\n                task_type_description: [\n                    { required: true, message: '请输入任务类型描述', trigger: 'blur' }\n                ],\n                recommended_effect_param_codes: [\n                    { required: true, message: '请输入推荐效果参数编码', trigger: 'blur' }\n                ],\n                effect_param_relationships_note: [\n                    { required: true, message: '请输入各推荐参数之间的逻辑关系说明', trigger: 'blur' }\n                ]\n            },\n            addingProject: false,\n            addingTaskType: false,\n            sceneTypes: [],\n            loadingInputPlatforms: false,\n            loadingOutputPlatforms: false,\n            tempModalitySelection: {}, // 临时存储模态选择\n            // 模态类型常量\n            MODALITY_TYPES: [\n                '数字人视频',\n                '卡通视频',\n                '语音',\n                '游戏',\n                '图文结合',\n                '图文排版',\n                '播客视频'\n            ],\n            // 发布形态类型常量\n            PUBLISH_FORM_TYPES: [\n                'PPT',\n                'Word文档',\n                'PDF',\n                'Excel表格',\n                '文本',\n                '作业练习题',\n                '评测考试题目',\n                '模态原生态展示'\n            ]\n        }\n    },\n    computed: {\n        ...mapState(['platforms']),\n        availablePlatforms() {\n            if (!this.form.linked_task_type_code) {\n                return this.platforms\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.linked_platform_ids) {\n                return this.platforms\n            }\n            \n            const linkedPlatformIds = selectedTaskType.linked_platform_ids.split(',').map(id => parseInt(id.trim()))\n            return this.platforms.filter(platform => linkedPlatformIds.includes(platform.platform_id))\n        },\n        availableEffectParams() {\n            if (!this.form.linked_task_type_code) {\n                return []\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.recommended_effect_param_codes) {\n                return []\n            }\n            \n            try {\n                const params = JSON.parse(selectedTaskType.recommended_effect_param_codes)\n                return Array.isArray(params) ? params : []\n            } catch (error) {\n                console.error('解析效果参数失败:', error)\n                return []\n            }\n        },\n        isPlatformConfigLoading() {\n            if (this.activeStep === 2 && this.loadingInputPlatforms) {\n                return true\n            }\n            if (this.activeStep === 3 && this.loadingOutputPlatforms) {\n                return true\n            }\n            return false\n        }\n    },\n    methods: {\n        ...mapActions(['fetchPlatforms']),\n        // 获取平台可用的发布形态\n        getAvailablePublishForms(platform) {\n            // ECL平台可以选择所有类型\n            if (platform.platform_name && platform.platform_name.toUpperCase().includes('ECL')) {\n                return this.PUBLISH_FORM_TYPES\n            }\n            // 其他平台根据具体需求返回特定类型，这里暂时返回所有类型\n            // 后续可以根据平台类型进行更精细的控制\n            return this.PUBLISH_FORM_TYPES\n        },\n        // 处理模态选择\n        handleModalitySelect(platformId, selectedModality) {\n            if (!selectedModality) return\n\n            if (!this.form.platform_modalities[platformId]) {\n                this.$set(this.form.platform_modalities, platformId, [])\n            }\n\n            if (!this.form.platform_modalities[platformId].includes(selectedModality)) {\n                this.form.platform_modalities[platformId].push(selectedModality)\n            }\n\n            // 清空临时选择\n            this.$set(this.tempModalitySelection, platformId, '')\n        },\n        // 移除模态\n        removeModality(platformId, modality) {\n            if (this.form.platform_modalities[platformId]) {\n                const index = this.form.platform_modalities[platformId].indexOf(modality)\n                if (index > -1) {\n                    this.form.platform_modalities[platformId].splice(index, 1)\n                }\n            }\n        },\n        // 移除发布形态\n        removePublishForm(platformId) {\n            this.$set(this.form.platform_publish_forms, platformId, '')\n        },\n        async fetchPlatformOptions(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/option/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform options:', error)\n                return []\n            }\n        },\n        async fetchPlatformEffectParams(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/effectParamCategory/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform effect params:', error)\n                return []\n            }\n        },\n        getAvailableEffectParamsForPlatform(platformId) {\n            const recommendedParams = this.availableEffectParams || []\n            \n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n            \n            if (platformParams.length === 0) {\n                return []\n            }\n            \n            return platformParams.filter(param => recommendedParams.includes(param.effect_param_name))\n        },\n        getFieldComponent(type) {\n            const componentMap = {\n                'string': 'el-input',\n                'password': 'el-input',\n                'select': 'el-select',\n                'multiselect': 'el-select',\n                'number': 'el-input-number',\n                'bool': 'el-switch',\n                'textarea': 'el-input'\n            }\n            return componentMap[type] || 'el-input'\n        },\n        getFieldProps(field) {\n            const props = {\n                placeholder: `请输入${field.label}`\n            }\n            if (field.field_type === 'password') {\n                props.type = 'password'\n            }\n            if (field.field_type === 'textarea') {\n                props.type = 'textarea'\n                props.rows = 3\n            }\n            if (field.field_type === 'select' || field.field_type === 'multiselect') {\n                props.multiple = field.field_type === 'multiselect'\n                props.options = field.options || []\n            }\n            return props\n        },\n        async handleInputPlatformChange(platformIds) {\n            if (this.loadingInputPlatforms) {\n                return\n            }\n            this.loadingInputPlatforms = true\n            \n            try {\n                this.selectedInputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('input_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n                        \n                        const effectParams = await this.fetchPlatformEffectParams(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options,\n                            effectParams: effectParams\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedInputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n                    if (!this.form.input_platforms_data[platformId]) {\n                        this.$set(this.form.input_platforms_data, platformId, {\n                            additional_Information: ''\n                        })\n                    }\n\n                    if (!this.form.input_data_options[platformId]) {\n                        this.$set(this.form.input_data_options, platformId, [])\n                    }\n\n                    if (!this.form.input_effect_params[platformId]) {\n                        this.$set(this.form.input_effect_params, platformId, [])\n                    }\n\n                    const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n                    if (platformWithDetails && platformWithDetails.fields) {\n                        platformWithDetails.fields.forEach(field => {\n                            if (field.required) {\n                                const fieldProp = `input_platforms_data.${platformId}.${field.field_name}`\n                                this.$set(this.rules, fieldProp, [\n                                    { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                ])\n                            }\n                        })\n                    }\n                }\n            } catch (error) {\n                console.error('Error in handleInputPlatformChange:', error)\n                this.$message.error('处理输入平台变化时出错')\n            } finally {\n                this.loadingInputPlatforms = false\n            }\n        },\n        async handleOutputPlatformChange(platformIds) {\n            if (this.loadingOutputPlatforms) {\n                return\n            }\n            this.loadingOutputPlatforms = true\n            \n            try {\n                this.selectedOutputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('output_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedOutputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n                    if (!this.form.output_platforms_data[platformId]) {\n                        this.$set(this.form.output_platforms_data, platformId, {\n                            additional_Information: ''\n                        })\n                    }\n\n                    if (!this.form.output_data_options[platformId]) {\n                        this.$set(this.form.output_data_options, platformId, [])\n                    }\n\n                    // 初始化新的模态和发布形态字段\n                    if (!this.form.platform_modalities[platformId]) {\n                        this.$set(this.form.platform_modalities, platformId, [])\n                    }\n\n                    if (!this.form.platform_publish_forms[platformId]) {\n                        this.$set(this.form.platform_publish_forms, platformId, '')\n                    }\n\n                    const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)\n                    if (platformWithDetails && platformWithDetails.fields) {\n                        platformWithDetails.fields.forEach(field => {\n                            if (field.required) {\n                                const fieldProp = `output_platforms_data.${platformId}.${field.field_name}`\n                                this.$set(this.rules, fieldProp, [\n                                    { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                ])\n                            }\n                        })\n                    }\n                }\n            } catch (error) {\n                console.error('Error in handleOutputPlatformChange:', error)\n                this.$message.error('处理输出平台变化时出错')\n            } finally {\n                this.loadingOutputPlatforms = false\n            }\n        },\n        nextStep() {\n            if (this.isPlatformConfigLoading) {\n                this.$message.warning('平台配置正在加载中，请稍候...')\n                return\n            }\n\n            let fieldsToValidate = []\n\n            switch (this.activeStep) {\n                case 0:\n                    fieldsToValidate = ['linked_project_team_name', 'linked_task_type_code', 'scene_business_type', 'scene_name', 'scene_description']\n                    break\n                case 1:\n                    fieldsToValidate = ['baseline_data_start_days_ago', 'baseline_data_exclude_recent_days', 'min_baseline_sample_count', 'baseline_refresh_frequency_days']\n                    break\n                case 2:\n                    fieldsToValidate = ['input_platforms']\n                    \n                    this.selectedInputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `input_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n                        \n                        const selectedParams = this.form.input_effect_params[platform.platform_id] || []\n                        selectedParams.forEach(paramCode => {\n                            const configPrefix = `input_effect_params_config.${platform.platform_id}.${paramCode}`\n                            fieldsToValidate.push(`${configPrefix}.configured_evaluation_days`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_mean`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_stddev`)\n                        })\n                    })\n                    break\n                case 3:\n                    fieldsToValidate = ['output_platforms']\n\n                    this.selectedOutputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `output_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n\n                        // 验证模态选择\n                        if (!this.form.platform_modalities[platform.platform_id] ||\n                            this.form.platform_modalities[platform.platform_id].length === 0) {\n                            this.$message.error(`请为平台\"${platform.platform_name}\"选择至少一种模态类型`)\n                            return false\n                        }\n\n                        // 验证发布形态选择\n                        if (!this.form.platform_publish_forms[platform.platform_id]) {\n                            this.$message.error(`请为平台\"${platform.platform_name}\"选择发布形态`)\n                            return false\n                        }\n                    })\n                    break\n                case 4:\n                    fieldsToValidate = ['updated_prompt', 'scene_running_frequency', 'stored_strategy_refresh_days', 'explore_strategy_trigger_days']\n                    break\n                default:\n                    break\n            }\n\n            this.validateFields(fieldsToValidate, (valid) => {\n                if (valid) {\n                    this.activeStep++\n                }\n            })\n        },\n        validateFields(fields, callback) {\n            if (fields.length === 0) {\n                callback(true)\n                return\n            }\n\n            console.log('验证字段:', fields)\n            console.log('当前表单数据:', this.form)\n\n            const validationPromises = fields.map(field => {\n                return new Promise((resolve) => {\n                    this.$refs.form.validateField(field, (errorMessage) => {\n                        console.log(`字段 ${field} 验证结果:`, errorMessage)\n                        resolve({ field, errorMessage })\n                    })\n                })\n            })\n\n            Promise.all(validationPromises).then(results => {\n                const hasError = results.some(result => result.errorMessage)\n                console.log('验证结果:', results, '是否有错误:', hasError)\n                callback(!hasError)\n            })\n        },\n        prevStep() {\n            this.activeStep--\n        },\n        async submitForm() {\n            this.$refs.form.validate(async valid => {\n                if (valid) {\n                    try {\n                        const accounts = []\n\n                        const effectParams = []\n\n                        for (const platformId of this.form.input_platforms) {\n                            const platformData = this.form.input_platforms_data[platformId] || {}\n                            const dataOptions = this.form.input_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 1,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n\n                            if (this.form.input_effect_params_config[platformId]) {\n                                Object.values(this.form.input_effect_params_config[platformId]).forEach(paramConfig => {\n                                    effectParams.push({\n                                        platform_id: platformId,\n                                        ...paramConfig\n                                    })\n                                })\n                            }\n                        }\n\n                        for (const platformId of this.form.output_platforms) {\n                            const platformData = this.form.output_platforms_data[platformId] || {}\n                            const dataOptions = this.form.output_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 2,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n                        }\n\n                        const { input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, platform_modalities, platform_publish_forms, ...submitData } = this.form\n                        console.info(input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, platform_modalities, platform_publish_forms)\n\n                        submitData.state = 2;\n                        submitData.create_time = new Date().toLocaleString('sv-SE').replace('T', ' ');\n                        submitData.create_user_id = 0;\n                        submitData.company_id = 0;\n\n                        await this.$http.post('', {\n                            api: '/api/scene/add',\n                            data: submitData,\n                            accounts: accounts,\n                            effect_params: effectParams,\n                            platform_modalities: platform_modalities,\n                            platform_publish_forms: platform_publish_forms\n                        })\n                        this.$message.success('场景创建成功')\n                        this.$router.push('/')\n                    } catch (error) {\n                        this.$message.error('场景创建失败')\n                        console.error('Error creating scene:', error)\n                    }\n                }\n            })\n        },\n        showAddOptionDialog(platformId) {\n            this.newOptionForm = {\n                option_name: '',\n                option_key: '',\n                platform_id: platformId\n            }\n            this.addOptionDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.optionForm && this.$refs.optionForm.clearValidate()\n            })\n        },\n        async addNewOption() {\n            this.$refs.optionForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingOption = true\n                        await this.$http.post('', {\n                            api: '/api/option/add',\n                            data: {\n                                ...this.newOptionForm,\n                                platform_id: this.newOptionForm.platform_id\n                            }\n                        })\n                        this.$message.success('新增数据类型成功')\n                        this.addOptionDialogVisible = false\n                        this.newOptionForm = {\n                            option_name: '',\n                            option_key: '',\n                            platform_id: ''\n                        }\n                        await this.handleInputPlatformChange(this.form.input_platforms)\n                        await this.handleOutputPlatformChange(this.form.output_platforms)\n                    } catch (error) {\n                        this.$message.error('新增数据类型失败')\n                        console.error('Error adding new option:', error)\n                    } finally {\n                        this.addingOption = false\n                    }\n                }\n            })\n        },\n        async fetchModalityOptions() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'modality'\n                    }\n                })\n                this.modalityOptions = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching modality options:', error)\n                this.$message.error('获取模态列表失败')\n            }\n        },\n        async fetchProjectTeams() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/projectTeam/getList'\n                })\n                this.projectTeams = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching project teams:', error)\n                this.$message.error('获取项目组列表失败')\n            }\n        },\n        async fetchTaskTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/taskType/getList'\n                })\n                this.taskTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching task types:', error)\n                this.$message.error('获取任务类型列表失败')\n            }\n        },\n        async fetchSceneTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/sceneType/getList'\n                })\n                this.sceneTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching scene types:', error)\n                this.$message.error('获取场景类型列表失败')\n            }\n        },\n        handleProjectTeamChange() {\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_project_team_name')\n            })\n        },\n        handleTaskTypeChange() {\n            this.form.input_platforms = []\n            this.form.output_platforms = []\n            this.selectedInputPlatforms = []\n            this.selectedOutputPlatforms = []\n            this.form.input_platforms_data = {}\n            this.form.output_platforms_data = {}\n            this.form.input_data_options = {}\n            this.form.output_data_options = {}\n            this.form.input_effect_params = {}\n            this.form.input_effect_params_config = {}\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_task_type_code')\n            })\n        },\n        handleEffectParamsChange(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n\n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n\n            if (platformParams.length == 0) {\n                return;\n            }\n\n            Object.keys(this.rules).forEach(key => {\n                if (key.startsWith(`input_effect_params_config.${platformId}.`)) {\n                    this.$delete(this.rules, key)\n                }\n            })\n\n            selectedParams.forEach(param => {\n                if (!this.form.input_effect_params_config[platformId][param]) {\n\n                    const effectParam = platformParams.find(p => p.effect_param_code === param);\n\n                    this.$set(this.form.input_effect_params_config[platformId], param, {\n                        effect_param_code: effectParam.effect_param_code,\n                        effect_param_name: effectParam.effect_param_name,\n                        configured_evaluation_days: '',\n                        default_baseline_mean: '',\n                        default_baseline_stddev: ''\n                    })\n                }\n\n                const configPrefix = `input_effect_params_config.${platformId}.${param}`\n                this.$set(this.rules, `${configPrefix}.configured_evaluation_days`, [\n                    { required: true, message: '请输入效果实现天数', trigger: 'blur' },\n                    { pattern: /^[\\d,\\s]+$/, message: '请输入有效的天数格式，如：3,5,10', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_mean`, [\n                    { required: true, message: '请输入平均值', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '平均值必须是数字', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_stddev`, [\n                    { required: true, message: '请输入标准差', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '标准差必须是数字', trigger: 'blur' }\n                ])\n            })\n            \n            Object.keys(this.form.input_effect_params_config[platformId]).forEach(param => {\n                if (!selectedParams.includes(param)) {\n                    this.$delete(this.form.input_effect_params_config[platformId], param)\n                }\n            })\n        },\n        getEffectParamsTableData(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n            const config = this.form.input_effect_params_config[platformId] || {}\n            \n            return selectedParams.map(paramCode => ({\n                effect_param_code: paramCode,\n                effect_param_name: (config[paramCode] && config[paramCode].effect_param_name) || '',\n                configured_evaluation_days: (config[paramCode] && config[paramCode].configured_evaluation_days) || '',\n                default_baseline_mean: (config[paramCode] && config[paramCode].default_baseline_mean) || '',\n                default_baseline_stddev: (config[paramCode] && config[paramCode].default_baseline_stddev) || ''\n            }))\n        },\n        updateEffectParamConfig(platformId, paramName, field, value) {\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n            if (!this.form.input_effect_params_config[platformId][paramName]) {\n                this.$set(this.form.input_effect_params_config[platformId], paramName, {})\n            }\n            this.$set(this.form.input_effect_params_config[platformId][paramName], field, value)\n        },\n        getMinValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30\n                case 'hours':\n                    return 1\n                case 'days':\n                    return 1\n                default:\n                    return 1\n            }\n        },\n        getMaxValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 1440\n                case 'hours':\n                    return 24\n                case 'days':\n                    return 365\n                default:\n                    return 1\n            }\n        },\n        getStep() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30\n                case 'hours':\n                    return 1\n                case 'days':\n                    return 1\n                default:\n                    return 1\n            }\n        },\n        calculateTotalMinutes() {\n            let totalMinutes = 0\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    totalMinutes = this.frequencyValue\n                    break\n                case 'hours':\n                    totalMinutes = this.frequencyValue * 60\n                    break\n                case 'days':\n                    totalMinutes = this.frequencyValue * 24 * 60\n                    break\n            }\n            \n            if (totalMinutes < 30) {\n                totalMinutes = 30\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n            }\n            \n            this.form.scene_running_frequency = totalMinutes\n            return totalMinutes\n        },\n        parseFrequencyFromMinutes(minutes) {\n            if (!minutes || minutes < 30) {\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n                return\n            }\n            \n            if (minutes >= 1440 && minutes % 1440 === 0) {\n                this.frequencyValue = minutes / 1440\n                this.frequencyUnit = 'days'\n            } else if (minutes >= 60 && minutes % 60 === 0) {\n                this.frequencyValue = minutes / 60\n                this.frequencyUnit = 'hours'\n            } else {\n                this.frequencyValue = minutes\n                this.frequencyUnit = 'minutes'\n            }\n        },\n        handleFrequencyValueChange() {\n            this.calculateTotalMinutes()\n        },\n        handleFrequencyUnitChange() {\n            const currentMinutes = this.calculateTotalMinutes()\n            \n            this.parseFrequencyFromMinutes(currentMinutes)\n            this.calculateTotalMinutes()\n        },\n        showAddProjectDialog() {\n            this.newProjectForm = {\n                project_name: ''\n            }\n            this.addProjectDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.projectForm && this.$refs.projectForm.clearValidate()\n            })\n        },\n        async addNewProject() {\n            this.$refs.projectForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingProject = true\n                        await this.$http.post('', {\n                            api: '/api/projectTeam/add',\n                            data: this.newProjectForm\n                        })\n                        this.$message.success('新增项目组成功')\n                        this.addProjectDialogVisible = false\n                        await this.fetchProjectTeams()\n                        this.form.linked_project_team_name = this.newProjectForm.project_name\n                    } catch (error) {\n                        this.$message.error('新增项目组失败')\n                        console.error('Error adding new project:', error)\n                    } finally {\n                        this.addingProject = false\n                    }\n                }\n            })\n        },\n        showAddTaskTypeDialog() {\n            this.newTaskTypeForm = {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            }\n            this.selectedPlatformIds = []\n            this.selectedEffectParams = []\n            this.availableEffectParamsForTaskType = []\n            this.addTaskTypeDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.taskTypeForm && this.$refs.taskTypeForm.clearValidate()\n            })\n        },\n        async handlePlatformSelectChange(selectedIds) {\n            this.newTaskTypeForm.linked_platform_ids = selectedIds.join(',')\n            \n            await this.fetchEffectParamsForTaskType(selectedIds)\n        },\n        async fetchEffectParamsForTaskType(platformIds) {\n            if (platformIds.length === 0) {\n                this.availableEffectParamsForTaskType = []\n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                return\n            }\n            \n            try {\n                const platformParamsArrays = []\n                for (const platformId of platformIds) {\n                    const params = await this.fetchPlatformEffectParams(platformId)\n                    platformParamsArrays.push(params)\n                }\n                \n                const allParams = []\n                const seenCodes = new Set()\n                \n                platformParamsArrays.forEach(platformParams => {\n                    platformParams.forEach(param => {\n                        if (!seenCodes.has(param.effect_param_code)) {\n                            seenCodes.add(param.effect_param_code)\n                            allParams.push(param)\n                        }\n                    })\n                })\n                \n                this.availableEffectParamsForTaskType = allParams\n                \n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                \n            } catch (error) {\n                console.error('Error fetching effect params for task type:', error)\n                this.$message.error('获取效果参数失败')\n                this.availableEffectParamsForTaskType = []\n            }\n        },\n        handleEffectParamsSelectChange(selectedCodes) {\n            this.newTaskTypeForm.recommended_effect_param_codes = JSON.stringify(selectedCodes)\n        },\n        async addNewTaskType() {\n            this.$refs.taskTypeForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingTaskType = true\n                        await this.$http.post('', {\n                            api: '/api/taskType/add',\n                            data: this.newTaskTypeForm\n                        })\n                        this.$message.success('新增任务类型成功')\n                        this.addTaskTypeDialogVisible = false\n                        await this.fetchTaskTypes()\n                    } catch (error) {\n                        this.$message.error('新增任务类型失败')\n                        console.error('Error adding new task type:', error)\n                    } finally {\n                        this.addingTaskType = false\n                    }\n                }\n            })\n        }\n    },\n    async created() {\n        await this.fetchPlatforms({ page: 1, pageSize: 100 })\n        await this.fetchProjectTeams()\n        await this.fetchTaskTypes()\n        await this.fetchModalityOptions()\n        await this.fetchSceneTypes()\n        \n        this.calculateTotalMinutes()\n    }\n}\n</script>\n\n<style scoped>\n.new-scene {\n    padding: 20px;\n}\n\n.mt-20 {\n    margin-top: 20px;\n}\n\n.el-steps {\n    margin-bottom: 30px;\n}\n\n.platform-configs {\n    margin-top: 20px;\n    margin-bottom: 20px;\n}\n\n.platform-configs h3 {\n    margin-bottom: 16px;\n    color: #303133;\n    font-size: 16px;\n}\n\n.platform-card {\n    margin-bottom: 16px;\n}\n\n.platform-card:last-child {\n    margin-bottom: 0;\n}\n\n.data-options-container {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n}\n\n.checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.platform-selection-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.navigation-buttons {\n    margin-top: 30px;\n}\n\n.field-container {\n    display: flex;\n    flex-direction: column;\n    gap: 4px;\n}\n\n.field-description {\n    font-size: 12px;\n    color: #909399;\n    line-height: 1.4;\n}\n\n.platform-selection-tip {\n    margin-bottom: 16px;\n}\n\n.no-platforms-tip {\n    margin-top: 16px;\n}\n\n.loading-tip {\n    margin-top: 16px;\n}\n\n.effect-params-container {\n    margin-top: 16px;\n}\n\n.effect-param-checkbox {\n    margin-right: 16px;\n    margin-bottom: 8px;\n}\n\n.effect-params-table {\n    margin-top: 16px;\n}\n\n.effect-params-table h4 {\n    margin-bottom: 12px;\n    color: #303133;\n    font-size: 14px;\n}\n\n\n\n.no-effect-params-tip {\n    margin-bottom: 16px;\n}\n\n.table-header-with-tooltip {\n    cursor: help;\n    display: flex;\n    align-items: center;\n    gap: 4px;\n}\n\n.table-header-with-tooltip .el-icon-question {\n    color: #909399;\n    font-size: 14px;\n}\n\n/* 新增样式：多模态和发布形态选择 */\n.modality-selection-container,\n.publish-form-selection-container {\n    width: 100%;\n}\n\n.selection-row {\n    display: flex;\n    align-items: flex-start;\n    gap: 15px;\n    flex-wrap: wrap;\n}\n\n.selected-tags {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 8px;\n    min-height: 32px;\n    align-items: center;\n}\n</style>"], "mappings": "AAylBA,SAAAA,QAAA,EAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,UAAA;MACAC,OAAA;MACAC,IAAA;QACAC,wBAAA;QACAC,qBAAA;QACAC,UAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,oBAAA;QACAC,0BAAA;QACAC,2BAAA;QACAC,cAAA;QACAC,uBAAA;QACAC,IAAA;QACAC,QAAA;QACAC,mBAAA;QAAA;QACAC,sBAAA;QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,4BAAA;QACAC,6BAAA;QACAC,mBAAA;QACAC,4BAAA;QACAC,iCAAA;QACAC,yBAAA;QACAC,+BAAA;MACA;MACAC,cAAA;MACAC,aAAA;MACAC,KAAA;QACA/B,wBAAA,GACA;UACAgC,QAAA;UACAC,OAAA;UACAC,OAAA;UACAC,SAAA,EAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA,KAAAD,KAAA;cACAC,QAAA,KAAAC,KAAA;YACA;cACAD,QAAA;YACA;UACA;QACA,EACA;QACArC,qBAAA,GACA;UACA+B,QAAA;UACAC,OAAA;UACAC,OAAA;UACAC,SAAA,EAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA,KAAAD,KAAA;cACAC,QAAA,KAAAC,KAAA;YACA;cACAD,QAAA;YACA;UACA;QACA,EACA;QACApC,UAAA,GACA;UAAA8B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA/B,iBAAA,GACA;UAAA6B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA9B,eAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA7B,gBAAA,GACA;UAAA2B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACApB,cAAA,GACA;UAAAkB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,uBAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAM,IAAA;UACAC,GAAA;UACAR,OAAA;UACAC,OAAA;QACA,EACA;QACAZ,4BAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,6BAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,mBAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,4BAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,iCAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,yBAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,+BAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAS,YAAA;MACAC,SAAA;MACAC,sBAAA;MACAC,uBAAA;MACAC,sBAAA;MACAC,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,WAAA;MACA;MACAC,WAAA;QACAH,WAAA,GACA;UAAAjB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAgB,UAAA,GACA;UAAAlB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAmB,YAAA;MACAC,eAAA;MACAC,uBAAA;MACAC,wBAAA;MACAC,cAAA;QACAC,YAAA;MACA;MACAC,eAAA;QACAC,cAAA;QACAC,qBAAA;QACAC,8BAAA;QACAC,+BAAA;QACAC,wBAAA;QACAC,sCAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,eAAA;MACA;MACAC,mBAAA;MACAC,oBAAA;MACAC,gCAAA;MACAC,YAAA;QACAd,YAAA,GACA;UAAA1B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAuC,aAAA;QACAb,cAAA,GACA;UAAA5B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA2B,qBAAA,GACA;UAAA7B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA4B,8BAAA,GACA;UAAA9B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA6B,+BAAA,GACA;UAAA/B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAwC,aAAA;MACAC,cAAA;MACAC,UAAA;MACAC,qBAAA;MACAC,sBAAA;MACAC,qBAAA;MAAA;MACA;MACAC,cAAA,GACA,SACA,QACA,MACA,MACA,QACA,QACA,OACA;MACA;MACAC,kBAAA,GACA,OACA,UACA,OACA,WACA,MACA,SACA,UACA;IAEA;EACA;EACAC,QAAA;IACA,GAAAzF,QAAA;IACA0F,mBAAA;MACA,UAAApF,IAAA,CAAAE,qBAAA;QACA,YAAAmF,SAAA;MACA;MAEA,MAAAC,gBAAA,QAAAzC,SAAA,CAAA0C,IAAA,CAAAC,QAAA,IAAAA,QAAA,CAAAC,cAAA,UAAAzF,IAAA,CAAAE,qBAAA;MACA,KAAAoF,gBAAA,KAAAA,gBAAA,CAAAnB,mBAAA;QACA,YAAAkB,SAAA;MACA;MAEA,MAAAK,iBAAA,GAAAJ,gBAAA,CAAAnB,mBAAA,CAAAwB,KAAA,MAAAC,GAAA,CAAAC,EAAA,IAAAC,QAAA,CAAAD,EAAA,CAAAE,IAAA;MACA,YAAAV,SAAA,CAAAW,MAAA,CAAAC,QAAA,IAAAP,iBAAA,CAAAQ,QAAA,CAAAD,QAAA,CAAA7C,WAAA;IACA;IACA+C,sBAAA;MACA,UAAAnG,IAAA,CAAAE,qBAAA;QACA;MACA;MAEA,MAAAoF,gBAAA,QAAAzC,SAAA,CAAA0C,IAAA,CAAAC,QAAA,IAAAA,QAAA,CAAAC,cAAA,UAAAzF,IAAA,CAAAE,qBAAA;MACA,KAAAoF,gBAAA,KAAAA,gBAAA,CAAAvB,8BAAA;QACA;MACA;MAEA;QACA,MAAAqC,MAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAhB,gBAAA,CAAAvB,8BAAA;QACA,OAAAwC,KAAA,CAAAC,OAAA,CAAAJ,MAAA,IAAAA,MAAA;MACA,SAAAK,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA;MACA;IACA;IACAE,wBAAA;MACA,SAAA7G,UAAA,eAAAgF,qBAAA;QACA;MACA;MACA,SAAAhF,UAAA,eAAAiF,sBAAA;QACA;MACA;MACA;IACA;EACA;EACA6B,OAAA;IACA,GAAAjH,UAAA;IACA;IACAkH,yBAAAZ,QAAA;MACA;MACA,IAAAA,QAAA,CAAAa,aAAA,IAAAb,QAAA,CAAAa,aAAA,CAAAC,WAAA,GAAAb,QAAA;QACA,YAAAhB,kBAAA;MACA;MACA;MACA;MACA,YAAAA,kBAAA;IACA;IACA;IACA8B,qBAAAC,UAAA,EAAAC,gBAAA;MACA,KAAAA,gBAAA;MAEA,UAAAlH,IAAA,CAAAmB,mBAAA,CAAA8F,UAAA;QACA,KAAAE,IAAA,MAAAnH,IAAA,CAAAmB,mBAAA,EAAA8F,UAAA;MACA;MAEA,UAAAjH,IAAA,CAAAmB,mBAAA,CAAA8F,UAAA,EAAAf,QAAA,CAAAgB,gBAAA;QACA,KAAAlH,IAAA,CAAAmB,mBAAA,CAAA8F,UAAA,EAAAG,IAAA,CAAAF,gBAAA;MACA;;MAEA;MACA,KAAAC,IAAA,MAAAnC,qBAAA,EAAAiC,UAAA;IACA;IACA;IACAI,eAAAJ,UAAA,EAAA/F,QAAA;MACA,SAAAlB,IAAA,CAAAmB,mBAAA,CAAA8F,UAAA;QACA,MAAAK,KAAA,QAAAtH,IAAA,CAAAmB,mBAAA,CAAA8F,UAAA,EAAAM,OAAA,CAAArG,QAAA;QACA,IAAAoG,KAAA;UACA,KAAAtH,IAAA,CAAAmB,mBAAA,CAAA8F,UAAA,EAAAO,MAAA,CAAAF,KAAA;QACA;MACA;IACA;IACA;IACAG,kBAAAR,UAAA;MACA,KAAAE,IAAA,MAAAnH,IAAA,CAAAoB,sBAAA,EAAA6F,UAAA;IACA;IACA,MAAAS,qBAAAT,UAAA;MACA;QACA,MAAAU,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACA3E,WAAA,EAAA6D;UACA;QACA;QACA,OAAAU,QAAA,CAAA9H,IAAA,CAAAA,IAAA;MACA,SAAA4G,KAAA;QACAC,OAAA,CAAAD,KAAA,qCAAAA,KAAA;QACA;MACA;IACA;IACA,MAAAuB,0BAAAf,UAAA;MACA;QACA,MAAAU,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACA3E,WAAA,EAAA6D;UACA;QACA;QACA,OAAAU,QAAA,CAAA9H,IAAA,CAAAA,IAAA;MACA,SAAA4G,KAAA;QACAC,OAAA,CAAAD,KAAA,2CAAAA,KAAA;QACA;MACA;IACA;IACAwB,oCAAAhB,UAAA;MACA,MAAAiB,iBAAA,QAAA/B,qBAAA;MAEA,MAAAF,QAAA,QAAAnD,sBAAA,CAAAyC,IAAA,CAAA4C,CAAA,IAAAA,CAAA,CAAA/E,WAAA,KAAA6D,UAAA;MACA,MAAAmB,cAAA,GAAAnC,QAAA,GAAAA,QAAA,CAAAoC,YAAA;MAEA,IAAAD,cAAA,CAAAE,MAAA;QACA;MACA;MAEA,OAAAF,cAAA,CAAApC,MAAA,CAAA+B,KAAA,IAAAG,iBAAA,CAAAhC,QAAA,CAAA6B,KAAA,CAAAQ,iBAAA;IACA;IACAC,kBAAA/F,IAAA;MACA,MAAAgG,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,YAAA,CAAAhG,IAAA;IACA;IACAiG,cAAAC,KAAA;MACA,MAAAC,KAAA;QACAC,WAAA,QAAAF,KAAA,CAAAG,KAAA;MACA;MACA,IAAAH,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAnG,IAAA;MACA;MACA,IAAAkG,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAnG,IAAA;QACAmG,KAAA,CAAAI,IAAA;MACA;MACA,IAAAL,KAAA,CAAAI,UAAA,iBAAAJ,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAK,QAAA,GAAAN,KAAA,CAAAI,UAAA;QACAH,KAAA,CAAAM,OAAA,GAAAP,KAAA,CAAAO,OAAA;MACA;MACA,OAAAN,KAAA;IACA;IACA,MAAAO,0BAAAC,WAAA;MACA,SAAAtE,qBAAA;QACA;MACA;MACA,KAAAA,qBAAA;MAEA;QACA,KAAAhC,sBAAA;QAEAuG,MAAA,CAAAC,IAAA,MAAAtH,KAAA,EAAAuH,OAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,UAAA,8BAAAD,GAAA,CAAAE,QAAA;YACA,KAAAC,OAAA,MAAA3H,KAAA,EAAAwH,GAAA;UACA;QACA;QAEA,MAAAI,oBAAA;QACA,WAAA3C,UAAA,IAAAmC,WAAA;UACA,MAAAnD,QAAA,QAAAZ,SAAA,CAAAE,IAAA,CAAA4C,CAAA,IAAAA,CAAA,CAAA/E,WAAA,KAAA6D,UAAA;UACA,KAAAhB,QAAA;UAEA;YACA,MAAA4D,cAAA,cAAAjC,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAC,KAAA;gBAAA3E,WAAA,EAAA6D;cAAA;YACA;YAEA,MAAAiC,OAAA,cAAAxB,oBAAA,CAAAT,UAAA;YAEA,MAAAoB,YAAA,cAAAL,yBAAA,CAAAf,UAAA;YAEA2C,oBAAA,CAAAxC,IAAA;cACA,GAAAyC,cAAA,CAAAhK,IAAA,CAAAA,IAAA;cACAqJ,OAAA,EAAAA,OAAA;cACAb,YAAA,EAAAA;YACA;UACA,SAAA5B,KAAA;YACAC,OAAA,CAAAD,KAAA,oCAAAA,KAAA;YACA,KAAAqD,QAAA,CAAArD,KAAA,QAAAR,QAAA,CAAAa,aAAA;UACA;QACA;QAEA,KAAAhE,sBAAA,GAAA8G,oBAAA;QAEA,WAAA3C,UAAA,IAAAmC,WAAA;UACA,UAAApJ,IAAA,CAAAO,oBAAA,CAAA0G,UAAA;YACA,KAAAE,IAAA,MAAAnH,IAAA,CAAAO,oBAAA,EAAA0G,UAAA;cACA8C,sBAAA;YACA;UACA;UAEA,UAAA/J,IAAA,CAAAS,kBAAA,CAAAwG,UAAA;YACA,KAAAE,IAAA,MAAAnH,IAAA,CAAAS,kBAAA,EAAAwG,UAAA;UACA;UAEA,UAAAjH,IAAA,CAAAW,mBAAA,CAAAsG,UAAA;YACA,KAAAE,IAAA,MAAAnH,IAAA,CAAAW,mBAAA,EAAAsG,UAAA;UACA;UAEA,MAAA+C,mBAAA,QAAAlH,sBAAA,CAAAyC,IAAA,CAAA4C,CAAA,IAAAA,CAAA,CAAA/E,WAAA,KAAA6D,UAAA;UACA,IAAA+C,mBAAA,IAAAA,mBAAA,CAAAC,MAAA;YACAD,mBAAA,CAAAC,MAAA,CAAAV,OAAA,CAAAZ,KAAA;cACA,IAAAA,KAAA,CAAA1G,QAAA;gBACA,MAAAiI,SAAA,2BAAAjD,UAAA,IAAA0B,KAAA,CAAAwB,UAAA;gBACA,KAAAhD,IAAA,MAAAnF,KAAA,EAAAkI,SAAA,GACA;kBAAAjI,QAAA;kBAAAC,OAAA,QAAAyG,KAAA,CAAAG,KAAA;kBAAA3G,OAAA;gBAAA,EACA;cACA;YACA;UACA;QACA;MACA,SAAAsE,KAAA;QACAC,OAAA,CAAAD,KAAA,wCAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;QACA,KAAA3B,qBAAA;MACA;IACA;IACA,MAAAsF,2BAAAhB,WAAA;MACA,SAAArE,sBAAA;QACA;MACA;MACA,KAAAA,sBAAA;MAEA;QACA,KAAAhC,uBAAA;QAEAsG,MAAA,CAAAC,IAAA,MAAAtH,KAAA,EAAAuH,OAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,UAAA,+BAAAD,GAAA,CAAAE,QAAA;YACA,KAAAC,OAAA,MAAA3H,KAAA,EAAAwH,GAAA;UACA;QACA;QAEA,MAAAI,oBAAA;QACA,WAAA3C,UAAA,IAAAmC,WAAA;UACA,MAAAnD,QAAA,QAAAZ,SAAA,CAAAE,IAAA,CAAA4C,CAAA,IAAAA,CAAA,CAAA/E,WAAA,KAAA6D,UAAA;UACA,KAAAhB,QAAA;UAEA;YACA,MAAA4D,cAAA,cAAAjC,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAC,KAAA;gBAAA3E,WAAA,EAAA6D;cAAA;YACA;YAEA,MAAAiC,OAAA,cAAAxB,oBAAA,CAAAT,UAAA;YAEA2C,oBAAA,CAAAxC,IAAA;cACA,GAAAyC,cAAA,CAAAhK,IAAA,CAAAA,IAAA;cACAqJ,OAAA,EAAAA;YACA;UACA,SAAAzC,KAAA;YACAC,OAAA,CAAAD,KAAA,oCAAAA,KAAA;YACA,KAAAqD,QAAA,CAAArD,KAAA,QAAAR,QAAA,CAAAa,aAAA;UACA;QACA;QAEA,KAAA/D,uBAAA,GAAA6G,oBAAA;QAEA,WAAA3C,UAAA,IAAAmC,WAAA;UACA,UAAApJ,IAAA,CAAAQ,qBAAA,CAAAyG,UAAA;YACA,KAAAE,IAAA,MAAAnH,IAAA,CAAAQ,qBAAA,EAAAyG,UAAA;cACA8C,sBAAA;YACA;UACA;UAEA,UAAA/J,IAAA,CAAAU,mBAAA,CAAAuG,UAAA;YACA,KAAAE,IAAA,MAAAnH,IAAA,CAAAU,mBAAA,EAAAuG,UAAA;UACA;;UAEA;UACA,UAAAjH,IAAA,CAAAmB,mBAAA,CAAA8F,UAAA;YACA,KAAAE,IAAA,MAAAnH,IAAA,CAAAmB,mBAAA,EAAA8F,UAAA;UACA;UAEA,UAAAjH,IAAA,CAAAoB,sBAAA,CAAA6F,UAAA;YACA,KAAAE,IAAA,MAAAnH,IAAA,CAAAoB,sBAAA,EAAA6F,UAAA;UACA;UAEA,MAAA+C,mBAAA,QAAAjH,uBAAA,CAAAwC,IAAA,CAAA4C,CAAA,IAAAA,CAAA,CAAA/E,WAAA,KAAA6D,UAAA;UACA,IAAA+C,mBAAA,IAAAA,mBAAA,CAAAC,MAAA;YACAD,mBAAA,CAAAC,MAAA,CAAAV,OAAA,CAAAZ,KAAA;cACA,IAAAA,KAAA,CAAA1G,QAAA;gBACA,MAAAiI,SAAA,4BAAAjD,UAAA,IAAA0B,KAAA,CAAAwB,UAAA;gBACA,KAAAhD,IAAA,MAAAnF,KAAA,EAAAkI,SAAA,GACA;kBAAAjI,QAAA;kBAAAC,OAAA,QAAAyG,KAAA,CAAAG,KAAA;kBAAA3G,OAAA;gBAAA,EACA;cACA;YACA;UACA;QACA;MACA,SAAAsE,KAAA;QACAC,OAAA,CAAAD,KAAA,yCAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;QACA,KAAA1B,sBAAA;MACA;IACA;IACAsF,SAAA;MACA,SAAA1D,uBAAA;QACA,KAAAmD,QAAA,CAAAQ,OAAA;QACA;MACA;MAEA,IAAAC,gBAAA;MAEA,aAAAzK,UAAA;QACA;UACAyK,gBAAA;UACA;QACA;UACAA,gBAAA;UACA;QACA;UACAA,gBAAA;UAEA,KAAAzH,sBAAA,CAAAyG,OAAA,CAAAtD,QAAA;YACA,IAAAA,QAAA,CAAAgE,MAAA;cACAhE,QAAA,CAAAgE,MAAA,CAAAV,OAAA,CAAAZ,KAAA;gBACA,IAAAA,KAAA,CAAA1G,QAAA;kBACA,MAAAiI,SAAA,2BAAAjE,QAAA,CAAA7C,WAAA,IAAAuF,KAAA,CAAAwB,UAAA;kBACAI,gBAAA,CAAAnD,IAAA,CAAA8C,SAAA;gBACA;cACA;YACA;YAEA,MAAAM,cAAA,QAAAxK,IAAA,CAAAW,mBAAA,CAAAsF,QAAA,CAAA7C,WAAA;YACAoH,cAAA,CAAAjB,OAAA,CAAAkB,SAAA;cACA,MAAAC,YAAA,iCAAAzE,QAAA,CAAA7C,WAAA,IAAAqH,SAAA;cACAF,gBAAA,CAAAnD,IAAA,IAAAsD,YAAA;cACAH,gBAAA,CAAAnD,IAAA,IAAAsD,YAAA;cACAH,gBAAA,CAAAnD,IAAA,IAAAsD,YAAA;YACA;UACA;UACA;QACA;UACAH,gBAAA;UAEA,KAAAxH,uBAAA,CAAAwG,OAAA,CAAAtD,QAAA;YACA,IAAAA,QAAA,CAAAgE,MAAA;cACAhE,QAAA,CAAAgE,MAAA,CAAAV,OAAA,CAAAZ,KAAA;gBACA,IAAAA,KAAA,CAAA1G,QAAA;kBACA,MAAAiI,SAAA,4BAAAjE,QAAA,CAAA7C,WAAA,IAAAuF,KAAA,CAAAwB,UAAA;kBACAI,gBAAA,CAAAnD,IAAA,CAAA8C,SAAA;gBACA;cACA;YACA;;YAEA;YACA,UAAAlK,IAAA,CAAAmB,mBAAA,CAAA8E,QAAA,CAAA7C,WAAA,KACA,KAAApD,IAAA,CAAAmB,mBAAA,CAAA8E,QAAA,CAAA7C,WAAA,EAAAkF,MAAA;cACA,KAAAwB,QAAA,CAAArD,KAAA,SAAAR,QAAA,CAAAa,aAAA;cACA;YACA;;YAEA;YACA,UAAA9G,IAAA,CAAAoB,sBAAA,CAAA6E,QAAA,CAAA7C,WAAA;cACA,KAAA0G,QAAA,CAAArD,KAAA,SAAAR,QAAA,CAAAa,aAAA;cACA;YACA;UACA;UACA;QACA;UACAyD,gBAAA;UACA;QACA;UACA;MACA;MAEA,KAAAI,cAAA,CAAAJ,gBAAA,EAAAK,KAAA;QACA,IAAAA,KAAA;UACA,KAAA9K,UAAA;QACA;MACA;IACA;IACA6K,eAAAV,MAAA,EAAA1H,QAAA;MACA,IAAA0H,MAAA,CAAA3B,MAAA;QACA/F,QAAA;QACA;MACA;MAEAmE,OAAA,CAAAmE,GAAA,UAAAZ,MAAA;MACAvD,OAAA,CAAAmE,GAAA,iBAAA7K,IAAA;MAEA,MAAA8K,kBAAA,GAAAb,MAAA,CAAArE,GAAA,CAAA+C,KAAA;QACA,WAAAoC,OAAA,CAAAC,OAAA;UACA,KAAAC,KAAA,CAAAjL,IAAA,CAAAkL,aAAA,CAAAvC,KAAA,EAAAwC,YAAA;YACAzE,OAAA,CAAAmE,GAAA,OAAAlC,KAAA,UAAAwC,YAAA;YACAH,OAAA;cAAArC,KAAA;cAAAwC;YAAA;UACA;QACA;MACA;MAEAJ,OAAA,CAAAK,GAAA,CAAAN,kBAAA,EAAAO,IAAA,CAAAC,OAAA;QACA,MAAAC,QAAA,GAAAD,OAAA,CAAAE,IAAA,CAAAC,MAAA,IAAAA,MAAA,CAAAN,YAAA;QACAzE,OAAA,CAAAmE,GAAA,UAAAS,OAAA,YAAAC,QAAA;QACAhJ,QAAA,EAAAgJ,QAAA;MACA;IACA;IACAG,SAAA;MACA,KAAA5L,UAAA;IACA;IACA,MAAA6L,WAAA;MACA,KAAAV,KAAA,CAAAjL,IAAA,CAAA4L,QAAA,OAAAhB,KAAA;QACA,IAAAA,KAAA;UACA;YACA,MAAAiB,QAAA;YAEA,MAAAxD,YAAA;YAEA,WAAApB,UAAA,SAAAjH,IAAA,CAAAK,eAAA;cACA,MAAAyL,YAAA,QAAA9L,IAAA,CAAAO,oBAAA,CAAA0G,UAAA;cACA,MAAA8E,WAAA,QAAA/L,IAAA,CAAAS,kBAAA,CAAAwG,UAAA;cAEA4E,QAAA,CAAAzE,IAAA;gBACA4E,YAAA;gBACA5I,WAAA,EAAA6D,UAAA;gBACAgF,WAAA,MAAAC,IAAA,GAAAC,cAAA,UAAAC,OAAA;gBACAC,iBAAA,EAAAN,WAAA,CAAAzD,MAAA,OAAAyD,WAAA,CAAAO,IAAA;gBACA,GAAAR;cACA;cAEA,SAAA9L,IAAA,CAAAa,0BAAA,CAAAoG,UAAA;gBACAoC,MAAA,CAAAkD,MAAA,MAAAvM,IAAA,CAAAa,0BAAA,CAAAoG,UAAA,GAAAsC,OAAA,CAAAiD,WAAA;kBACAnE,YAAA,CAAAjB,IAAA;oBACAhE,WAAA,EAAA6D,UAAA;oBACA,GAAAuF;kBACA;gBACA;cACA;YACA;YAEA,WAAAvF,UAAA,SAAAjH,IAAA,CAAAM,gBAAA;cACA,MAAAwL,YAAA,QAAA9L,IAAA,CAAAQ,qBAAA,CAAAyG,UAAA;cACA,MAAA8E,WAAA,QAAA/L,IAAA,CAAAU,mBAAA,CAAAuG,UAAA;cAEA4E,QAAA,CAAAzE,IAAA;gBACA4E,YAAA;gBACA5I,WAAA,EAAA6D,UAAA;gBACAgF,WAAA,MAAAC,IAAA,GAAAC,cAAA,UAAAC,OAAA;gBACAC,iBAAA,EAAAN,WAAA,CAAAzD,MAAA,OAAAyD,WAAA,CAAAO,IAAA;gBACA,GAAAR;cACA;YACA;YAEA;cAAAzL,eAAA;cAAAI,kBAAA;cAAAF,oBAAA;cAAAG,mBAAA;cAAAJ,gBAAA;cAAAE,qBAAA;cAAAG,mBAAA;cAAAC,oBAAA;cAAAC,0BAAA;cAAAC,2BAAA;cAAAK,mBAAA;cAAAC,sBAAA;cAAA,GAAAqL;YAAA,SAAAzM,IAAA;YACA0G,OAAA,CAAAgG,IAAA,CAAArM,eAAA,EAAAI,kBAAA,EAAAF,oBAAA,EAAAG,mBAAA,EAAAJ,gBAAA,EAAAE,qBAAA,EAAAG,mBAAA,EAAAC,oBAAA,EAAAC,0BAAA,EAAAC,2BAAA,EAAAK,mBAAA,EAAAC,sBAAA;YAEAqL,UAAA,CAAAE,KAAA;YACAF,UAAA,CAAAR,WAAA,OAAAC,IAAA,GAAAC,cAAA,UAAAC,OAAA;YACAK,UAAA,CAAAG,cAAA;YACAH,UAAA,CAAAI,UAAA;YAEA,WAAAjF,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAjI,IAAA,EAAA4M,UAAA;cACAZ,QAAA,EAAAA,QAAA;cACAiB,aAAA,EAAAzE,YAAA;cACAlH,mBAAA,EAAAA,mBAAA;cACAC,sBAAA,EAAAA;YACA;YACA,KAAA0I,QAAA,CAAAiD,OAAA;YACA,KAAAC,OAAA,CAAA5F,IAAA;UACA,SAAAX,KAAA;YACA,KAAAqD,QAAA,CAAArD,KAAA;YACAC,OAAA,CAAAD,KAAA,0BAAAA,KAAA;UACA;QACA;MACA;IACA;IACAwG,oBAAAhG,UAAA;MACA,KAAAhE,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,WAAA,EAAA6D;MACA;MACA,KAAAjE,sBAAA;MACA,KAAAkK,SAAA;QACA,KAAAjC,KAAA,CAAAkC,UAAA,SAAAlC,KAAA,CAAAkC,UAAA,CAAAC,aAAA;MACA;IACA;IACA,MAAAC,aAAA;MACA,KAAApC,KAAA,CAAAkC,UAAA,CAAAvB,QAAA,OAAAhB,KAAA;QACA,IAAAA,KAAA;UACA;YACA,KAAAtH,YAAA;YACA,WAAAsE,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAjI,IAAA;gBACA,QAAAoD,aAAA;gBACAG,WAAA,OAAAH,aAAA,CAAAG;cACA;YACA;YACA,KAAA0G,QAAA,CAAAiD,OAAA;YACA,KAAA/J,sBAAA;YACA,KAAAC,aAAA;cACAC,WAAA;cACAC,UAAA;cACAC,WAAA;YACA;YACA,WAAA+F,yBAAA,MAAAnJ,IAAA,CAAAK,eAAA;YACA,WAAA+J,0BAAA,MAAApK,IAAA,CAAAM,gBAAA;UACA,SAAAmG,KAAA;YACA,KAAAqD,QAAA,CAAArD,KAAA;YACAC,OAAA,CAAAD,KAAA,6BAAAA,KAAA;UACA;YACA,KAAAnD,YAAA;UACA;QACA;MACA;IACA;IACA,MAAAgK,qBAAA;MACA;QACA,MAAA3F,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;UACAC,KAAA;YACAwF,SAAA;UACA;QACA;QACA,KAAAhK,eAAA,GAAAoE,QAAA,CAAA9H,IAAA,CAAAA,IAAA;MACA,SAAA4G,KAAA;QACAC,OAAA,CAAAD,KAAA,qCAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;IACA;IACA,MAAA+G,kBAAA;MACA;QACA,MAAA7F,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;QACA;QACA,KAAAlF,YAAA,GAAA+E,QAAA,CAAA9H,IAAA,CAAAA,IAAA;MACA,SAAA4G,KAAA;QACAC,OAAA,CAAAD,KAAA,kCAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;IACA;IACA,MAAAgH,eAAA;MACA;QACA,MAAA9F,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;QACA;QACA,KAAAjF,SAAA,GAAA8E,QAAA,CAAA9H,IAAA,CAAAA,IAAA;MACA,SAAA4G,KAAA;QACAC,OAAA,CAAAD,KAAA,+BAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;IACA;IACA,MAAAiH,gBAAA;MACA;QACA,MAAA/F,QAAA,cAAAC,KAAA,CAAAC,IAAA;UACAC,GAAA;QACA;QACA,KAAAjD,UAAA,GAAA8C,QAAA,CAAA9H,IAAA,CAAAA,IAAA;MACA,SAAA4G,KAAA;QACAC,OAAA,CAAAD,KAAA,gCAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;MACA;IACA;IACAkH,wBAAA;MACA,KAAAT,SAAA;QACA,KAAAjC,KAAA,CAAAjL,IAAA,CAAAoN,aAAA;MACA;IACA;IACAQ,qBAAA;MACA,KAAA5N,IAAA,CAAAK,eAAA;MACA,KAAAL,IAAA,CAAAM,gBAAA;MACA,KAAAwC,sBAAA;MACA,KAAAC,uBAAA;MACA,KAAA/C,IAAA,CAAAO,oBAAA;MACA,KAAAP,IAAA,CAAAQ,qBAAA;MACA,KAAAR,IAAA,CAAAS,kBAAA;MACA,KAAAT,IAAA,CAAAU,mBAAA;MACA,KAAAV,IAAA,CAAAW,mBAAA;MACA,KAAAX,IAAA,CAAAa,0BAAA;MACA,KAAAqM,SAAA;QACA,KAAAjC,KAAA,CAAAjL,IAAA,CAAAoN,aAAA;MACA;IACA;IACAS,yBAAA5G,UAAA;MACA,MAAAuD,cAAA,QAAAxK,IAAA,CAAAW,mBAAA,CAAAsG,UAAA;MAEA,UAAAjH,IAAA,CAAAa,0BAAA,CAAAoG,UAAA;QACA,KAAAE,IAAA,MAAAnH,IAAA,CAAAa,0BAAA,EAAAoG,UAAA;MACA;MAEA,MAAAhB,QAAA,QAAAnD,sBAAA,CAAAyC,IAAA,CAAA4C,CAAA,IAAAA,CAAA,CAAA/E,WAAA,KAAA6D,UAAA;MACA,MAAAmB,cAAA,GAAAnC,QAAA,GAAAA,QAAA,CAAAoC,YAAA;MAEA,IAAAD,cAAA,CAAAE,MAAA;QACA;MACA;MAEAe,MAAA,CAAAC,IAAA,MAAAtH,KAAA,EAAAuH,OAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,UAAA,+BAAAxC,UAAA;UACA,KAAA0C,OAAA,MAAA3H,KAAA,EAAAwH,GAAA;QACA;MACA;MAEAgB,cAAA,CAAAjB,OAAA,CAAAxB,KAAA;QACA,UAAA/H,IAAA,CAAAa,0BAAA,CAAAoG,UAAA,EAAAc,KAAA;UAEA,MAAA+F,WAAA,GAAA1F,cAAA,CAAA7C,IAAA,CAAA4C,CAAA,IAAAA,CAAA,CAAA4F,iBAAA,KAAAhG,KAAA;UAEA,KAAAZ,IAAA,MAAAnH,IAAA,CAAAa,0BAAA,CAAAoG,UAAA,GAAAc,KAAA;YACAgG,iBAAA,EAAAD,WAAA,CAAAC,iBAAA;YACAxF,iBAAA,EAAAuF,WAAA,CAAAvF,iBAAA;YACAyF,0BAAA;YACAC,qBAAA;YACAC,uBAAA;UACA;QACA;QAEA,MAAAxD,YAAA,iCAAAzD,UAAA,IAAAc,KAAA;QACA,KAAAZ,IAAA,MAAAnF,KAAA,KAAA0I,YAAA,gCACA;UAAAzI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAgM,OAAA;UAAAjM,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,KAAAgF,IAAA,MAAAnF,KAAA,KAAA0I,YAAA,2BACA;UAAAzI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAgM,OAAA;UAAAjM,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,KAAAgF,IAAA,MAAAnF,KAAA,KAAA0I,YAAA,6BACA;UAAAzI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAgM,OAAA;UAAAjM,OAAA;UAAAC,OAAA;QAAA,EACA;MACA;MAEAkH,MAAA,CAAAC,IAAA,MAAAtJ,IAAA,CAAAa,0BAAA,CAAAoG,UAAA,GAAAsC,OAAA,CAAAxB,KAAA;QACA,KAAAyC,cAAA,CAAAtE,QAAA,CAAA6B,KAAA;UACA,KAAA4B,OAAA,MAAA3J,IAAA,CAAAa,0BAAA,CAAAoG,UAAA,GAAAc,KAAA;QACA;MACA;IACA;IACAqG,yBAAAnH,UAAA;MACA,MAAAuD,cAAA,QAAAxK,IAAA,CAAAW,mBAAA,CAAAsG,UAAA;MACA,MAAAoH,MAAA,QAAArO,IAAA,CAAAa,0BAAA,CAAAoG,UAAA;MAEA,OAAAuD,cAAA,CAAA5E,GAAA,CAAA6E,SAAA;QACAsD,iBAAA,EAAAtD,SAAA;QACAlC,iBAAA,EAAA8F,MAAA,CAAA5D,SAAA,KAAA4D,MAAA,CAAA5D,SAAA,EAAAlC,iBAAA;QACAyF,0BAAA,EAAAK,MAAA,CAAA5D,SAAA,KAAA4D,MAAA,CAAA5D,SAAA,EAAAuD,0BAAA;QACAC,qBAAA,EAAAI,MAAA,CAAA5D,SAAA,KAAA4D,MAAA,CAAA5D,SAAA,EAAAwD,qBAAA;QACAC,uBAAA,EAAAG,MAAA,CAAA5D,SAAA,KAAA4D,MAAA,CAAA5D,SAAA,EAAAyD,uBAAA;MACA;IACA;IACAI,wBAAArH,UAAA,EAAAsH,SAAA,EAAA5F,KAAA,EAAArG,KAAA;MACA,UAAAtC,IAAA,CAAAa,0BAAA,CAAAoG,UAAA;QACA,KAAAE,IAAA,MAAAnH,IAAA,CAAAa,0BAAA,EAAAoG,UAAA;MACA;MACA,UAAAjH,IAAA,CAAAa,0BAAA,CAAAoG,UAAA,EAAAsH,SAAA;QACA,KAAApH,IAAA,MAAAnH,IAAA,CAAAa,0BAAA,CAAAoG,UAAA,GAAAsH,SAAA;MACA;MACA,KAAApH,IAAA,MAAAnH,IAAA,CAAAa,0BAAA,CAAAoG,UAAA,EAAAsH,SAAA,GAAA5F,KAAA,EAAArG,KAAA;IACA;IACAkM,YAAA;MACA,aAAAzM,aAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACA0M,YAAA;MACA,aAAA1M,aAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACA2M,QAAA;MACA,aAAA3M,aAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACA4M,sBAAA;MACA,IAAAC,YAAA;MACA,aAAA7M,aAAA;QACA;UACA6M,YAAA,QAAA9M,cAAA;UACA;QACA;UACA8M,YAAA,QAAA9M,cAAA;UACA;QACA;UACA8M,YAAA,QAAA9M,cAAA;UACA;MACA;MAEA,IAAA8M,YAAA;QACAA,YAAA;QACA,KAAA9M,cAAA;QACA,KAAAC,aAAA;MACA;MAEA,KAAA/B,IAAA,CAAAgB,uBAAA,GAAA4N,YAAA;MACA,OAAAA,YAAA;IACA;IACAC,0BAAAC,OAAA;MACA,KAAAA,OAAA,IAAAA,OAAA;QACA,KAAAhN,cAAA;QACA,KAAAC,aAAA;QACA;MACA;MAEA,IAAA+M,OAAA,YAAAA,OAAA;QACA,KAAAhN,cAAA,GAAAgN,OAAA;QACA,KAAA/M,aAAA;MACA,WAAA+M,OAAA,UAAAA,OAAA;QACA,KAAAhN,cAAA,GAAAgN,OAAA;QACA,KAAA/M,aAAA;MACA;QACA,KAAAD,cAAA,GAAAgN,OAAA;QACA,KAAA/M,aAAA;MACA;IACA;IACAgN,2BAAA;MACA,KAAAJ,qBAAA;IACA;IACAK,0BAAA;MACA,MAAAC,cAAA,QAAAN,qBAAA;MAEA,KAAAE,yBAAA,CAAAI,cAAA;MACA,KAAAN,qBAAA;IACA;IACAO,qBAAA;MACA,KAAAxL,cAAA;QACAC,YAAA;MACA;MACA,KAAAH,uBAAA;MACA,KAAA0J,SAAA;QACA,KAAAjC,KAAA,CAAAkE,WAAA,SAAAlE,KAAA,CAAAkE,WAAA,CAAA/B,aAAA;MACA;IACA;IACA,MAAAgC,cAAA;MACA,KAAAnE,KAAA,CAAAkE,WAAA,CAAAvD,QAAA,OAAAhB,KAAA;QACA,IAAAA,KAAA;UACA;YACA,KAAAjG,aAAA;YACA,WAAAiD,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAjI,IAAA,OAAA6D;YACA;YACA,KAAAoG,QAAA,CAAAiD,OAAA;YACA,KAAAvJ,uBAAA;YACA,WAAAgK,iBAAA;YACA,KAAAxN,IAAA,CAAAC,wBAAA,QAAAyD,cAAA,CAAAC,YAAA;UACA,SAAA8C,KAAA;YACA,KAAAqD,QAAA,CAAArD,KAAA;YACAC,OAAA,CAAAD,KAAA,8BAAAA,KAAA;UACA;YACA,KAAA9B,aAAA;UACA;QACA;MACA;IACA;IACA0K,sBAAA;MACA,KAAAzL,eAAA;QACAC,cAAA;QACAC,qBAAA;QACAC,8BAAA;QACAC,+BAAA;QACAC,wBAAA;QACAC,sCAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,eAAA;MACA;MACA,KAAAC,mBAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,gCAAA;MACA,KAAAf,wBAAA;MACA,KAAAyJ,SAAA;QACA,KAAAjC,KAAA,CAAAqE,YAAA,SAAArE,KAAA,CAAAqE,YAAA,CAAAlC,aAAA;MACA;IACA;IACA,MAAAmC,2BAAAC,WAAA;MACA,KAAA5L,eAAA,CAAAO,mBAAA,GAAAqL,WAAA,CAAAlD,IAAA;MAEA,WAAAmD,4BAAA,CAAAD,WAAA;IACA;IACA,MAAAC,6BAAArG,WAAA;MACA,IAAAA,WAAA,CAAAd,MAAA;QACA,KAAA9D,gCAAA;QACA,KAAAD,oBAAA;QACA,KAAAX,eAAA,CAAAG,8BAAA;QACA;MACA;MAEA;QACA,MAAA2L,oBAAA;QACA,WAAAzI,UAAA,IAAAmC,WAAA;UACA,MAAAhD,MAAA,cAAA4B,yBAAA,CAAAf,UAAA;UACAyI,oBAAA,CAAAtI,IAAA,CAAAhB,MAAA;QACA;QAEA,MAAAuJ,SAAA;QACA,MAAAC,SAAA,OAAAC,GAAA;QAEAH,oBAAA,CAAAnG,OAAA,CAAAnB,cAAA;UACAA,cAAA,CAAAmB,OAAA,CAAAxB,KAAA;YACA,KAAA6H,SAAA,CAAAE,GAAA,CAAA/H,KAAA,CAAAgG,iBAAA;cACA6B,SAAA,CAAAG,GAAA,CAAAhI,KAAA,CAAAgG,iBAAA;cACA4B,SAAA,CAAAvI,IAAA,CAAAW,KAAA;YACA;UACA;QACA;QAEA,KAAAvD,gCAAA,GAAAmL,SAAA;QAEA,KAAApL,oBAAA;QACA,KAAAX,eAAA,CAAAG,8BAAA;MAEA,SAAA0C,KAAA;QACAC,OAAA,CAAAD,KAAA,gDAAAA,KAAA;QACA,KAAAqD,QAAA,CAAArD,KAAA;QACA,KAAAjC,gCAAA;MACA;IACA;IACAwL,+BAAAC,aAAA;MACA,KAAArM,eAAA,CAAAG,8BAAA,GAAAsC,IAAA,CAAA6J,SAAA,CAAAD,aAAA;IACA;IACA,MAAAE,eAAA;MACA,KAAAlF,KAAA,CAAAqE,YAAA,CAAA1D,QAAA,OAAAhB,KAAA;QACA,IAAAA,KAAA;UACA;YACA,KAAAhG,cAAA;YACA,WAAAgD,KAAA,CAAAC,IAAA;cACAC,GAAA;cACAjI,IAAA,OAAA+D;YACA;YACA,KAAAkG,QAAA,CAAAiD,OAAA;YACA,KAAAtJ,wBAAA;YACA,WAAAgK,cAAA;UACA,SAAAhH,KAAA;YACA,KAAAqD,QAAA,CAAArD,KAAA;YACAC,OAAA,CAAAD,KAAA,gCAAAA,KAAA;UACA;YACA,KAAA7B,cAAA;UACA;QACA;MACA;IACA;EACA;EACA,MAAAwL,QAAA;IACA,WAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;IAAA;IACA,WAAA/C,iBAAA;IACA,WAAAC,cAAA;IACA,WAAAH,oBAAA;IACA,WAAAI,eAAA;IAEA,KAAAiB,qBAAA;EACA;AACA", "ignoreList": []}]}