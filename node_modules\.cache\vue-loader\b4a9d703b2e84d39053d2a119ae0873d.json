{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\App.vue", "mtime": 1754017590000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICAgIG5hbWU6ICdBcHAnLAogICAgZGF0YSgpIHsKICAgICAgICByZXR1cm4gewogICAgICAgICAgICBsb2dvSW1hZ2U6IG51bGwKICAgICAgICB9CiAgICB9Cn0K"}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n    <div id=\"app\">\n        <el-container>\n            <el-aside width=\"200px\">\n                <div class=\"logo\">\n                    <img v-if=\"logoImage\" :src=\"logoImage\" alt=\"Logo\" class=\"logo-image\" />\n                    <i v-else class=\"el-icon-s-data logo-icon\"></i>\n                    <h3>AI自适应系统</h3>\n                </div>\n                <el-menu mode=\"vertical\" router :default-active=\"$route.path\" background-color=\"#304156\"\n                    text-color=\"#bfcbd9\" active-text-color=\"#409EFF\">\n                    <el-menu-item index=\"/\">\n                        <i class=\"el-icon-pie-chart\"></i>\n                        <span slot=\"title\">总览</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/scene/new\">\n                        <i class=\"el-icon-plus\"></i>\n                        <span slot=\"title\">新建场景</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/scene/manage\">\n                        <i class=\"el-icon-setting\"></i>\n                        <span slot=\"title\">场景管理</span>\n                    </el-menu-item>\n                </el-menu>\n            </el-aside>\n            <el-main>\n                <router-view></router-view>\n            </el-main>\n        </el-container>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'App',\n    data() {\n        return {\n            logoImage: null\n        }\n    }\n}\n</script>\n\n<style>\n#app {\n    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',\n        'Microsoft YaHei', '微软雅黑', Arial, sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    color: #2c3e50;\n}\n\n.el-container {\n    height: 100vh;\n}\n\n.el-aside {\n    background-color: #304156;\n    color: white;\n}\n\n.logo {\n    padding: 20px;\n    text-align: center;\n    border-bottom: 1px solid #434a50;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n}\n\n.logo-image {\n    width: 40px;\n    height: 40px;\n    margin-bottom: 10px;\n    border-radius: 4px;\n}\n\n.logo-icon {\n    font-size: 32px;\n    color: #409EFF;\n    margin-bottom: 10px;\n}\n\n.logo h3 {\n    color: #bfcbd9;\n    margin: 0;\n    font-size: 16px;\n    font-weight: 400;\n}\n\n.el-menu {\n    border-right: none;\n}\n\n.el-main {\n    padding: 20px;\n    background-color: #f5f7fa;\n    overflow-y: auto;\n}\n</style>"]}]}