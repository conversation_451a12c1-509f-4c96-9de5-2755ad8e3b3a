<template>
    <div class="manage-scene">
        <el-card class="scene-list">
            <div slot="header" class="header-with-search">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: '/scene/manage' }">场景管理</el-breadcrumb-item>
                    <el-breadcrumb-item>场景列表</el-breadcrumb-item>
                </el-breadcrumb>
                <div class="search-box">
                    <el-input
                        v-model="searchSceneName"
                        placeholder="请输入场景名称搜索"
                        style="width: 200px;"
                        clearable
                        @clear="handleSearch"
                        @keyup.enter.native="handleSearch"
                    >
                        <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
                    </el-input>
                </div>
            </div>

            <el-table :data="scenes" v-loading="loading" style="width: 100%">
                <el-table-column prop="scene_name" label="场景名称" min-width="150"/>
                <!-- <el-table-column prop="scene_description" label="场景描述" /> -->
                <el-table-column label="输入平台" min-width="120">
                    <template slot-scope="{ row }">
                        <el-tooltip :content="getPlatformNames(row.input_platforms)" placement="top" :disabled="!row.input_platforms || !row.input_platforms.length">
                            <span>{{ formatPlatforms(row.input_platforms) }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column label="输出平台" min-width="120">
                    <template slot-scope="{ row }">
                        <el-tooltip :content="getPlatformNames(row.output_platforms)" placement="top" :disabled="!row.output_platforms || !row.output_platforms.length">
                            <span>{{ formatPlatforms(row.output_platforms) }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column label="运行频率" min-width="100">
                    <template slot-scope="{ row }">
                        {{ formatFrequency(row.scene_running_frequency) }}
                    </template>
                </el-table-column>
                <el-table-column prop="final_running_time" label="最近运行时间"/>
                <el-table-column label="状态" width="100">
                    <template slot-scope="{ row }">
                        <el-tag :type="row.state === 1 ? 'success' : 'info'">
                            {{ row.state === 1 ? '运行中' : (row.state === 2 ? '已暂停' : '已删除') }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="350">
                    <template slot-scope="{ row }">
                        <el-button v-if="row.state !== 3" :type="row.state === 1 ? 'danger' : 'success'" @click="handleStateChange(row)">
                            {{ row.state === 1 ? '暂停' : '运行' }}
                        </el-button>
                        <el-button v-if="row.state === 2" type="danger" @click="handleDelete(row)">
                            删除
                        </el-button>
                        <el-button type="text" @click="handleShowHistory(row)">运行记录</el-button>
                        <el-button v-if="row.state !== 3" type="text" @click="handleEditScene(row)">场景设置</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="currentPage" :page-sizes="[10, 15, 20, 30]" :page-size="pageSize"
                    layout="total, sizes, prev, pager, next" :total="total" />
            </div>
        </el-card>
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
    name: 'ManageScene',
    data() {
        return {
            scenes: [],
            loading: false,
            currentPage: 1,
            pageSize: 15,
            total: 0,
            searchSceneName: ''
        }
    },
    computed: {
        ...mapState(['platforms'])
    },
    methods: {
        ...mapActions(['fetchPlatforms']),
        getPlatformNames(platformIds) {
            if (!platformIds || !platformIds.length) return ''
            return platformIds.map(id => {
                const platform = this.platforms.find(p => p.platform_id === id)
                return platform ? platform.platform_name : id
            }).join('、')
        },
        formatPlatforms(platformIds) {
            if (!platformIds || !platformIds.length) return '-'
            const platformNames = platformIds.map(id => {
                const platform = this.platforms.find(p => p.platform_id === id)
                return platform ? platform.platform_name : id
            })
            if (platformNames.length > 3) {
                return platformNames.slice(0, 3).join('、') + '...'
            }
            return platformNames.join('、')
        },
        formatFrequency(minutes) {
            if (!minutes || minutes <= 0) return '-'
            
            if (minutes >= 1440 && minutes % 1440 === 0) {
                const days = minutes / 1440
                return `每${days}天运行一次`
            }
            
            if (minutes >= 60 && minutes % 60 === 0) {
                const hours = minutes / 60
                return `每${hours}小时运行一次`
            }
            
            return `每${minutes}分钟运行一次`
        },
        async handleStateChange(scene) {
            try {
                await this.$http.post('', {
                    api: '/api/scene/updateState',
                    param: {
                        scene_id: scene.scene_id,
                        state: scene.state === 1 ? 2 : 1
                    }
                })
                this.$message.success('状态更新成功')
                this.fetchAllScenes();
            } catch (error) {
                this.$message.error('状态更新失败')
                console.error('Error updating scene state:', error)
            }
        },
        handleShowHistory(scene) {
            this.$router.push({ 
                path: `/scene/history/${scene.scene_id}`,
                query: { name: scene.scene_name }
            })
        },
        handleEditScene(scene) {
            this.$router.push({ path: `/scene/edit/${scene.scene_id}` })
        },
        async fetchAllScenes() {
            this.loading = true
            try {
                const response = await this.$http.post('', {
                    api: '/api/scene/getList',
                    param: {
                        page: this.currentPage,
                        pageSize: this.pageSize,
                        scene_name: this.searchSceneName
                    }
                })
                this.scenes = response.data.data || []
                this.scenes.forEach(scene => {
                    const accounts = scene.accounts || []
                    scene.input_platforms = accounts.filter(account => account.operate_type === 1).map(account => account.platform_id) || []
                    scene.output_platforms = accounts.filter(account => account.operate_type === 2).map(account => account.platform_id) || []
                })
                this.total = response.data.total || 0
            } catch (error) {
                this.$message.error('获取场景列表失败')
                console.error('Error fetching all scenes:', error)
            } finally {
                this.loading = false
            }
        },

        handleSizeChange(val) {
            this.pageSize = val
            this.fetchAllScenes()
        },
        handleCurrentChange(val) {
            this.currentPage = val
            this.fetchAllScenes()
        },

        handleSearch() {
            this.currentPage = 1
            this.fetchAllScenes()
        },
        async handleDelete(scene) {
            try {
                await this.$confirm('确认删除该场景吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                
                await this.$http.post('', {
                    api: '/api/scene/delete',
                    param: {
                        scene_id: scene.scene_id
                    }
                })
                
                this.$message.success('删除成功')
                this.fetchAllScenes()
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('删除失败')
                    console.error('Error deleting scene:', error)
                }
            }
        },

    },
    created() {
        this.fetchPlatforms({ page: 1, pageSize: 1000 })
        this.fetchAllScenes()
    }
}
</script>

<style scoped>
.manage-scene {
    padding: 20px;
}

.scene-list {
    margin-bottom: 20px;
}

.pagination {
    margin-top: 20px;
    text-align: right;
}

.header-with-search {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0;
}

.search-box {
    display: flex;
    align-items: center;
}

.el-breadcrumb {
    line-height: 1;
}


</style>