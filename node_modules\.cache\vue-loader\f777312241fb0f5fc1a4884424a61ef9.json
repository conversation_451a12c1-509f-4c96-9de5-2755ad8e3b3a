{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\scene\\NewScene.vue?vue&type=style&index=0&id=02de0229&scoped=true&lang=css", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\scene\\NewScene.vue", "mtime": 1754036804722}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754032202777}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754032187306}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754032201397}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["NewScene.vue"], "names": [], "mappings": ";AAuqDA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "NewScene.vue", "sourceRoot": "src/views/scene", "sourcesContent": ["<template>\n    <div class=\"new-scene\">\n        <el-steps :active=\"activeStep\" finish-status=\"success\" simple>\n            <el-step title=\"基本信息\" />\n            <el-step title=\"计算基准线配置\" />\n            <el-step title=\"数据输入平台\" />\n            <el-step title=\"数据输出平台\" />\n            <el-step title=\"其他设置\" />\n        </el-steps>\n\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"150px\" class=\"mt-20\" v-loading=\"loading\">\n            <div v-show=\"activeStep === 0\">\n                <el-form-item label=\"场景名称\" prop=\"scene_name\">\n                    <el-input v-model=\"form.scene_name\" placeholder=\"请输入场景名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型\" prop=\"linked_task_type_code\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_task_type_code\" placeholder=\"请选择任务类型\" style=\"flex: 1;\" @change=\"handleTaskTypeChange\">\n                            <el-option\n                                v-for=\"taskType in taskTypes\"\n                                :key=\"taskType.task_type_code\"\n                                :label=\"taskType.task_type_name\"\n                                :value=\"taskType.task_type_code\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddTaskTypeDialog\">\n                            新增任务类型\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景类型\" prop=\"scene_business_type\">\n                    <el-select \n                        v-model=\"form.scene_business_type\" \n                        placeholder=\"请选择或输入场景类型\" \n                        filterable \n                        allow-create \n                        default-first-option\n                        style=\"width: 100%\">\n                        <el-option\n                            v-for=\"sceneType in sceneTypes\"\n                            :key=\"sceneType\"\n                            :label=\"sceneType\"\n                            :value=\"sceneType\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"项目组\" prop=\"linked_project_team_name\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_project_team_name\" placeholder=\"请选择项目组\" style=\"flex: 1;\" @change=\"handleProjectTeamChange\">\n                            <el-option\n                                v-for=\"project in projectTeams\"\n                                :key=\"project.project_id\"\n                                :label=\"project.project_name\"\n                                :value=\"project.project_name\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddProjectDialog\">\n                            新增项目组\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景描述\" prop=\"scene_description\">\n                    <el-input type=\"textarea\" v-model=\"form.scene_description\" placeholder=\"请输入场景描述\"\n                        :rows=\"4\"></el-input>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 1\">\n                <el-form-item label=\"使用数据的起始天数\" prop=\"baseline_data_start_days_ago\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_start_days_ago\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线使用数据的起始天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"排除最近的数据天数\" prop=\"baseline_data_exclude_recent_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_exclude_recent_days\"\n                            :min=\"0\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线排除最近的数据天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"样本量最小阈值\" prop=\"min_baseline_sample_count\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.min_baseline_sample_count\"\n                            :min=\"1\"\n                            :max=\"10000\"\n                            :step=\"1\"\n                            placeholder=\"请输入样本量\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">个</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"一个场景在一次计算基准线时，所需要的最小样本量\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"基准线更新频率\" prop=\"baseline_refresh_frequency_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_refresh_frequency_days\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入频率\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"评估效果的基准线更新的频率\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 2\">\n                <el-form-item label=\"选择输入平台\" prop=\"input_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.input_platforms\" @change=\"handleInputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingInputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedInputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingInputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输入平台配置</h3>\n                    <el-card v-for=\"platform in selectedInputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <div class=\"field-container\">\n                                <component :is=\"getFieldComponent(field.field_type)\"\n                                    v-model=\"form.input_platforms_data[platform.platform_id][field.field_name]\"\n                                    v-bind=\"getFieldProps(field)\"></component>\n                                <div v-if=\"field.description\" class=\"field-description\">{{ field.description }}</div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据类型\" :prop=\"'input_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.input_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\"\n                                    @click=\"showAddOptionDialog(platform.platform_id)\">\n                                    新增数据类型\n                                </el-button>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"效果参数配置\">\n                            <div class=\"effect-params-container\">\n                                <div v-if=\"getAvailableEffectParamsForPlatform(platform.platform_id).length === 0\" class=\"no-effect-params-tip\">\n                                    <el-alert\n                                        title=\"该平台暂无可用的效果参数\"\n                                        type=\"info\"\n                                        :closable=\"false\"\n                                        show-icon>\n                                    </el-alert>\n                                </div>\n                                <el-checkbox-group v-else v-model=\"form.input_effect_params[platform.platform_id]\" @change=\"handleEffectParamsChange(platform.platform_id)\">\n                                    <el-checkbox v-for=\"param in getAvailableEffectParamsForPlatform(platform.platform_id)\" :key=\"param.effect_param_code\" :label=\"param.effect_param_code\" class=\"effect-param-checkbox\">\n                                        {{ param.effect_param_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                \n                                <div v-if=\"form.input_effect_params[platform.platform_id] && form.input_effect_params[platform.platform_id].length > 0\" class=\"effect-params-table\">\n                                    <h4>参数配置详情</h4>\n                                    <el-table :data=\"getEffectParamsTableData(platform.platform_id)\" border>\n                                        <el-table-column prop=\"effect_param_name\" label=\"参数名称\" width=\"120\"></el-table-column>\n                                        <el-table-column prop=\"configured_evaluation_days\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"系统会获取发布时间在T-基线范围内，且已满足各参数的Tij值的样本总数量。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *效果实现天数\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.configured_evaluation_days`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.configured_evaluation_days\" \n                                                        placeholder=\"如：3,5,10\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'configured_evaluation_days', scope.row.configured_evaluation_days)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_mean\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"μi是选取的样本量的效果数据里，该参数的平均值。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算μi，而是会使用您输入的缺省状态下的μi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *平均值\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_mean`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.default_baseline_mean\" \n                                                        placeholder=\"如：0\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_mean', scope.row.default_baseline_mean)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_stddev\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"σi是选取的样本量的效果数据里，该参数的标准差。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算σi，而是会使用您输入的缺省状态下的σi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *标准差\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_stddev`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input\n                                                        v-model=\"scope.row.default_baseline_stddev\" \n                                                        placeholder=\"如：1\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_stddev', scope.row.default_baseline_stddev)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                    </el-table>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.input_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n            </div>\n\n            <div v-show=\"activeStep === 3\">\n                <el-form-item label=\"选择输出平台\" prop=\"output_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.output_platforms\" @change=\"handleOutputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingOutputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedOutputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingOutputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输出平台配置</h3>\n                    <el-card v-for=\"platform in selectedOutputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <div class=\"field-container\">\n                                <component :is=\"getFieldComponent(field.field_type)\"\n                                    v-model=\"form.output_platforms_data[platform.platform_id][field.field_name]\"\n                                    v-bind=\"getFieldProps(field)\"></component>\n                                <div v-if=\"field.description\" class=\"field-description\">{{ field.description }}</div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据内容\" :prop=\"'output_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.output_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"多模态内容\" :prop=\"'platform_modalities.' + platform.platform_id\">\n                            <div class=\"modality-selection-container\">\n                                <div class=\"selection-row\">\n                                    <el-select\n                                        v-model=\"tempModalitySelection[platform.platform_id]\"\n                                        placeholder=\"请选择模态类型\"\n                                        style=\"width: 300px;\"\n                                        @change=\"handleModalitySelect(platform.platform_id, $event)\">\n                                        <el-option\n                                            v-for=\"modality in modalityTypes\"\n                                            :key=\"modality.dict_name\"\n                                            :label=\"modality.dict_name\"\n                                            :value=\"modality.dict_name\"\n                                            :disabled=\"form.platform_modalities[platform.platform_id] && form.platform_modalities[platform.platform_id].includes(modality.dict_name)\">\n                                        </el-option>\n                                    </el-select>\n                                    <div class=\"selected-tags\">\n                                        <el-tag\n                                            v-for=\"modality in form.platform_modalities[platform.platform_id] || []\"\n                                            :key=\"modality\"\n                                            closable\n                                            @close=\"removeModality(platform.platform_id, modality)\"\n                                            style=\"margin-right: 8px; margin-bottom: 8px;\">\n                                            {{ modality }}\n                                        </el-tag>\n                                    </div>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"发布形态\" :prop=\"'platform_publish_forms.' + platform.platform_id\">\n                            <div class=\"publish-form-selection-container\">\n                                <div class=\"selection-row\">\n                                    <el-select\n                                        v-model=\"form.platform_publish_forms[platform.platform_id]\"\n                                        placeholder=\"请选择发布形态\"\n                                        style=\"width: 300px;\">\n                                        <el-option\n                                            v-for=\"publishForm in getAvailablePublishForms(platform)\"\n                                            :key=\"publishForm.dict_name\"\n                                            :label=\"publishForm.dict_name\"\n                                            :value=\"publishForm.dict_name\">\n                                        </el-option>\n                                    </el-select>\n                                    <div class=\"selected-tags\">\n                                        <el-tag\n                                            v-if=\"form.platform_publish_forms[platform.platform_id]\"\n                                            closable\n                                            @close=\"removePublishForm(platform.platform_id)\"\n                                            style=\"margin-left: 8px;\">\n                                            {{ form.platform_publish_forms[platform.platform_id] }}\n                                        </el-tag>\n                                    </div>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.output_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n\n                <el-form-item label=\"模态\" prop=\"modality\">\n                    <el-select v-model=\"form.modality\" placeholder=\"请选择模态\">\n                        <el-option\n                            v-for=\"item in modalityOptions\"\n                            :key=\"item.dict_name\"\n                            :label=\"item.dict_name\"\n                            :value=\"item.dict_name\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 4\">\n                <el-form-item label=\"运行频率\" prop=\"scene_running_frequency\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-input-number\n                            v-model=\"frequencyValue\"\n                            :min=\"getMinValue()\"\n                            :max=\"getMaxValue()\"\n                            :step=\"getStep()\"\n                            placeholder=\"请输入数值\"\n                            style=\"width: 150px\"\n                            @change=\"handleFrequencyValueChange\">\n                        </el-input-number>\n                        <el-select \n                            v-model=\"frequencyUnit\" \n                            placeholder=\"请选择单位\"\n                            style=\"width: 120px\"\n                            @change=\"handleFrequencyUnitChange\">\n                            <el-option label=\"分钟\" value=\"minutes\"></el-option>\n                            <el-option label=\"小时\" value=\"hours\"></el-option>\n                            <el-option label=\"天\" value=\"days\"></el-option>\n                        </el-select>\n                        <span style=\"color: #909399; font-size: 12px;\">\n                            (最小间隔30分钟)\n                        </span>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"个性化进化更新频率\" prop=\"stored_strategy_refresh_days\">\n                    <el-input-number\n                        v-model=\"form.stored_strategy_refresh_days\"\n                        :min=\"0\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">建议您设为0</span>\n                </el-form-item>\n                <el-form-item label=\"AI自行探索频率\" width=\"400px\" prop=\"explore_strategy_trigger_days\">\n                    <el-input-number\n                        v-model=\"form.explore_strategy_trigger_days\"\n                        :min=\"1\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">目前我们的探索模式暂未上线，建议您先将Z值设为365天</span>\n                </el-form-item>\n                <el-form-item label=\"AI提示词\" prop=\"updated_prompt\">\n                    <el-input type=\"textarea\" v-model=\"form.updated_prompt\" placeholder=\"请输入AI提示词\" :rows=\"10\"></el-input>\n                </el-form-item>\n            </div>\n\n            <el-form-item class=\"navigation-buttons\">\n                <el-button v-if=\"activeStep > 0\" @click=\"prevStep\">上一步</el-button>\n                <el-button \n                    v-if=\"activeStep < 4\" \n                    type=\"primary\" \n                    @click=\"nextStep\"\n                    :disabled=\"isPlatformConfigLoading\"\n                    :loading=\"isPlatformConfigLoading\">\n                    {{ isPlatformConfigLoading ? '配置加载中...' : '下一步' }}\n                </el-button>\n                <el-button v-if=\"activeStep === 4\" type=\"primary\" @click=\"submitForm\">提交</el-button>\n            </el-form-item>\n        </el-form>\n\n        <el-dialog title=\"新增数据内容\" :visible.sync=\"addOptionDialogVisible\" width=\"500px\">\n            <el-form ref=\"optionForm\" :model=\"newOptionForm\" :rules=\"optionRules\" label-width=\"120px\">\n                <el-form-item label=\"内容名称\" prop=\"option_name\">\n                    <el-input v-model=\"newOptionForm.option_name\" placeholder=\"请输入内容名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"内容标识\" prop=\"option_key\">\n                    <el-input v-model=\"newOptionForm.option_key\" placeholder=\"请输入内容标识（英文）\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addOptionDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewOption\" :loading=\"addingOption\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增项目组\" :visible.sync=\"addProjectDialogVisible\" width=\"500px\">\n            <el-form ref=\"projectForm\" :model=\"newProjectForm\" :rules=\"projectRules\" label-width=\"120px\">\n                <el-form-item label=\"项目名称\" prop=\"project_name\">\n                    <el-input v-model=\"newProjectForm.project_name\" placeholder=\"请输入项目名称\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addProjectDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewProject\" :loading=\"addingProject\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增任务类型\" :visible.sync=\"addTaskTypeDialogVisible\" width=\"600px\">\n            <el-form ref=\"taskTypeForm\" :model=\"newTaskTypeForm\" :rules=\"taskTypeRules\" label-width=\"140px\">\n                <el-form-item label=\"任务类型名称\" prop=\"task_type_name\">\n                    <el-input v-model=\"newTaskTypeForm.task_type_name\" placeholder=\"请输入任务类型名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型描述\" prop=\"task_type_description\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.task_type_description\" placeholder=\"请输入任务类型描述\" :rows=\"3\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"关联平台\" prop=\"linked_platform_ids\">\n                    <el-select \n                        v-model=\"selectedPlatformIds\" \n                        multiple \n                        placeholder=\"请选择关联平台\"\n                        style=\"width: 100%\"\n                        @change=\"handlePlatformSelectChange\">\n                        <el-option\n                            v-for=\"platform in platforms\"\n                            :key=\"platform.platform_id\"\n                            :label=\"platform.platform_name\"\n                            :value=\"platform.platform_id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"推荐效果参数\" prop=\"recommended_effect_param_codes\">\n                    <el-select \n                        v-model=\"selectedEffectParams\" \n                        multiple \n                        placeholder=\"请选择推荐效果参数\"\n                        style=\"width: 100%\"\n                        @change=\"handleEffectParamsSelectChange\">\n                        <el-option\n                            v-for=\"param in availableEffectParamsForTaskType\"\n                            :key=\"param.effect_param_code\"\n                            :label=\"`${param.effect_param_name} (${param.effect_param_code})`\"\n                            :value=\"param.effect_param_name\">\n                        </el-option>\n                    </el-select>\n                    <div v-if=\"availableEffectParamsForTaskType.length === 0 && selectedPlatformIds.length > 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        所选平台暂无可用的效果参数\n                    </div>\n                    <div v-if=\"selectedPlatformIds.length === 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        请先选择关联平台\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"参数关系说明\" prop=\"effect_param_relationships_note\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.effect_param_relationships_note\" placeholder=\"请输入各推荐参数之间的逻辑关系说明\" :rows=\"3\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addTaskTypeDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewTaskType\" :loading=\"addingTaskType\">确定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\n\nexport default {\n    name: 'NewScene',\n    data() {\n        return {\n            activeStep: 0,\n            loading: false,\n            form: {\n                linked_project_team_name: null,\n                linked_task_type_code: null,\n                scene_name: '',\n                scene_description: '',\n                input_platforms: [],\n                output_platforms: [],\n                input_platforms_data: {},\n                output_platforms_data: {},\n                input_data_options: {},\n                output_data_options: {},\n                input_effect_params: {},\n                output_effect_params: {},\n                input_effect_params_config: {},\n                output_effect_params_config: {},\n                updated_prompt: '',\n                scene_running_frequency: '',\n                hour: '',\n                modality: '',\n                platform_modalities: {}, // 新增：平台多模态配置\n                platform_publish_forms: {}, // 新增：平台发布形态配置\n                day: '',\n                weeks: '',\n                stored_strategy_refresh_days: 0,\n                explore_strategy_trigger_days: 365,\n                scene_business_type: '',\n                baseline_data_start_days_ago: 30,\n                baseline_data_exclude_recent_days: 3,\n                min_baseline_sample_count: 3,\n                baseline_refresh_frequency_days: 7\n            },\n            frequencyValue: 30,\n            frequencyUnit: 'minutes',\n            rules: {\n                linked_project_team_name: [\n                    { \n                        required: true, \n                        message: '请选择项目组', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            if (!value) {\n                                callback(new Error('请选择项目组'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                linked_task_type_code: [\n                    { \n                        required: true, \n                        message: '请选择任务类型', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            if (!value) {\n                                callback(new Error('请选择任务类型'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                scene_name: [\n                    { required: true, message: '请输入场景名称', trigger: 'blur' }\n                ],\n                scene_description: [\n                    { required: false, message: '请输入场景描述', trigger: 'blur' }\n                ],\n                input_platforms: [\n                    { required: true, message: '请选择输入平台', trigger: 'change' }\n                ],\n                output_platforms: [\n                    { required: true, message: '请选择输出平台', trigger: 'change' }\n                ],\n                updated_prompt: [\n                    { required: true, message: '请输入AI提示词', trigger: 'blur' }\n                ],\n                scene_running_frequency: [\n                    { required: true, message: '请设置运行频率', trigger: 'change' },\n                    { \n                        type: 'number', \n                        min: 30, \n                        message: '运行频率最小间隔为30分钟', \n                        trigger: 'change' \n                    }\n                ],\n                stored_strategy_refresh_days: [\n                    { required: true, message: '请输入个性化进化策略更新频率', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-365之间的天数', trigger: 'blur' }\n                ],\n                explore_strategy_trigger_days: [\n                    { required: true, message: '请输入AI自行探索频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                scene_business_type: [\n                    { required: true, message: '请选择或输入场景类型', trigger: 'change' }\n                ],\n                baseline_data_start_days_ago: [\n                    { required: true, message: '请输入使用数据的起始天数', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                baseline_data_exclude_recent_days: [\n                    { required: true, message: '请输入排除最近的数据天数', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-30之间的天数', trigger: 'blur' }\n                ],\n                min_baseline_sample_count: [\n                    { required: true, message: '请输入样本量最小阈值', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 10000, message: '请输入1-10000之间的数值', trigger: 'blur' }\n                ],\n                baseline_refresh_frequency_days: [\n                    { required: true, message: '请输入基准线更新频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ]\n            },\n            projectTeams: [],\n            taskTypes: [],\n            selectedInputPlatforms: [],\n            selectedOutputPlatforms: [],\n            addOptionDialogVisible: false,\n            newOptionForm: {\n                option_name: '',\n                option_key: '',\n                platform_id: ''\n            },\n            optionRules: {\n                option_name: [\n                    { required: true, message: '请输入内容名称', trigger: 'blur' }\n                ],\n                option_key: [\n                    { required: true, message: '请输入内容标识（英文）', trigger: 'blur' }\n                ]\n            },\n            addingOption: false,\n            modalityOptions: [],\n            addProjectDialogVisible: false,\n            addTaskTypeDialogVisible: false,\n            newProjectForm: {\n                project_name: ''\n            },\n            newTaskTypeForm: {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            },\n            selectedPlatformIds: [],\n            selectedEffectParams: [],\n            availableEffectParamsForTaskType: [],\n            projectRules: {\n                project_name: [\n                    { required: true, message: '请输入项目名称', trigger: 'blur' }\n                ]\n            },\n            taskTypeRules: {\n                task_type_name: [\n                    { required: true, message: '请输入任务类型名称', trigger: 'blur' }\n                ],\n                task_type_description: [\n                    { required: true, message: '请输入任务类型描述', trigger: 'blur' }\n                ],\n                recommended_effect_param_codes: [\n                    { required: true, message: '请输入推荐效果参数编码', trigger: 'blur' }\n                ],\n                effect_param_relationships_note: [\n                    { required: true, message: '请输入各推荐参数之间的逻辑关系说明', trigger: 'blur' }\n                ]\n            },\n            addingProject: false,\n            addingTaskType: false,\n            sceneTypes: [],\n            loadingInputPlatforms: false,\n            loadingOutputPlatforms: false,\n            tempModalitySelection: {}, // 临时存储模态选择\n            modalityTypes: [], // 从API获取的模态类型\n            publishFormTypes: [] // 从API获取的发布形态类型\n        }\n    },\n    computed: {\n        ...mapState(['platforms']),\n        availablePlatforms() {\n            if (!this.form.linked_task_type_code) {\n                return this.platforms\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.linked_platform_ids) {\n                return this.platforms\n            }\n            \n            const linkedPlatformIds = selectedTaskType.linked_platform_ids.split(',').map(id => parseInt(id.trim()))\n            return this.platforms.filter(platform => linkedPlatformIds.includes(platform.platform_id))\n        },\n        availableEffectParams() {\n            if (!this.form.linked_task_type_code) {\n                return []\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.recommended_effect_param_codes) {\n                return []\n            }\n            \n            try {\n                const params = JSON.parse(selectedTaskType.recommended_effect_param_codes)\n                return Array.isArray(params) ? params : []\n            } catch (error) {\n                console.error('解析效果参数失败:', error)\n                return []\n            }\n        },\n        isPlatformConfigLoading() {\n            if (this.activeStep === 2 && this.loadingInputPlatforms) {\n                return true\n            }\n            if (this.activeStep === 3 && this.loadingOutputPlatforms) {\n                return true\n            }\n            return false\n        }\n    },\n    methods: {\n        ...mapActions(['fetchPlatforms']),\n        // 获取平台可用的发布形态\n        getAvailablePublishForms(platform) {\n            // ECL平台可以选择所有类型\n            if (platform.platform_name && platform.platform_name.toUpperCase().includes('ECL')) {\n                return this.publishFormTypes\n            }\n            // 其他平台根据具体需求返回特定类型，这里暂时返回所有类型\n            // 后续可以根据平台类型进行更精细的控制\n            return this.publishFormTypes\n        },\n        // 处理模态选择\n        handleModalitySelect(platformId, selectedModality) {\n            if (!selectedModality) return\n\n            if (!this.form.platform_modalities[platformId]) {\n                this.$set(this.form.platform_modalities, platformId, [])\n            }\n\n            if (!this.form.platform_modalities[platformId].includes(selectedModality)) {\n                this.form.platform_modalities[platformId].push(selectedModality)\n            }\n\n            // 清空临时选择\n            this.$set(this.tempModalitySelection, platformId, '')\n        },\n        // 移除模态\n        removeModality(platformId, modality) {\n            if (this.form.platform_modalities[platformId]) {\n                const index = this.form.platform_modalities[platformId].indexOf(modality)\n                if (index > -1) {\n                    this.form.platform_modalities[platformId].splice(index, 1)\n                }\n            }\n        },\n        // 移除发布形态\n        removePublishForm(platformId) {\n            this.$set(this.form.platform_publish_forms, platformId, '')\n        },\n        async fetchPlatformOptions(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/option/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform options:', error)\n                return []\n            }\n        },\n        async fetchPlatformEffectParams(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/effectParamCategory/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform effect params:', error)\n                return []\n            }\n        },\n        getAvailableEffectParamsForPlatform(platformId) {\n            const recommendedParams = this.availableEffectParams || []\n            \n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n            \n            if (platformParams.length === 0) {\n                return []\n            }\n            \n            return platformParams.filter(param => recommendedParams.includes(param.effect_param_name))\n        },\n        getFieldComponent(type) {\n            const componentMap = {\n                'string': 'el-input',\n                'password': 'el-input',\n                'select': 'el-select',\n                'multiselect': 'el-select',\n                'number': 'el-input-number',\n                'bool': 'el-switch',\n                'textarea': 'el-input'\n            }\n            return componentMap[type] || 'el-input'\n        },\n        getFieldProps(field) {\n            const props = {\n                placeholder: `请输入${field.label}`\n            }\n            if (field.field_type === 'password') {\n                props.type = 'password'\n            }\n            if (field.field_type === 'textarea') {\n                props.type = 'textarea'\n                props.rows = 3\n            }\n            if (field.field_type === 'select' || field.field_type === 'multiselect') {\n                props.multiple = field.field_type === 'multiselect'\n                props.options = field.options || []\n            }\n            return props\n        },\n        async handleInputPlatformChange(platformIds) {\n            if (this.loadingInputPlatforms) {\n                return\n            }\n            this.loadingInputPlatforms = true\n            \n            try {\n                this.selectedInputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('input_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n                        \n                        const effectParams = await this.fetchPlatformEffectParams(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options,\n                            effectParams: effectParams\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedInputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n                    if (!this.form.input_platforms_data[platformId]) {\n                        this.$set(this.form.input_platforms_data, platformId, {\n                            additional_Information: ''\n                        })\n                    }\n\n                    if (!this.form.input_data_options[platformId]) {\n                        this.$set(this.form.input_data_options, platformId, [])\n                    }\n\n                    if (!this.form.input_effect_params[platformId]) {\n                        this.$set(this.form.input_effect_params, platformId, [])\n                    }\n\n                    const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n                    if (platformWithDetails && platformWithDetails.fields) {\n                        platformWithDetails.fields.forEach(field => {\n                            if (field.required) {\n                                const fieldProp = `input_platforms_data.${platformId}.${field.field_name}`\n                                this.$set(this.rules, fieldProp, [\n                                    { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                ])\n                            }\n                        })\n                    }\n                }\n            } catch (error) {\n                console.error('Error in handleInputPlatformChange:', error)\n                this.$message.error('处理输入平台变化时出错')\n            } finally {\n                this.loadingInputPlatforms = false\n            }\n        },\n        async handleOutputPlatformChange(platformIds) {\n            if (this.loadingOutputPlatforms) {\n                return\n            }\n            this.loadingOutputPlatforms = true\n            \n            try {\n                this.selectedOutputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('output_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedOutputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n                    if (!this.form.output_platforms_data[platformId]) {\n                        this.$set(this.form.output_platforms_data, platformId, {\n                            additional_Information: ''\n                        })\n                    }\n\n                    if (!this.form.output_data_options[platformId]) {\n                        this.$set(this.form.output_data_options, platformId, [])\n                    }\n\n                    // 初始化新的模态和发布形态字段\n                    if (!this.form.platform_modalities[platformId]) {\n                        this.$set(this.form.platform_modalities, platformId, [])\n                    }\n\n                    if (!this.form.platform_publish_forms[platformId]) {\n                        this.$set(this.form.platform_publish_forms, platformId, '')\n                    }\n\n                    const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)\n                    if (platformWithDetails && platformWithDetails.fields) {\n                        platformWithDetails.fields.forEach(field => {\n                            if (field.required) {\n                                const fieldProp = `output_platforms_data.${platformId}.${field.field_name}`\n                                this.$set(this.rules, fieldProp, [\n                                    { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                ])\n                            }\n                        })\n                    }\n                }\n            } catch (error) {\n                console.error('Error in handleOutputPlatformChange:', error)\n                this.$message.error('处理输出平台变化时出错')\n            } finally {\n                this.loadingOutputPlatforms = false\n            }\n        },\n        nextStep() {\n            if (this.isPlatformConfigLoading) {\n                this.$message.warning('平台配置正在加载中，请稍候...')\n                return\n            }\n\n            let fieldsToValidate = []\n\n            switch (this.activeStep) {\n                case 0:\n                    fieldsToValidate = ['linked_project_team_name', 'linked_task_type_code', 'scene_business_type', 'scene_name', 'scene_description']\n                    break\n                case 1:\n                    fieldsToValidate = ['baseline_data_start_days_ago', 'baseline_data_exclude_recent_days', 'min_baseline_sample_count', 'baseline_refresh_frequency_days']\n                    break\n                case 2:\n                    fieldsToValidate = ['input_platforms']\n                    \n                    this.selectedInputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `input_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n                        \n                        const selectedParams = this.form.input_effect_params[platform.platform_id] || []\n                        selectedParams.forEach(paramCode => {\n                            const configPrefix = `input_effect_params_config.${platform.platform_id}.${paramCode}`\n                            fieldsToValidate.push(`${configPrefix}.configured_evaluation_days`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_mean`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_stddev`)\n                        })\n                    })\n                    break\n                case 3:\n                    fieldsToValidate = ['output_platforms']\n\n                    this.selectedOutputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `output_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n\n                        // 验证模态选择\n                        if (!this.form.platform_modalities[platform.platform_id] ||\n                            this.form.platform_modalities[platform.platform_id].length === 0) {\n                            this.$message.error(`请为平台\"${platform.platform_name}\"选择至少一种模态类型`)\n                            return false\n                        }\n\n                        // 验证发布形态选择\n                        if (!this.form.platform_publish_forms[platform.platform_id]) {\n                            this.$message.error(`请为平台\"${platform.platform_name}\"选择发布形态`)\n                            return false\n                        }\n                    })\n                    break\n                case 4:\n                    fieldsToValidate = ['updated_prompt', 'scene_running_frequency', 'stored_strategy_refresh_days', 'explore_strategy_trigger_days']\n                    break\n                default:\n                    break\n            }\n\n            this.validateFields(fieldsToValidate, (valid) => {\n                if (valid) {\n                    this.activeStep++\n                }\n            })\n        },\n        validateFields(fields, callback) {\n            if (fields.length === 0) {\n                callback(true)\n                return\n            }\n\n            console.log('验证字段:', fields)\n            console.log('当前表单数据:', this.form)\n\n            const validationPromises = fields.map(field => {\n                return new Promise((resolve) => {\n                    this.$refs.form.validateField(field, (errorMessage) => {\n                        console.log(`字段 ${field} 验证结果:`, errorMessage)\n                        resolve({ field, errorMessage })\n                    })\n                })\n            })\n\n            Promise.all(validationPromises).then(results => {\n                const hasError = results.some(result => result.errorMessage)\n                console.log('验证结果:', results, '是否有错误:', hasError)\n                callback(!hasError)\n            })\n        },\n        prevStep() {\n            this.activeStep--\n        },\n        async submitForm() {\n            this.$refs.form.validate(async valid => {\n                if (valid) {\n                    try {\n                        const accounts = []\n\n                        const effectParams = []\n\n                        for (const platformId of this.form.input_platforms) {\n                            const platformData = this.form.input_platforms_data[platformId] || {}\n                            const dataOptions = this.form.input_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 1,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n\n                            if (this.form.input_effect_params_config[platformId]) {\n                                Object.values(this.form.input_effect_params_config[platformId]).forEach(paramConfig => {\n                                    effectParams.push({\n                                        platform_id: platformId,\n                                        ...paramConfig\n                                    })\n                                })\n                            }\n                        }\n\n                        for (const platformId of this.form.output_platforms) {\n                            const platformData = this.form.output_platforms_data[platformId] || {}\n                            const dataOptions = this.form.output_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 2,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n                        }\n\n                        const { input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, platform_modalities, platform_publish_forms, ...submitData } = this.form\n                        console.info(input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, platform_modalities, platform_publish_forms)\n\n                        submitData.state = 2;\n                        submitData.create_time = new Date().toLocaleString('sv-SE').replace('T', ' ');\n                        submitData.create_user_id = 0;\n                        submitData.company_id = 0;\n\n                        await this.$http.post('', {\n                            api: '/api/scene/add',\n                            data: submitData,\n                            accounts: accounts,\n                            effect_params: effectParams,\n                            platform_modalities: platform_modalities,\n                            platform_publish_forms: platform_publish_forms\n                        })\n                        this.$message.success('场景创建成功')\n                        this.$router.push('/')\n                    } catch (error) {\n                        this.$message.error('场景创建失败')\n                        console.error('Error creating scene:', error)\n                    }\n                }\n            })\n        },\n        showAddOptionDialog(platformId) {\n            this.newOptionForm = {\n                option_name: '',\n                option_key: '',\n                platform_id: platformId\n            }\n            this.addOptionDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.optionForm && this.$refs.optionForm.clearValidate()\n            })\n        },\n        async addNewOption() {\n            this.$refs.optionForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingOption = true\n                        await this.$http.post('', {\n                            api: '/api/option/add',\n                            data: {\n                                ...this.newOptionForm,\n                                platform_id: this.newOptionForm.platform_id\n                            }\n                        })\n                        this.$message.success('新增数据类型成功')\n                        this.addOptionDialogVisible = false\n                        this.newOptionForm = {\n                            option_name: '',\n                            option_key: '',\n                            platform_id: ''\n                        }\n                        await this.handleInputPlatformChange(this.form.input_platforms)\n                        await this.handleOutputPlatformChange(this.form.output_platforms)\n                    } catch (error) {\n                        this.$message.error('新增数据类型失败')\n                        console.error('Error adding new option:', error)\n                    } finally {\n                        this.addingOption = false\n                    }\n                }\n            })\n        },\n        async fetchModalityOptions() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'modality'\n                    }\n                })\n                this.modalityOptions = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching modality options:', error)\n                this.$message.error('获取模态列表失败')\n            }\n        },\n        // 获取多模态类型列表\n        async fetchModalityTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'modality_types'\n                    }\n                })\n                this.modalityTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching modality types:', error)\n                this.$message.error('获取多模态类型列表失败')\n            }\n        },\n        // 获取发布形态类型列表\n        async fetchPublishFormTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'publish_form_types'\n                    }\n                })\n                this.publishFormTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching publish form types:', error)\n                this.$message.error('获取发布形态类型列表失败')\n            }\n        },\n        async fetchProjectTeams() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/projectTeam/getList'\n                })\n                this.projectTeams = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching project teams:', error)\n                this.$message.error('获取项目组列表失败')\n            }\n        },\n        async fetchTaskTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/taskType/getList'\n                })\n                this.taskTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching task types:', error)\n                this.$message.error('获取任务类型列表失败')\n            }\n        },\n        async fetchSceneTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/sceneType/getList'\n                })\n                this.sceneTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching scene types:', error)\n                this.$message.error('获取场景类型列表失败')\n            }\n        },\n        handleProjectTeamChange() {\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_project_team_name')\n            })\n        },\n        handleTaskTypeChange() {\n            this.form.input_platforms = []\n            this.form.output_platforms = []\n            this.selectedInputPlatforms = []\n            this.selectedOutputPlatforms = []\n            this.form.input_platforms_data = {}\n            this.form.output_platforms_data = {}\n            this.form.input_data_options = {}\n            this.form.output_data_options = {}\n            this.form.input_effect_params = {}\n            this.form.input_effect_params_config = {}\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_task_type_code')\n            })\n        },\n        handleEffectParamsChange(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n\n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n\n            if (platformParams.length == 0) {\n                return;\n            }\n\n            Object.keys(this.rules).forEach(key => {\n                if (key.startsWith(`input_effect_params_config.${platformId}.`)) {\n                    this.$delete(this.rules, key)\n                }\n            })\n\n            selectedParams.forEach(param => {\n                if (!this.form.input_effect_params_config[platformId][param]) {\n\n                    const effectParam = platformParams.find(p => p.effect_param_code === param);\n\n                    this.$set(this.form.input_effect_params_config[platformId], param, {\n                        effect_param_code: effectParam.effect_param_code,\n                        effect_param_name: effectParam.effect_param_name,\n                        configured_evaluation_days: '',\n                        default_baseline_mean: '',\n                        default_baseline_stddev: ''\n                    })\n                }\n\n                const configPrefix = `input_effect_params_config.${platformId}.${param}`\n                this.$set(this.rules, `${configPrefix}.configured_evaluation_days`, [\n                    { required: true, message: '请输入效果实现天数', trigger: 'blur' },\n                    { pattern: /^[\\d,\\s]+$/, message: '请输入有效的天数格式，如：3,5,10', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_mean`, [\n                    { required: true, message: '请输入平均值', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '平均值必须是数字', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_stddev`, [\n                    { required: true, message: '请输入标准差', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '标准差必须是数字', trigger: 'blur' }\n                ])\n            })\n            \n            Object.keys(this.form.input_effect_params_config[platformId]).forEach(param => {\n                if (!selectedParams.includes(param)) {\n                    this.$delete(this.form.input_effect_params_config[platformId], param)\n                }\n            })\n        },\n        getEffectParamsTableData(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n            const config = this.form.input_effect_params_config[platformId] || {}\n            \n            return selectedParams.map(paramCode => ({\n                effect_param_code: paramCode,\n                effect_param_name: (config[paramCode] && config[paramCode].effect_param_name) || '',\n                configured_evaluation_days: (config[paramCode] && config[paramCode].configured_evaluation_days) || '',\n                default_baseline_mean: (config[paramCode] && config[paramCode].default_baseline_mean) || '',\n                default_baseline_stddev: (config[paramCode] && config[paramCode].default_baseline_stddev) || ''\n            }))\n        },\n        updateEffectParamConfig(platformId, paramName, field, value) {\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n            if (!this.form.input_effect_params_config[platformId][paramName]) {\n                this.$set(this.form.input_effect_params_config[platformId], paramName, {})\n            }\n            this.$set(this.form.input_effect_params_config[platformId][paramName], field, value)\n        },\n        getMinValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30\n                case 'hours':\n                    return 1\n                case 'days':\n                    return 1\n                default:\n                    return 1\n            }\n        },\n        getMaxValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 1440\n                case 'hours':\n                    return 24\n                case 'days':\n                    return 365\n                default:\n                    return 1\n            }\n        },\n        getStep() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30\n                case 'hours':\n                    return 1\n                case 'days':\n                    return 1\n                default:\n                    return 1\n            }\n        },\n        calculateTotalMinutes() {\n            let totalMinutes = 0\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    totalMinutes = this.frequencyValue\n                    break\n                case 'hours':\n                    totalMinutes = this.frequencyValue * 60\n                    break\n                case 'days':\n                    totalMinutes = this.frequencyValue * 24 * 60\n                    break\n            }\n            \n            if (totalMinutes < 30) {\n                totalMinutes = 30\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n            }\n            \n            this.form.scene_running_frequency = totalMinutes\n            return totalMinutes\n        },\n        parseFrequencyFromMinutes(minutes) {\n            if (!minutes || minutes < 30) {\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n                return\n            }\n            \n            if (minutes >= 1440 && minutes % 1440 === 0) {\n                this.frequencyValue = minutes / 1440\n                this.frequencyUnit = 'days'\n            } else if (minutes >= 60 && minutes % 60 === 0) {\n                this.frequencyValue = minutes / 60\n                this.frequencyUnit = 'hours'\n            } else {\n                this.frequencyValue = minutes\n                this.frequencyUnit = 'minutes'\n            }\n        },\n        handleFrequencyValueChange() {\n            this.calculateTotalMinutes()\n        },\n        handleFrequencyUnitChange() {\n            const currentMinutes = this.calculateTotalMinutes()\n            \n            this.parseFrequencyFromMinutes(currentMinutes)\n            this.calculateTotalMinutes()\n        },\n        showAddProjectDialog() {\n            this.newProjectForm = {\n                project_name: ''\n            }\n            this.addProjectDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.projectForm && this.$refs.projectForm.clearValidate()\n            })\n        },\n        async addNewProject() {\n            this.$refs.projectForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingProject = true\n                        await this.$http.post('', {\n                            api: '/api/projectTeam/add',\n                            data: this.newProjectForm\n                        })\n                        this.$message.success('新增项目组成功')\n                        this.addProjectDialogVisible = false\n                        await this.fetchProjectTeams()\n                        this.form.linked_project_team_name = this.newProjectForm.project_name\n                    } catch (error) {\n                        this.$message.error('新增项目组失败')\n                        console.error('Error adding new project:', error)\n                    } finally {\n                        this.addingProject = false\n                    }\n                }\n            })\n        },\n        showAddTaskTypeDialog() {\n            this.newTaskTypeForm = {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            }\n            this.selectedPlatformIds = []\n            this.selectedEffectParams = []\n            this.availableEffectParamsForTaskType = []\n            this.addTaskTypeDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.taskTypeForm && this.$refs.taskTypeForm.clearValidate()\n            })\n        },\n        async handlePlatformSelectChange(selectedIds) {\n            this.newTaskTypeForm.linked_platform_ids = selectedIds.join(',')\n            \n            await this.fetchEffectParamsForTaskType(selectedIds)\n        },\n        async fetchEffectParamsForTaskType(platformIds) {\n            if (platformIds.length === 0) {\n                this.availableEffectParamsForTaskType = []\n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                return\n            }\n            \n            try {\n                const platformParamsArrays = []\n                for (const platformId of platformIds) {\n                    const params = await this.fetchPlatformEffectParams(platformId)\n                    platformParamsArrays.push(params)\n                }\n                \n                const allParams = []\n                const seenCodes = new Set()\n                \n                platformParamsArrays.forEach(platformParams => {\n                    platformParams.forEach(param => {\n                        if (!seenCodes.has(param.effect_param_code)) {\n                            seenCodes.add(param.effect_param_code)\n                            allParams.push(param)\n                        }\n                    })\n                })\n                \n                this.availableEffectParamsForTaskType = allParams\n                \n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                \n            } catch (error) {\n                console.error('Error fetching effect params for task type:', error)\n                this.$message.error('获取效果参数失败')\n                this.availableEffectParamsForTaskType = []\n            }\n        },\n        handleEffectParamsSelectChange(selectedCodes) {\n            this.newTaskTypeForm.recommended_effect_param_codes = JSON.stringify(selectedCodes)\n        },\n        async addNewTaskType() {\n            this.$refs.taskTypeForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingTaskType = true\n                        await this.$http.post('', {\n                            api: '/api/taskType/add',\n                            data: this.newTaskTypeForm\n                        })\n                        this.$message.success('新增任务类型成功')\n                        this.addTaskTypeDialogVisible = false\n                        await this.fetchTaskTypes()\n                    } catch (error) {\n                        this.$message.error('新增任务类型失败')\n                        console.error('Error adding new task type:', error)\n                    } finally {\n                        this.addingTaskType = false\n                    }\n                }\n            })\n        }\n    },\n    async created() {\n        await this.fetchPlatforms({ page: 1, pageSize: 100 })\n        await this.fetchProjectTeams()\n        await this.fetchTaskTypes()\n        await this.fetchModalityOptions()\n        await this.fetchSceneTypes()\n        \n        this.calculateTotalMinutes()\n    }\n}\n</script>\n\n<style scoped>\n.new-scene {\n    padding: 20px;\n}\n\n.mt-20 {\n    margin-top: 20px;\n}\n\n.el-steps {\n    margin-bottom: 30px;\n}\n\n.platform-configs {\n    margin-top: 20px;\n    margin-bottom: 20px;\n}\n\n.platform-configs h3 {\n    margin-bottom: 16px;\n    color: #303133;\n    font-size: 16px;\n}\n\n.platform-card {\n    margin-bottom: 16px;\n}\n\n.platform-card:last-child {\n    margin-bottom: 0;\n}\n\n.data-options-container {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n}\n\n.checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.platform-selection-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.navigation-buttons {\n    margin-top: 30px;\n}\n\n.field-container {\n    display: flex;\n    flex-direction: column;\n    gap: 4px;\n}\n\n.field-description {\n    font-size: 12px;\n    color: #909399;\n    line-height: 1.4;\n}\n\n.platform-selection-tip {\n    margin-bottom: 16px;\n}\n\n.no-platforms-tip {\n    margin-top: 16px;\n}\n\n.loading-tip {\n    margin-top: 16px;\n}\n\n.effect-params-container {\n    margin-top: 16px;\n}\n\n.effect-param-checkbox {\n    margin-right: 16px;\n    margin-bottom: 8px;\n}\n\n.effect-params-table {\n    margin-top: 16px;\n}\n\n.effect-params-table h4 {\n    margin-bottom: 12px;\n    color: #303133;\n    font-size: 14px;\n}\n\n\n\n.no-effect-params-tip {\n    margin-bottom: 16px;\n}\n\n.table-header-with-tooltip {\n    cursor: help;\n    display: flex;\n    align-items: center;\n    gap: 4px;\n}\n\n.table-header-with-tooltip .el-icon-question {\n    color: #909399;\n    font-size: 14px;\n}\n\n/* 新增样式：多模态和发布形态选择 */\n.modality-selection-container,\n.publish-form-selection-container {\n    width: 100%;\n}\n\n.selection-row {\n    display: flex;\n    align-items: flex-start;\n    gap: 15px;\n    flex-wrap: wrap;\n}\n\n.selected-tags {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 8px;\n    min-height: 32px;\n    align-items: center;\n}\n</style>"]}]}