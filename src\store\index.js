import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
    state: {
        scenes: [],
        platforms: [],
        currentScene: null,
        loading: false,
    },
    mutations: {
        SET_SCENES(state, scenes) {
            state.scenes = scenes
        },
        SET_PLATFORMS(state, platforms) {
            state.platforms = platforms
        },
        SET_CURRENT_SCENE(state, scene) {
            state.currentScene = scene
        },
        SET_LOADING(state, loading) {
            state.loading = loading
        }
    },
    actions: {
        async fetchScenes({ commit }, { page = 1, pageSize = 15 }) {
            commit('SET_LOADING', true)
            try {
                const response = await this._vm.$http.post('', {
                    api: '/api/scene/getList',
                    param: { page, pageSize }
                })
                console.log(response)
                commit('SET_SCENES', response.data.data)
            } catch (error) {
                console.error('Error fetching scenes:', error)
            } finally {
                commit('SET_LOADING', false)
            }
        },
        async fetchRecentScenes({ commit }) {
            commit('SET_LOADING', true)
            try {
                const response = await this._vm.$http.post('', {
                    api: '/api/scene/getRecent'
                })
                console.log(response)
                commit('SET_SCENES', response.data.data)
            } catch (error) {
                console.error('Error fetching recent scenes:', error)
            } finally {
                commit('SET_LOADING', false)
            }
        },
        async fetchPlatforms({ commit }, { page = 1, pageSize = 15 }) {
            try {
                const response = await this._vm.$http.post('', {
                    api: '/api/platform/getList',
                    param: { page, pageSize }
                })
                commit('SET_PLATFORMS', response.data.data)
            } catch (error) {
                console.error('Error fetching platforms:', error)
            }
        }
    }
}) 