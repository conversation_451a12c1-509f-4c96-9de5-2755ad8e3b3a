{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\scene\\EditScene.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\scene\\EditScene.vue", "mtime": 1754036632809}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["EditScene.vue"], "names": [], "mappings": ";AA6pBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "EditScene.vue", "sourceRoot": "src/views/scene", "sourcesContent": ["<template>\n    <div class=\"edit-scene\">\n        <el-breadcrumb separator=\"/\" class=\"edit-breadcrumb\" style=\"margin-bottom: 18px;\">\n            <el-breadcrumb-item :to=\"{ path: '/scene/manage' }\">场景管理</el-breadcrumb-item>\n            <el-breadcrumb-item :to=\"{ path: '/scene/edit/'+ form.scene_id }\">{{ form.scene_name }}</el-breadcrumb-item>\n            <el-breadcrumb-item>场景编辑</el-breadcrumb-item>\n        </el-breadcrumb>\n        <div class=\"edit-actions\">\n            <el-button type=\"primary\" size=\"small\" @click=\"openAIOptimize\" :disabled=\"!form.scene_id\">AI优化提示词</el-button>\n            <el-button type=\"danger\" size=\"small\" @click=\"handleDeleteScene\" :disabled=\"!form.scene_id\">删除场景</el-button>\n        </div>\n        <el-steps :active=\"activeStep\" finish-status=\"success\" simple>\n            <el-step title=\"基本信息\" />\n            <el-step title=\"计算基准线配置\" />\n            <el-step title=\"数据输入平台\" />\n            <el-step title=\"数据输出平台\" />\n            <el-step title=\"其他设置\" />\n        </el-steps>\n\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"150px\" class=\"mt-20\" v-loading=\"loading\">\n            <div v-show=\"activeStep === 0\">\n                <el-form-item label=\"场景名称\" prop=\"scene_name\">\n                    <el-input v-model=\"form.scene_name\" placeholder=\"请输入场景名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型\" prop=\"linked_task_type_code\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_task_type_code\" placeholder=\"请选择任务类型\" style=\"flex: 1;\" @change=\"handleTaskTypeChange\">\n                            <el-option\n                                v-for=\"taskType in taskTypes\"\n                                :key=\"taskType.task_type_code\"\n                                :label=\"taskType.task_type_name\"\n                                :value=\"taskType.task_type_code\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddTaskTypeDialog\">\n                            新增任务类型\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景类型\" prop=\"scene_business_type\">\n                    <el-select \n                        v-model=\"form.scene_business_type\" \n                        placeholder=\"请选择或输入场景类型\" \n                        filterable \n                        allow-create \n                        default-first-option\n                        style=\"width: 100%\">\n                        <el-option\n                            v-for=\"sceneType in sceneTypes\"\n                            :key=\"sceneType.scene_type_id || sceneType.scene_type_code\"\n                            :label=\"sceneType.scene_type_name\"\n                            :value=\"sceneType.scene_type_name\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"项目组\" prop=\"linked_project_team_name\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-select v-model=\"form.linked_project_team_name\" placeholder=\"请选择项目组\" style=\"flex: 1;\" @change=\"handleProjectTeamChange\">\n                            <el-option\n                                v-for=\"project in projectTeams\"\n                                :key=\"project.project_id\"\n                                :label=\"project.project_name\"\n                                :value=\"project.project_name\">\n                            </el-option>\n                        </el-select>\n                        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddProjectDialog\">\n                            新增项目组\n                        </el-button>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"场景描述\" prop=\"scene_description\">\n                    <el-input type=\"textarea\" v-model=\"form.scene_description\" placeholder=\"请输入场景描述\"\n                        :rows=\"4\"></el-input>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 1\">\n                <el-form-item label=\"使用数据的起始天数\" prop=\"baseline_data_start_days_ago\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_start_days_ago\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线使用数据的起始天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"排除最近的数据天数\" prop=\"baseline_data_exclude_recent_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_data_exclude_recent_days\"\n                            :min=\"0\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入天数\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"T-基线排除最近的数据天数\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <!-- <el-form-item label=\"样本总数\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input \n                            value=\"将根据上述配置自动计算\" \n                            disabled \n                            style=\"width: 300px;\">\n                        </el-input>\n                        <span style=\"color: #909399; font-size: 12px;\">基于起始天数和排除天数范围内的数据量</span>\n                    </div>\n                </el-form-item> -->\n                <el-form-item label=\"样本量最小阈值\" prop=\"min_baseline_sample_count\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.min_baseline_sample_count\"\n                            :min=\"1\"\n                            :max=\"10000\"\n                            :step=\"1\"\n                            placeholder=\"请输入样本量\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">个</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"一个场景在一次计算基准线时，所需要的最小样本量\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"基准线更新频率\" prop=\"baseline_refresh_frequency_days\">\n                    <div style=\"display: flex; align-items: center; gap: 10px;\">\n                        <el-input-number\n                            v-model=\"form.baseline_refresh_frequency_days\"\n                            :min=\"1\"\n                            :max=\"365\"\n                            :step=\"1\"\n                            placeholder=\"请输入频率\"\n                            style=\"width: 200px\">\n                        </el-input-number>\n                        <span style=\"color: #909399;\">天</span>\n                        <el-tooltip effect=\"dark\" placement=\"top\" content=\"评估效果的基准线更新的频率\">\n                            <i class=\"el-icon-question\" style=\"color: #909399; cursor: help;\"></i>\n                        </el-tooltip>\n                    </div>\n                </el-form-item>\n            </div>\n\n            <div v-show=\"activeStep === 2\">\n                <el-form-item label=\"选择输入平台\" prop=\"input_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.input_platforms\" @change=\"handleInputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingInputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedInputPlatforms && selectedInputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingInputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输入平台配置</h3>\n                    <el-card v-for=\"platform in selectedInputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <component :is=\"getFieldComponent(field.field_type)\"\n                                v-model=\"form.input_platforms_data[platform.platform_id][field.field_name]\"\n                                v-bind=\"getFieldProps(field)\"></component>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据类型\" :prop=\"'input_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.input_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\"\n                                    @click=\"showAddOptionDialog(platform.platform_id)\">\n                                    新增数据类型\n                                </el-button>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"效果参数配置\">\n                            <div class=\"effect-params-container\">\n                                <div v-if=\"getAvailableEffectParamsForPlatform(platform.platform_id).length === 0\" class=\"no-effect-params-tip\">\n                                    <el-alert\n                                        title=\"该平台暂无可用的效果参数\"\n                                        type=\"info\"\n                                        :closable=\"false\"\n                                        show-icon>\n                                    </el-alert>\n                                </div>\n                                <el-checkbox-group v-else v-model=\"form.input_effect_params[platform.platform_id]\" @change=\"handleEffectParamsChange(platform.platform_id)\">\n                                    <el-checkbox v-for=\"param in getAvailableEffectParamsForPlatform(platform.platform_id)\" :key=\"param.effect_param_code\" :label=\"param.effect_param_code\" class=\"effect-param-checkbox\">\n                                        {{ param.effect_param_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                \n                                <div v-if=\"form.input_effect_params[platform.platform_id] && form.input_effect_params[platform.platform_id].length > 0\" class=\"effect-params-table\">\n                                    <h4>参数配置详情</h4>\n                                    <el-table :data=\"getEffectParamsTableData(platform.platform_id)\" border>\n                                        <el-table-column prop=\"effect_param_name\" label=\"参数名称\" width=\"120\"></el-table-column>\n                                        <el-table-column prop=\"configured_evaluation_days\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"系统会获取发布时间在'T-基线'范围内，且已满足各参数的Tij 值的样本总数量。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *效果实现天数\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.configured_evaluation_days`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.configured_evaluation_days\" \n                                                        placeholder=\"如：3,5,10\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'configured_evaluation_days', scope.row.configured_evaluation_days)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_mean\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"μi是选取的样本量的效果数据里，该参数的平均值。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算μi，而是会使用您输入的缺省状态下的μi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *平均值\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_mean`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input\n                                                        v-model=\"scope.row.default_baseline_mean\" \n                                                        placeholder=\"如：0\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_mean', scope.row.default_baseline_mean)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                        <el-table-column prop=\"default_baseline_stddev\" width=\"200\">\n                                            <template slot=\"header\">\n                                                <el-tooltip effect=\"dark\" placement=\"top\" content=\"σi是选取的样本量的效果数据里，该参数的标准差。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算σi，而是会使用您输入的缺省状态下的σi。\">\n                                                    <span class=\"table-header-with-tooltip\">\n                                                        *标准差\n                                                        <i class=\"el-icon-question\"></i>\n                                                    </span>\n                                                </el-tooltip>\n                                            </template>\n                                            <template slot-scope=\"scope\">\n                                                <el-form-item \n                                                    :prop=\"`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_stddev`\"\n                                                    style=\"margin-bottom: 0;\">\n                                                    <el-input \n                                                        v-model=\"scope.row.default_baseline_stddev\" \n                                                        placeholder=\"如：1\"\n                                                        @change=\"updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_stddev', scope.row.default_baseline_stddev)\">\n                                                    </el-input>\n                                                </el-form-item>\n                                            </template>\n                                        </el-table-column>\n                                    </el-table>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'input_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.input_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n            </div>\n\n            <div v-show=\"activeStep === 3\">\n                <el-form-item label=\"选择输出平台\" prop=\"output_platforms\">\n                    <div v-if=\"!form.linked_task_type_code\" class=\"platform-selection-tip\">\n                        <el-alert\n                            title=\"请先在第一步选择任务类型\"\n                            type=\"info\"\n                            :closable=\"false\"\n                            show-icon>\n                        </el-alert>\n                    </div>\n                    <div v-else class=\"platform-selection-container\">\n                        <el-checkbox-group v-model=\"form.output_platforms\" @change=\"handleOutputPlatformChange\"\n                            class=\"platform-checkbox-group\">\n                            <el-checkbox v-for=\"platform in availablePlatforms\" :key=\"platform.platform_id\"\n                                :label=\"platform.platform_id\" class=\"platform-checkbox-item\">\n                                {{ platform.platform_name }}\n                            </el-checkbox>\n                        </el-checkbox-group>\n                        <div v-if=\"loadingOutputPlatforms\" class=\"loading-tip\">\n                            <el-alert\n                                title=\"正在加载平台配置，请稍候...\"\n                                type=\"info\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                        <div v-if=\"availablePlatforms.length === 0\" class=\"no-platforms-tip\">\n                            <el-alert\n                                title=\"当前任务类型没有关联的平台\"\n                                type=\"warning\"\n                                :closable=\"false\"\n                                show-icon>\n                            </el-alert>\n                        </div>\n                    </div>\n                </el-form-item>\n\n                <div v-if=\"selectedOutputPlatforms && selectedOutputPlatforms.length > 0\" class=\"platform-configs\" v-loading=\"loadingOutputPlatforms\" element-loading-text=\"正在加载平台配置...\">\n                    <h3>输出平台配置</h3>\n                    <el-card v-for=\"platform in selectedOutputPlatforms\" :key=\"platform.platform_id\"\n                        class=\"platform-card\">\n                        <div slot=\"header\">\n                            <span>{{ platform.platform_name }}</span>\n                        </div>\n\n                        <el-form-item v-for=\"field in platform.fields\" :key=\"field.field_name\" :label=\"field.label\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.' + field.field_name\">\n                            <component :is=\"getFieldComponent(field.field_type)\"\n                                v-model=\"form.output_platforms_data[platform.platform_id][field.field_name]\"\n                                v-bind=\"getFieldProps(field)\"></component>\n                        </el-form-item>\n\n                        <el-form-item label=\"数据内容\" :prop=\"'output_data_options.' + platform.platform_id\">\n                            <div class=\"data-options-container\">\n                                <el-checkbox-group v-model=\"form.output_data_options[platform.platform_id]\"\n                                    class=\"checkbox-group\">\n                                    <el-checkbox v-for=\"option in platform.options\" :key=\"option.option_key\"\n                                        :label=\"option.option_key\" class=\"checkbox-item\">\n                                        {{ option.option_name }}\n                                    </el-checkbox>\n                                </el-checkbox-group>\n                                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\"\n                                    @click=\"showAddOptionDialog(platform.platform_id)\">\n                                    新增数据内容\n                                </el-button>\n                            </div>\n                        </el-form-item>\n\n\n\n                        <el-form-item label=\"多模态内容\" :prop=\"'platform_modalities.' + platform.platform_id\">\n                            <div class=\"modality-selection-container\">\n                                <div class=\"selection-row\">\n                                    <el-select\n                                        v-model=\"tempModalitySelection[platform.platform_id]\"\n                                        placeholder=\"请选择模态类型\"\n                                        style=\"width: 300px;\"\n                                        @change=\"handleModalitySelect(platform.platform_id, $event)\">\n                                        <el-option\n                                            v-for=\"modality in modalityTypes\"\n                                            :key=\"modality.dict_name\"\n                                            :label=\"modality.dict_name\"\n                                            :value=\"modality.dict_name\"\n                                            :disabled=\"form.platform_modalities[platform.platform_id] && form.platform_modalities[platform.platform_id].includes(modality.dict_name)\">\n                                        </el-option>\n                                    </el-select>\n                                    <div class=\"selected-tags\">\n                                        <el-tag\n                                            v-for=\"modality in form.platform_modalities[platform.platform_id] || []\"\n                                            :key=\"modality\"\n                                            closable\n                                            @close=\"removeModality(platform.platform_id, modality)\"\n                                            style=\"margin-right: 8px; margin-bottom: 8px;\">\n                                            {{ modality }}\n                                        </el-tag>\n                                    </div>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"发布形态\" :prop=\"'platform_publish_forms.' + platform.platform_id\">\n                            <div class=\"publish-form-selection-container\">\n                                <div class=\"selection-row\">\n                                    <el-select\n                                        v-model=\"form.platform_publish_forms[platform.platform_id]\"\n                                        placeholder=\"请选择发布形态\"\n                                        style=\"width: 300px;\">\n                                        <el-option\n                                            v-for=\"publishForm in getAvailablePublishForms(platform)\"\n                                            :key=\"publishForm.dict_name\"\n                                            :label=\"publishForm.dict_name\"\n                                            :value=\"publishForm.dict_name\">\n                                        </el-option>\n                                    </el-select>\n                                    <div class=\"selected-tags\">\n                                        <el-tag\n                                            v-if=\"form.platform_publish_forms[platform.platform_id]\"\n                                            closable\n                                            @close=\"removePublishForm(platform.platform_id)\"\n                                            style=\"margin-left: 8px;\">\n                                            {{ form.platform_publish_forms[platform.platform_id] }}\n                                        </el-tag>\n                                    </div>\n                                </div>\n                            </div>\n                        </el-form-item>\n\n                        <el-form-item label=\"补充信息\"\n                            :prop=\"'output_platforms_data.' + platform.platform_id + '.additional_Information'\">\n                            <el-input v-model=\"form.output_platforms_data[platform.platform_id].additional_Information\"\n                                type=\"textarea\" :rows=\"5\" placeholder=\"请输入补充信息\">\n                            </el-input>\n                        </el-form-item>\n                    </el-card>\n                </div>\n\n                <!-- <el-form-item label=\"模态\" prop=\"modality\">\n                    <el-select v-model=\"form.modality\" placeholder=\"请选择模态\">\n                        <el-option\n                            v-for=\"item in modalityOptions\"\n                            :key=\"item.dict_name\"\n                            :label=\"item.dict_name\"\n                            :value=\"item.dict_name\">\n                        </el-option>\n                    </el-select>\n                </el-form-item> -->\n            </div>\n\n            <div v-show=\"activeStep === 4\">\n                <el-form-item label=\"运行频率\" prop=\"scene_running_frequency\">\n                    <div style=\"display: flex; gap: 10px; align-items: center;\">\n                        <el-input-number\n                            v-model=\"frequencyValue\"\n                            :min=\"getMinValue()\"\n                            :max=\"getMaxValue()\"\n                            :step=\"getStep()\"\n                            placeholder=\"请输入数值\"\n                            style=\"width: 150px\"\n                            @change=\"handleFrequencyValueChange\">\n                        </el-input-number>\n                        <el-select \n                            v-model=\"frequencyUnit\" \n                            placeholder=\"请选择单位\"\n                            style=\"width: 120px\"\n                            @change=\"handleFrequencyUnitChange\">\n                            <el-option label=\"分钟\" value=\"minutes\"></el-option>\n                            <el-option label=\"小时\" value=\"hours\"></el-option>\n                            <el-option label=\"天\" value=\"days\"></el-option>\n                        </el-select>\n                        <span style=\"color: #909399; font-size: 12px;\">\n                            (最小间隔30分钟)\n                        </span>\n                    </div>\n                </el-form-item>\n                <!-- <el-form-item label=\"运行时间\" prop=\"time_config\">\n                    <el-row>\n                        <el-col :span=\"8\">\n                            <el-time-picker\n                                v-model=\"form.hour\"\n                                format=\"HH:00\"\n                                :picker-options=\"{\n                                    selectableRange: '00:00:00 - 23:00:00',\n                                    format: 'HH:00'\n                                }\"\n                                placeholder=\"请选择运行时间\"\n                                style=\"width: 100%\">\n                            </el-time-picker>\n                        </el-col>\n                        <el-col :span=\"8\" v-if=\"form.scene_running_frequency === '每月一次'\">\n                            <el-select v-model=\"form.day\" placeholder=\"请选择运行日期\" style=\"width: 100%\">\n                                <el-option\n                                    v-for=\"day in 31\"\n                                    :key=\"day\"\n                                    :label=\"`${day}日`\"\n                                    :value=\"day\">\n                                </el-option>\n                            </el-select>\n                        </el-col>\n                        <el-col :span=\"8\" v-if=\"['每周一次', '每两周一次'].includes(form.scene_running_frequency)\">\n                            <el-select v-model=\"form.weeks\" placeholder=\"请选择运行星期\" style=\"width: 100%\">\n                                <el-option label=\"星期一\" value=\"1\"></el-option>\n                                <el-option label=\"星期二\" value=\"2\"></el-option>\n                                <el-option label=\"星期三\" value=\"3\"></el-option>\n                                <el-option label=\"星期四\" value=\"4\"></el-option>\n                                <el-option label=\"星期五\" value=\"5\"></el-option>\n                                <el-option label=\"星期六\" value=\"6\"></el-option>\n                                <el-option label=\"星期日\" value=\"0\"></el-option>\n                            </el-select>\n                        </el-col>\n                    </el-row>\n                </el-form-item> -->\n                <el-form-item label=\"个性化进化更新频率\" prop=\"stored_strategy_refresh_days\">\n                    <el-input-number\n                        v-model=\"form.stored_strategy_refresh_days\"\n                        :min=\"0\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">建议您设为0</span>\n                </el-form-item>\n                <el-form-item label=\"AI自行探索频率\" prop=\"explore_strategy_trigger_days\">\n                    <el-input-number\n                        v-model=\"form.explore_strategy_trigger_days\"\n                        :min=\"1\"\n                        :max=\"365\"\n                        :step=\"1\"\n                        placeholder=\"请输入天数\"\n                        style=\"width: 200px\">\n                    </el-input-number>\n                    <span style=\"margin-left: 8px; color: #909399;\">天</span>\n                    <span style=\"margin-left: 16px; color: #909399; font-size: 12px;\">目前我们的探索模式暂未上线，建议您先将Z值设为365天</span>\n                </el-form-item>\n                <el-form-item label=\"AI提示词\" prop=\"updated_prompt\">\n                    <el-input type=\"textarea\" v-model=\"form.updated_prompt\" placeholder=\"请输入AI提示词\" :rows=\"10\"></el-input>\n                </el-form-item>\n            </div>\n\n            <el-form-item class=\"navigation-buttons\">\n                <el-button v-if=\"activeStep > 0\" @click=\"prevStep\">上一步</el-button>\n                <el-button \n                    v-if=\"activeStep < 4\" \n                    type=\"primary\" \n                    @click=\"nextStep\"\n                    :disabled=\"isPlatformConfigLoading\"\n                    :loading=\"isPlatformConfigLoading\">\n                    {{ isPlatformConfigLoading ? '配置加载中...' : '下一步' }}\n                </el-button>\n                <el-button v-if=\"activeStep === 4\" type=\"primary\" @click=\"submitForm\">保存</el-button>\n            </el-form-item>\n        </el-form>\n\n        <el-dialog title=\"新增数据内容\" :visible.sync=\"addOptionDialogVisible\" width=\"500px\">\n            <el-form ref=\"optionForm\" :model=\"newOptionForm\" :rules=\"optionRules\" label-width=\"120px\">\n                <el-form-item label=\"内容名称\" prop=\"option_name\">\n                    <el-input v-model=\"newOptionForm.option_name\" placeholder=\"请输入内容名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"内容标识\" prop=\"option_key\">\n                    <el-input v-model=\"newOptionForm.option_key\" placeholder=\"请输入内容标识（英文）\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addOptionDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewOption\" :loading=\"addingOption\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增项目组\" :visible.sync=\"addProjectDialogVisible\" width=\"500px\">\n            <el-form ref=\"projectForm\" :model=\"newProjectForm\" :rules=\"projectRules\" label-width=\"120px\">\n                <el-form-item label=\"项目名称\" prop=\"project_name\">\n                    <el-input v-model=\"newProjectForm.project_name\" placeholder=\"请输入项目名称\"></el-input>\n                </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addProjectDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewProject\" :loading=\"addingProject\">确定</el-button>\n            </div>\n        </el-dialog>\n\n        <el-dialog title=\"新增任务类型\" :visible.sync=\"addTaskTypeDialogVisible\" width=\"600px\">\n            <el-form ref=\"taskTypeForm\" :model=\"newTaskTypeForm\" :rules=\"taskTypeRules\" label-width=\"140px\">\n                <el-form-item label=\"任务类型名称\" prop=\"task_type_name\">\n                    <el-input v-model=\"newTaskTypeForm.task_type_name\" placeholder=\"请输入任务类型名称\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"任务类型描述\" prop=\"task_type_description\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.task_type_description\" placeholder=\"请输入任务类型描述\" :rows=\"3\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"关联平台\" prop=\"linked_platform_ids\">\n                    <el-select \n                        v-model=\"selectedPlatformIds\" \n                        multiple \n                        placeholder=\"请选择关联平台\"\n                        style=\"width: 100%\"\n                        @change=\"handlePlatformSelectChange\">\n                        <el-option\n                            v-for=\"platform in platforms\"\n                            :key=\"platform.platform_id\"\n                            :label=\"platform.platform_name\"\n                            :value=\"platform.platform_id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"推荐效果参数\" prop=\"recommended_effect_param_codes\">\n                    <el-select \n                        v-model=\"selectedEffectParams\" \n                        multiple \n                        placeholder=\"请选择推荐效果参数\"\n                        style=\"width: 100%\"\n                        @change=\"handleEffectParamsSelectChange\">\n                        <el-option\n                            v-for=\"param in availableEffectParamsForTaskType\"\n                            :key=\"param.effect_param_code\"\n                            :label=\"`${param.effect_param_name} (${param.effect_param_code})`\"\n                            :value=\"param.effect_param_name\">\n                        </el-option>\n                    </el-select>\n                    <div v-if=\"availableEffectParamsForTaskType.length === 0 && selectedPlatformIds.length > 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        所选平台暂无可用的效果参数\n                    </div>\n                    <div v-if=\"selectedPlatformIds.length === 0\" style=\"margin-top: 8px; color: #909399; font-size: 12px;\">\n                        请先选择关联平台\n                    </div>\n                </el-form-item>\n                <el-form-item label=\"参数关系说明\" prop=\"effect_param_relationships_note\">\n                    <el-input type=\"textarea\" v-model=\"newTaskTypeForm.effect_param_relationships_note\" placeholder=\"请输入各推荐参数之间的逻辑关系说明\" :rows=\"3\"></el-input>\n                </el-form-item>\n                <!-- <el-form-item label=\"是否需要外部内容\" prop=\"is_content_from_external\">\n                    <el-select v-model=\"newTaskTypeForm.is_content_from_external\" placeholder=\"请选择\">\n                        <el-option label=\"是\" value=\"1\"></el-option>\n                        <el-option label=\"否\" value=\"0\"></el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"是否需要双边偏好\" prop=\"is_bilateral_pref_consideration_needed\">\n                    <el-select v-model=\"newTaskTypeForm.is_bilateral_pref_consideration_needed\" placeholder=\"请选择\">\n                        <el-option label=\"是\" value=\"1\"></el-option>\n                        <el-option label=\"否\" value=\"0\"></el-option>\n                    </el-select>\n                </el-form-item> -->\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addTaskTypeDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"addNewTaskType\" :loading=\"addingTaskType\">确定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\n\nexport default {\n    name: 'EditScene',\n    data() {\n        return {\n            activeStep: 0,\n            loading: false,\n            form: {\n                scene_id: null,\n                linked_project_team_name: null,\n                linked_task_type_code: null,\n                scene_name: '',\n                scene_description: '',\n                input_platforms: [],\n                output_platforms: [],\n                input_platforms_data: {},\n                output_platforms_data: {},\n                input_data_options: {},\n                output_data_options: {},\n                input_effect_params: {},\n                input_effect_params_config: {},\n                updated_prompt: '',\n                scene_running_frequency: '',\n                hour: '',\n                modality: '',\n                platform_modalities: {}, // 新增：平台多模态配置\n                platform_publish_forms: {}, // 新增：平台发布形态配置\n                day: '',\n                weeks: '',\n                stored_strategy_refresh_days: 0,\n                explore_strategy_trigger_days: 365,\n                scene_business_type: '',\n                baseline_data_start_days_ago: 30,\n                baseline_data_exclude_recent_days: 3,\n                min_baseline_sample_count: 3,\n                baseline_refresh_frequency_days: 7\n            },\n            frequencyValue: 30,\n            frequencyUnit: 'minutes',\n            rules: {\n                linked_project_team_name: [\n                    { \n                        required: true, \n                        message: '请选择项目组', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            console.log('验证项目组:', value)\n                            if (!value) {\n                                callback(new Error('请选择项目组'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                linked_task_type_code: [\n                    { \n                        required: true, \n                        message: '请选择任务类型', \n                        trigger: ['change', 'blur'],\n                        validator: (rule, value, callback) => {\n                            console.log('验证任务类型:', value)\n                            if (!value) {\n                                callback(new Error('请选择任务类型'))\n                            } else {\n                                callback()\n                            }\n                        }\n                    }\n                ],\n                scene_name: [\n                    { required: true, message: '请输入场景名称', trigger: 'blur' }\n                ],\n                scene_description: [\n                    { required: false, message: '请输入场景描述', trigger: 'blur' }\n                ],\n                input_platforms: [\n                    { required: true, message: '请选择输入平台', trigger: 'change' }\n                ],\n                output_platforms: [\n                    { required: true, message: '请选择输出平台', trigger: 'change' }\n                ],\n                updated_prompt: [\n                    { required: true, message: '请输入AI提示词', trigger: 'blur' }\n                ],\n                scene_running_frequency: [\n                    { required: true, message: '请设置运行频率', trigger: 'change' },\n                    { \n                        type: 'number', \n                        min: 30, \n                        message: '运行频率最小间隔为30分钟', \n                        trigger: 'change' \n                    }\n                ],\n                stored_strategy_refresh_days: [\n                    { required: true, message: '请输入个性化进化策略更新频率', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-365之间的天数', trigger: 'blur' }\n                ],\n                explore_strategy_trigger_days: [\n                    { required: true, message: '请输入AI自行探索频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                scene_business_type: [\n                    { required: true, message: '请选择场景类型', trigger: 'change' }\n                ],\n                baseline_data_start_days_ago: [\n                    { required: true, message: '请输入使用数据的起始天数', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ],\n                baseline_data_exclude_recent_days: [\n                    { required: true, message: '请输入排除最近的数据天数', trigger: 'blur' },\n                    { type: 'number', min: 0, max: 365, message: '请输入0-30之间的天数', trigger: 'blur' }\n                ],\n                min_baseline_sample_count: [\n                    { required: true, message: '请输入样本量最小阈值', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 10000, message: '请输入1-10000之间的数值', trigger: 'blur' }\n                ],\n                baseline_refresh_frequency_days: [\n                    { required: true, message: '请输入基准线更新频率', trigger: 'blur' },\n                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }\n                ]\n            },\n            projectTeams: [], \n            taskTypes: [], \n            modalityOptions: [], \n            selectedInputPlatforms: [],\n            selectedOutputPlatforms: [],\n            sceneInputAccountsData: {}, \n            sceneOutputAccountsData: {}, \n            effectParamsData: {},\n            addOptionDialogVisible: false,\n            newOptionForm: {\n                option_name: '',\n                option_key: '',\n                platform_id: ''\n            },\n            optionRules: {\n                option_name: [\n                    { required: true, message: '请输入内容名称', trigger: 'blur' }\n                ],\n                option_key: [\n                    { required: true, message: '请输入内容标识（英文）', trigger: 'blur' }\n                ]\n            },\n            addingOption: false,\n            addProjectDialogVisible: false,\n            addTaskTypeDialogVisible: false,\n            newProjectForm: {\n                project_name: ''\n            },\n            newTaskTypeForm: {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            },\n            selectedPlatformIds: [], \n            selectedEffectParams: [], \n            availableEffectParamsForTaskType: [], \n            projectRules: {\n                project_name: [\n                    { required: true, message: '请输入项目名称', trigger: 'blur' }\n                ]\n            },\n            taskTypeRules: {\n                task_type_name: [\n                    { required: true, message: '请输入任务类型名称', trigger: 'blur' }\n                ],\n                task_type_description: [\n                    { required: true, message: '请输入任务类型描述', trigger: 'blur' }\n                ],\n                recommended_effect_param_codes: [\n                    { required: true, message: '请输入推荐效果参数编码', trigger: 'blur' }\n                ],\n                effect_param_relationships_note: [\n                    { required: true, message: '请输入各推荐参数之间的逻辑关系说明', trigger: 'blur' }\n                ]\n            },\n            addingProject: false,\n            addingTaskType: false,\n            sceneTypes: [],\n            loadingInputPlatforms: false,\n            loadingOutputPlatforms: false,\n            tempModalitySelection: {}, // 临时存储模态选择\n            modalityTypes: [], // 从API获取的模态类型\n            publishFormTypes: [] // 从API获取的发布形态类型\n        }\n    },\n    computed: {\n        ...mapState(['platforms']),\n        availablePlatforms() {\n            if (!this.platforms || !this.form.linked_task_type_code) {\n                return this.platforms\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.linked_platform_ids) {\n                return this.platforms\n            }\n            \n            const linkedPlatformIds = selectedTaskType.linked_platform_ids.split(',').map(id => parseInt(id.trim()))\n            return this.platforms.filter(platform => linkedPlatformIds.includes(platform.platform_id))\n        },\n        availableEffectParams() {\n            if (!this.form.linked_task_type_code|| !this.taskTypes) {\n                return []\n            }\n            \n            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)\n            if (!selectedTaskType || !selectedTaskType.recommended_effect_param_codes) {\n                return []\n            }\n            \n            try {\n                const params = JSON.parse(selectedTaskType.recommended_effect_param_codes)\n                return Array.isArray(params) ? params : []\n            } catch (error) {\n                console.error('解析效果参数失败:', error)\n                return []\n            }\n        },\n        isPlatformConfigLoading() {\n            if (this.activeStep === 2 && this.loadingInputPlatforms) {\n                return true\n            }\n            if (this.activeStep === 3 && this.loadingOutputPlatforms) {\n                return true\n            }\n            return false\n        }\n    },\n    methods: {\n        ...mapActions(['fetchPlatforms']),\n        // 获取平台可用的发布形态\n        getAvailablePublishForms(platform) {\n            // ECL平台可以选择所有类型\n            if (platform.platform_name && platform.platform_name.toUpperCase().includes('ECL')) {\n                return this.publishFormTypes\n            }\n            // 其他平台根据具体需求返回特定类型，这里暂时返回所有类型\n            // 后续可以根据平台类型进行更精细的控制\n            return this.publishFormTypes\n        },\n        // 处理模态选择\n        handleModalitySelect(platformId, selectedModality) {\n            if (!selectedModality) return\n\n            if (!this.form.platform_modalities[platformId]) {\n                this.$set(this.form.platform_modalities, platformId, [])\n            }\n\n            if (!this.form.platform_modalities[platformId].includes(selectedModality)) {\n                this.form.platform_modalities[platformId].push(selectedModality)\n            }\n\n            // 清空临时选择\n            this.$set(this.tempModalitySelection, platformId, '')\n        },\n        // 移除模态\n        removeModality(platformId, modality) {\n            if (this.form.platform_modalities[platformId]) {\n                const index = this.form.platform_modalities[platformId].indexOf(modality)\n                if (index > -1) {\n                    this.form.platform_modalities[platformId].splice(index, 1)\n                }\n            }\n        },\n        // 移除发布形态\n        removePublishForm(platformId) {\n            this.$set(this.form.platform_publish_forms, platformId, '')\n        },\n        async fetchPlatformOptions(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/option/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform options:', error)\n                return []\n            }\n        },\n        async fetchPlatformEffectParams(platformId) {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/effectParamCategory/getList',\n                    param: {\n                        platform_id: platformId\n                    }\n                })\n                return response.data.data || []\n            } catch (error) {\n                console.error('Error fetching platform effect params:', error)\n                return []\n            }\n        },\n        getAvailableEffectParamsForPlatform(platformId) {\n            const recommendedParams = this.availableEffectParams || []\n            \n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n\n            console.info(platformParams)\n\n            if (platformParams.length === 0) {\n                return []\n            }\n            \n            // const platformParamCodes = platformParams.map(param => param.effect_param_name || param.effect_param_code)\n            \n            return platformParams.filter(param => recommendedParams.includes(param.effect_param_name))\n        },\n        getFieldComponent(type) {\n            const componentMap = {\n                'string': 'el-input',\n                'password': 'el-input',\n                'select': 'el-select',\n                'multiselect': 'el-select',\n                'number': 'el-input-number',\n                'bool': 'el-switch',\n                'textarea': 'el-input'\n            }\n            return componentMap[type] || 'el-input'\n        },\n        getFieldProps(field) {\n            const props = {\n                placeholder: `请输入${field.label}`\n            }\n            if (field.field_type === 'password') {\n                props.type = 'password'\n            }\n            if (field.field_type === 'textarea') {\n                props.type = 'textarea'\n                props.rows = 3\n            }\n            if (field.field_type === 'select' || field.field_type === 'multiselect') {\n                props.multiple = field.field_type === 'multiselect'\n                props.options = field.options || []\n            }\n            return props\n        },\n        async handleInputPlatformChange(platformIds) {\n            if (this.loadingInputPlatforms) {\n                return\n            }\n            this.loadingInputPlatforms = true\n            \n            try {\n                this.selectedInputPlatforms = []\n\n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('input_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n\n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n                        \n                        const effectParams = await this.fetchPlatformEffectParams(platformId)\n\n                        console.info(\"effectParams\", effectParams);\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options,\n                            effectParams: effectParams\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedInputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n\n                        if (!this.form.input_platforms_data[platformId]) {\n                            this.$set(this.form.input_platforms_data, platformId, {\n                                additional_Information: ''\n                            })\n                        }\n\n                        if (!this.form.input_data_options[platformId]) {\n                            this.$set(this.form.input_data_options, platformId, [])\n                        }\n\n                        if (!this.form.input_effect_params[platformId]) {\n                            this.$set(this.form.input_effect_params, platformId, [])\n                        }\n\n                        const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n                        if (platformWithDetails && platformWithDetails.fields) {\n                            platformWithDetails.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `input_platforms_data.${platformId}.${field.field_name}`\n                                    this.$set(this.rules, fieldProp, [\n                                        { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                    ])\n                                }\n                            })\n                        }\n\n                        if (this.sceneInputAccountsData[platformId]) {\n                            const accountData = this.sceneInputAccountsData[platformId]\n                            const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n\n                            for (const field of (platformWithDetails && platformWithDetails.fields) || []) {\n                                if (accountData[field.field_name] !== undefined) {\n                                    this.$set(this.form.input_platforms_data[platformId], field.field_name, accountData[field.field_name])\n                                }\n                            }\n\n                            if (accountData.additional_Information !== undefined) {\n                                this.$set(this.form.input_platforms_data[platformId], 'additional_Information', accountData.additional_Information)\n                            }\n\n                            if (accountData.adding_data_types && accountData.adding_data_types !== '无') {\n                                const selectedOptions = accountData.adding_data_types.split(',').filter(item => item.trim())\n                                this.$set(this.form.input_data_options, platformId, selectedOptions)\n                            }\n                        }\n\n                        if (this.effectParamsData) {\n                            const platformEffectParams = Object.values(this.effectParamsData).filter(param => param.platform_id === platformId)\n                            \n                            if (platformEffectParams.length > 0) {\n                                if (!this.form.input_effect_params[platformId]) {\n                                    this.$set(this.form.input_effect_params, platformId, [])\n                                }\n                                \n                                if (!this.form.input_effect_params_config[platformId]) {\n                                    this.$set(this.form.input_effect_params_config, platformId, {})\n                                }\n                                \n                                const selectedParams = []\n                                platformEffectParams.forEach(param => {\n                                    selectedParams.push(param.effect_param_code)\n                                    this.$set(this.form.input_effect_params_config[platformId], param.effect_param_code, {\n                                        effect_param_code: param.effect_param_code,\n                                        effect_param_name: param.effect_param_name,\n                                        configured_evaluation_days: param.configured_evaluation_days || '',\n                                        default_baseline_mean: param.default_baseline_mean || '',\n                                        default_baseline_stddev: param.default_baseline_stddev || ''\n                                    })\n                                })\n                                \n                                this.$set(this.form.input_effect_params, platformId, selectedParams)\n                            }\n                        }\n                }\n            } catch (error) {\n                console.error('Error in handleInputPlatformChange:', error)\n                this.$message.error('处理输入平台变化时出错')\n            } finally {\n                this.loadingInputPlatforms = false\n            }\n        },\n        async handleOutputPlatformChange(platformIds) {\n            if (this.loadingOutputPlatforms) {\n                return\n            }\n            this.loadingOutputPlatforms = true\n            \n            try {\n                this.selectedOutputPlatforms = []\n                \n                Object.keys(this.rules).forEach(key => {\n                    if (key.startsWith('output_platforms_data.') && !key.endsWith('.additional_Information')) {\n                        this.$delete(this.rules, key)\n                    }\n                })\n                \n                const platformsWithDetails = []\n                for (const platformId of platformIds) {\n                    const platform = this.platforms.find(p => p.platform_id === platformId)\n                    if (!platform) continue\n                    \n                    try {\n                        const detailResponse = await this.$http.post('', {\n                            api: '/api/platform/getDetail',\n                            param: { platform_id: platformId }\n                        })\n                        \n                        const options = await this.fetchPlatformOptions(platformId)\n\n                        platformsWithDetails.push({\n                            ...detailResponse.data.data,\n                            options: options\n                        })\n                    } catch (error) {\n                        console.error('Error fetching platform detail:', error)\n                        this.$message.error(`获取平台${platform.platform_name}详情失败`)\n                    }\n                }\n                \n                this.selectedOutputPlatforms = platformsWithDetails\n                \n                for (const platformId of platformIds) {\n\n                        if (!this.form.output_platforms_data[platformId]) {\n                            this.$set(this.form.output_platforms_data, platformId, {\n                                additional_Information: ''\n                            })\n                        }\n\n                        if (!this.form.output_data_options[platformId]) {\n                            this.$set(this.form.output_data_options, platformId, [])\n                        }\n\n                        // 初始化新的模态和发布形态字段\n                        if (!this.form.platform_modalities[platformId]) {\n                            this.$set(this.form.platform_modalities, platformId, [])\n                        }\n\n                        if (!this.form.platform_publish_forms[platformId]) {\n                            this.$set(this.form.platform_publish_forms, platformId, '')\n                        }\n\n                        const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)\n                        if (platformWithDetails && platformWithDetails.fields) {\n                            platformWithDetails.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `output_platforms_data.${platformId}.${field.field_name}`\n                                    this.$set(this.rules, fieldProp, [\n                                        { required: true, message: `请输入${field.label}`, trigger: 'blur' }\n                                    ])\n                                }\n                            })\n                        }\n\n                        if (this.sceneOutputAccountsData[platformId]) {\n                            const accountData = this.sceneOutputAccountsData[platformId]\n                            const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)\n\n                            for (const field of (platformWithDetails && platformWithDetails.fields) || []) {\n                                if (accountData[field.field_name] !== undefined) {\n                                    this.$set(this.form.output_platforms_data[platformId], field.field_name, accountData[field.field_name])\n                                }\n                            }\n\n                            if (accountData.additional_Information !== undefined) {\n                                this.$set(this.form.output_platforms_data[platformId], 'additional_Information', accountData.additional_Information)\n                            }\n\n                            if (accountData.adding_data_types && accountData.adding_data_types !== '无') {\n                                const selectedOptions = accountData.adding_data_types.split(',').filter(item => item.trim())\n                                this.$set(this.form.output_data_options, platformId, selectedOptions)\n                            }\n                        }\n                }\n            } catch (error) {\n                console.error('Error in handleOutputPlatformChange:', error)\n                this.$message.error('处理输出平台变化时出错')\n            } finally {\n                this.loadingOutputPlatforms = false\n            }\n        },\n        nextStep() {\n            if (this.isPlatformConfigLoading) {\n                this.$message.warning('平台配置正在加载中，请稍候...')\n                return\n            }\n\n            let fieldsToValidate = []\n\n            switch (this.activeStep) {\n                case 0:\n                    fieldsToValidate = ['linked_project_team_name', 'linked_task_type_code', 'scene_business_type', 'scene_name', 'scene_description']\n                    break\n                case 1:\n                    fieldsToValidate = ['baseline_data_start_days_ago', 'baseline_data_exclude_recent_days', 'min_baseline_sample_count', 'baseline_refresh_frequency_days']\n                    break\n                case 2:\n                    fieldsToValidate = ['input_platforms']\n                    \n                    this.selectedInputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `input_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n                        \n                        const selectedParams = this.form.input_effect_params[platform.platform_id] || []\n                        selectedParams.forEach(paramCode => {\n                            const configPrefix = `input_effect_params_config.${platform.platform_id}.${paramCode}`\n                            fieldsToValidate.push(`${configPrefix}.configured_evaluation_days`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_mean`)\n                            fieldsToValidate.push(`${configPrefix}.default_baseline_stddev`)\n                        })\n                    })\n                    break\n                case 3:\n                    fieldsToValidate = ['output_platforms']\n\n                    this.selectedOutputPlatforms.forEach(platform => {\n                        if (platform.fields) {\n                            platform.fields.forEach(field => {\n                                if (field.required) {\n                                    const fieldProp = `output_platforms_data.${platform.platform_id}.${field.field_name}`\n                                    fieldsToValidate.push(fieldProp)\n                                }\n                            })\n                        }\n\n                        // 验证模态选择\n                        if (!this.form.platform_modalities[platform.platform_id] ||\n                            this.form.platform_modalities[platform.platform_id].length === 0) {\n                            this.$message.error(`请为平台\"${platform.platform_name}\"选择至少一种模态类型`)\n                            return false\n                        }\n\n                        // 验证发布形态选择\n                        if (!this.form.platform_publish_forms[platform.platform_id]) {\n                            this.$message.error(`请为平台\"${platform.platform_name}\"选择发布形态`)\n                            return false\n                        }\n                    })\n                    break\n                case 4:\n                    fieldsToValidate = ['updated_prompt', 'scene_running_frequency', 'stored_strategy_refresh_days', 'explore_strategy_trigger_days']\n                    break\n                default:\n                    break\n            }\n\n            this.validateFields(fieldsToValidate, (valid) => {\n                if (valid) {\n                    this.activeStep++\n                }\n            })\n        },\n        validateFields(fields, callback) {\n            if (fields.length === 0) {\n                callback(true)\n                return\n            }\n\n            const validationPromises = fields.map(field => {\n                return new Promise((resolve) => {\n                    this.$refs.form.validateField(field, (errorMessage) => {\n                        console.log(`字段 ${field} 验证结果:`, errorMessage)\n                        resolve({ field, errorMessage })\n                    })\n                })\n            })\n\n            Promise.all(validationPromises).then(results => {\n                const hasError = results.some(result => result.errorMessage)\n                console.log('验证结果:', results, '是否有错误:', hasError)\n                callback(!hasError)\n            })\n        },\n        prevStep() {\n            this.activeStep--\n        },\n        async submitForm() {\n            this.$refs.form.validate(async valid => {\n                if (valid) {\n                    try {\n                        const accounts = []\n                        const effectParams = []\n\n                        for (const platformId of this.form.input_platforms) {\n                            const platformData = this.form.input_platforms_data[platformId] || {}\n                            const dataOptions = this.form.input_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 1,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n\n                            if (this.form.input_effect_params_config[platformId]) {\n                                Object.values(this.form.input_effect_params_config[platformId]).forEach(paramConfig => {\n                                    effectParams.push({\n                                        platform_id: platformId,\n                                        ...paramConfig\n                                    })\n                                })\n                            }\n                        }\n\n                        for (const platformId of this.form.output_platforms) {\n                            const platformData = this.form.output_platforms_data[platformId] || {}\n                            const dataOptions = this.form.output_data_options[platformId] || []\n\n                            accounts.push({\n                                operate_type: 2,\n                                platform_id: platformId,\n                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),\n                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',\n                                ...platformData\n                            })\n                        }\n\n                        const { input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, platform_modalities, platform_publish_forms, ...submitData } = this.form;\n                        console.info(input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, platform_modalities, platform_publish_forms)\n\n                        submitData.update_time = new Date().toLocaleString('sv-SE').replace('T', ' ');\n\n                        await this.$http.post('', {\n                            api: '/api/scene/update',\n                            data: {\n                                ...submitData,\n                                scene_id: this.$route.params.id\n                            },\n                            accounts: accounts,\n                            effect_params: effectParams,\n                            platform_modalities: platform_modalities,\n                            platform_publish_forms: platform_publish_forms\n                        })\n                        this.$message.success('场景更新成功')\n                        this.$router.push('/')\n                    } catch (error) {\n                        this.$message.error('场景更新失败')\n                        console.error('Error updating scene:', error)\n                    }\n                }\n            })\n        },\n        async fetchSceneDetail() {\n            if (!this.$route.params.id) {\n                return\n            }\n\n            this.loading = true\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/scene/getDetail',\n                    param: { scene_id: this.$route.params.id }\n                })\n                const sceneData = response.data.data\n                const accounts = response.data.accounts || []\n                const effectParams = response.data.effect_params || []\n\n                this.sceneInputAccountsData = {}\n                this.sceneOutputAccountsData = {}\n                accounts.forEach(account => {\n                    if (account.operate_type === 1) {\n                        this.sceneInputAccountsData[account.platform_id] = account\n                    } else if (account.operate_type === 2) {\n                        this.sceneOutputAccountsData[account.platform_id] = account\n                    }\n                })\n                const inputPlatforms = accounts\n                    .filter(platform => platform.operate_type === 1)\n                    .map(platform => platform.platform_id)\n\n                const outputPlatforms = accounts\n                    .filter(platform => platform.operate_type === 2)\n                    .map(platform => platform.platform_id)\n\n                this.effectParamsData = {}\n                effectParams.forEach(param => {\n                    this.effectParamsData[param.effect_param_code] = param\n                })\n\n                console.info(\"effectParamsData\", this.effectParamsData);\n\n                this.form = {\n                    ...this.form,\n                    ...sceneData,\n                    linked_task_type_code: parseInt(sceneData.linked_task_type_code),\n                    input_platforms: inputPlatforms,\n                    output_platforms: outputPlatforms\n                }\n\n                if (sceneData.scene_running_frequency) {\n                    this.parseFrequencyFromMinutes(parseInt(sceneData.scene_running_frequency))\n                } else {\n                    this.frequencyValue = 30\n                    this.frequencyUnit = 'minutes'\n                }\n                this.calculateTotalMinutes()\n\n                await this.$nextTick()\n                await this.handleInputPlatformChange(this.form.input_platforms)\n                await this.handleOutputPlatformChange(this.form.output_platforms)\n\n            } catch (error) {\n                this.$message.error('获取场景详情失败')\n                console.error('Error fetching scene detail:', error)\n            } finally {\n                this.loading = false\n            }\n        },\n        showAddOptionDialog(platformId) {\n            this.newOptionForm = {\n                option_name: '',\n                option_key: '',\n                platform_id: platformId\n            }\n            this.addOptionDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.optionForm && this.$refs.optionForm.clearValidate()\n            })\n        },\n        async addNewOption() {\n            this.$refs.optionForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingOption = true\n                        await this.$http.post('', {\n                            api: '/api/option/add',\n                            data: {\n                                ...this.newOptionForm,\n                                platform_id: this.newOptionForm.platform_id\n                            }\n                        })\n                        this.$message.success('新增数据类型成功')\n                        this.addOptionDialogVisible = false\n                        this.newOptionForm = {\n                            option_name: '',\n                            option_key: '',\n                            platform_id: ''\n                        }\n                        await this.handleInputPlatformChange(this.form.input_platforms)\n                        await this.handleOutputPlatformChange(this.form.output_platforms)\n                    } catch (error) {\n                        this.$message.error('新增数据类型失败')\n                        console.error('Error adding new option:', error)\n                    } finally {\n                        this.addingOption = false\n                    }\n                }\n            })\n        },\n        openAIOptimize() {\n            if (!this.form.scene_id) return\n            const url = `https://acpfbbeg.manus.space/?scene_id=${this.form.scene_id}`\n            window.open(url, '_blank')\n        },\n        async handleDeleteScene() {\n            if (!this.form.scene_id) return\n            try {\n                await this.$confirm('确认删除该场景吗？', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                })\n                await this.$http.post('', {\n                    api: '/api/scene/delete',\n                    param: { scene_id: this.form.scene_id }\n                })\n                this.$message.success('删除成功')\n                this.$router.push('/scene/manage')\n            } catch (error) {\n                if (error !== 'cancel') {\n                    this.$message.error('删除失败')\n                    console.error('Error deleting scene:', error)\n                }\n            }\n        },\n        async fetchModalityOptions() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'modality'\n                    }\n                })\n                this.modalityOptions = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching modality options:', error)\n                this.$message.error('获取模态列表失败')\n            }\n        },\n        // 获取多模态类型列表\n        async fetchModalityTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'modality_types'\n                    }\n                })\n                this.modalityTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching modality types:', error)\n                this.$message.error('获取多模态类型列表失败')\n            }\n        },\n        // 获取发布形态类型列表\n        async fetchPublishFormTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/dict/getList',\n                    param: {\n                        dict_type: 'publish_form_types'\n                    }\n                })\n                this.publishFormTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching publish form types:', error)\n                this.$message.error('获取发布形态类型列表失败')\n            }\n        },\n        async fetchProjectTeams() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/projectTeam/getList'\n                })\n                this.projectTeams = response.data.data || []\n                console.log('项目组数据:', this.projectTeams)\n            } catch (error) {\n                console.error('Error fetching project teams:', error)\n                this.$message.error('获取项目组列表失败')\n            }\n        },\n        async fetchTaskTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/taskType/getList'\n                })\n                this.taskTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching task types:', error)\n                this.$message.error('获取任务类型列表失败')\n            }\n        },\n        async fetchSceneTypes() {\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/sceneType/getList'\n                })\n                this.sceneTypes = response.data.data || []\n            } catch (error) {\n                console.error('Error fetching scene types:', error)\n                this.$message.error('获取场景类型列表失败')\n            }\n        },\n        handleProjectTeamChange() {\n            console.log('项目组改变:', this.form.linked_project_team_name)\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_project_team_name')\n            })\n        },\n        handleTaskTypeChange() {\n            this.form.input_platforms = []\n            this.form.output_platforms = []\n            this.selectedInputPlatforms = []\n            this.selectedOutputPlatforms = []\n            this.form.input_platforms_data = {}\n            this.form.output_platforms_data = {}\n            this.form.input_data_options = {}\n            this.form.output_data_options = {}\n            this.form.input_effect_params = {}\n            this.form.input_effect_params_config = {}\n            this.form.platform_modalities = {}\n            this.form.platform_publish_forms = {}\n            this.tempModalitySelection = {}\n            this.$nextTick(() => {\n                this.$refs.form.clearValidate('linked_task_type_code')\n            })\n        },\n        handleEffectParamsChange(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n\n            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)\n            const platformParams = platform ? (platform.effectParams || []) : []\n\n            if (platformParams.length == 0) {\n                return;\n            }\n\n            Object.keys(this.rules).forEach(key => {\n                if (key.startsWith(`input_effect_params_config.${platformId}.`)) {\n                    this.$delete(this.rules, key)\n                }\n            })\n\n            selectedParams.forEach(param => {\n                if (!this.form.input_effect_params_config[platformId][param]) {\n\n                    const effectParam = platformParams.find(p => p.effect_param_code === param);\n\n                    this.$set(this.form.input_effect_params_config[platformId], param, {\n                        effect_param_code: effectParam.effect_param_code,\n                        effect_param_name: effectParam.effect_param_name,\n                        configured_evaluation_days: '',\n                        default_baseline_mean: '',\n                        default_baseline_stddev: ''\n                    })\n                }\n\n                const configPrefix = `input_effect_params_config.${platformId}.${param}`\n                this.$set(this.rules, `${configPrefix}.configured_evaluation_days`, [\n                    { required: true, message: '请输入效果实现天数', trigger: 'blur' },\n                    { pattern: /^[\\d,\\s]+$/, message: '请输入有效的天数格式，如：3,5,10', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_mean`, [\n                    { required: true, message: '请输入平均值', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '平均值必须是数字', trigger: 'blur' }\n                ])\n                this.$set(this.rules, `${configPrefix}.default_baseline_stddev`, [\n                    { required: true, message: '请输入标准差', trigger: 'blur' },\n                    { pattern: /^(-?[1-9]\\d*(\\.\\d*[1-9])?)|(-?0\\.\\d*[1-9])$/, message: '标准差必须是数字', trigger: 'blur' }\n                ])\n            })\n            \n            Object.keys(this.form.input_effect_params_config[platformId]).forEach(param => {\n                if (!selectedParams.includes(param)) {\n                    this.$delete(this.form.input_effect_params_config[platformId], param)\n                }\n            })\n        },\n        getEffectParamsTableData(platformId) {\n            const selectedParams = this.form.input_effect_params[platformId] || []\n            const config = this.form.input_effect_params_config[platformId] || {}\n            \n            const ret = selectedParams.map(paramCode => ({\n                effect_param_code: paramCode,\n                effect_param_name: (config[paramCode] && config[paramCode].effect_param_name) || '',\n                configured_evaluation_days: (config[paramCode] && config[paramCode].configured_evaluation_days) || '',\n                default_baseline_mean: (config[paramCode] && config[paramCode].default_baseline_mean) || 0,\n                default_baseline_stddev: (config[paramCode] && config[paramCode].default_baseline_stddev) || 1\n            }))\n\n            console.info(\"ret\", ret);\n            return ret;\n        },\n        updateEffectParamConfig(platformId, paramName, field, value) {\n            if (!this.form.input_effect_params_config[platformId]) {\n                this.$set(this.form.input_effect_params_config, platformId, {})\n            }\n            if (!this.form.input_effect_params_config[platformId][paramName]) {\n                this.$set(this.form.input_effect_params_config[platformId], paramName, {})\n            }\n            this.$set(this.form.input_effect_params_config[platformId][paramName], field, value)\n        },\n        getMinValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30 \n                case 'hours':\n                    return 1 \n                case 'days':\n                    return 1 \n                default:\n                    return 1\n            }\n        },\n        getMaxValue() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 1440\n                case 'hours':\n                    return 24 \n                case 'days':\n                    return 365 \n                default:\n                    return 1\n            }\n        },\n        getStep() {\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    return 30 \n                case 'hours':\n                    return 1 \n                case 'days':\n                    return 1 \n                default:\n                    return 1\n            }\n        },\n        calculateTotalMinutes() {\n            let totalMinutes = 0\n            switch (this.frequencyUnit) {\n                case 'minutes':\n                    totalMinutes = this.frequencyValue\n                    break\n                case 'hours':\n                    totalMinutes = this.frequencyValue * 60\n                    break\n                case 'days':\n                    totalMinutes = this.frequencyValue * 24 * 60\n                    break\n            }\n            \n            if (totalMinutes < 30) {\n                totalMinutes = 30\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n            }\n            \n            this.form.scene_running_frequency = totalMinutes\n            return totalMinutes\n        },\n        parseFrequencyFromMinutes(minutes) {\n            if (!minutes || minutes < 30) {\n                this.frequencyValue = 30\n                this.frequencyUnit = 'minutes'\n                return\n            }\n            \n            if (minutes >= 1440 && minutes % 1440 === 0) {\n                this.frequencyValue = minutes / 1440\n                this.frequencyUnit = 'days'\n            } else if (minutes >= 60 && minutes % 60 === 0) {\n                this.frequencyValue = minutes / 60\n                this.frequencyUnit = 'hours'\n            } else {\n                this.frequencyValue = minutes\n                this.frequencyUnit = 'minutes'\n            }\n        },\n        handleFrequencyValueChange() {\n            this.calculateTotalMinutes()\n        },\n        handleFrequencyUnitChange() {\n            const currentMinutes = this.calculateTotalMinutes()\n            this.parseFrequencyFromMinutes(currentMinutes)\n            this.calculateTotalMinutes()\n        },\n        showAddProjectDialog() {\n            this.newProjectForm = {\n                project_name: ''\n            }\n            this.addProjectDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.projectForm && this.$refs.projectForm.clearValidate()\n            })\n        },\n        async addNewProject() {\n            this.$refs.projectForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingProject = true\n                        await this.$http.post('', {\n                            api: '/api/projectTeam/add',\n                            data: this.newProjectForm\n                        })\n                        this.$message.success('新增项目组成功')\n                        this.addProjectDialogVisible = false\n                        await this.fetchProjectTeams()\n                        if (this.projectTeams.length > 0) {\n                            const newProject = this.projectTeams.find(project => project.project_name === this.newProjectForm.project_name)\n                            if (newProject) {\n                                this.form.linked_project_team_name = newProject.project_name\n                            }\n                        }\n                    } catch (error) {\n                        this.$message.error('新增项目组失败')\n                        console.error('Error adding new project:', error)\n                    } finally {\n                        this.addingProject = false\n                    }\n                }\n            })\n        },\n        showAddTaskTypeDialog() {\n            this.newTaskTypeForm = {\n                task_type_name: '',\n                task_type_description: '',\n                recommended_effect_param_codes: '',\n                effect_param_relationships_note: '',\n                is_content_from_external: '1',\n                is_bilateral_pref_consideration_needed: '0',\n                linked_platform_ids: '',\n                task_type_status: 1,\n                task_type_owner: 2\n            }\n            this.selectedPlatformIds = [] \n            this.selectedEffectParams = [] \n            this.availableEffectParamsForTaskType = []\n            this.addTaskTypeDialogVisible = true\n            this.$nextTick(() => {\n                this.$refs.taskTypeForm && this.$refs.taskTypeForm.clearValidate()\n            })\n        },\n        async handlePlatformSelectChange(selectedIds) {\n            this.newTaskTypeForm.linked_platform_ids = selectedIds.join(',')\n            \n            await this.fetchEffectParamsForTaskType(selectedIds)\n        },\n        async fetchEffectParamsForTaskType(platformIds) {\n            if (platformIds.length === 0) {\n                this.availableEffectParamsForTaskType = []\n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                return\n            }\n            \n            try {\n                const platformParamsArrays = []\n                for (const platformId of platformIds) {\n                    const params = await this.fetchPlatformEffectParams(platformId)\n                    platformParamsArrays.push(params)\n                }\n                \n                const allParams = []\n                const seenCodes = new Set()\n                \n                platformParamsArrays.forEach(platformParams => {\n                    platformParams.forEach(param => {\n                        if (!seenCodes.has(param.effect_param_code)) {\n                            seenCodes.add(param.effect_param_code)\n                            allParams.push(param)\n                        }\n                    })\n                })\n                \n                this.availableEffectParamsForTaskType = allParams\n                \n                this.selectedEffectParams = []\n                this.newTaskTypeForm.recommended_effect_param_codes = ''\n                \n            } catch (error) {\n                console.error('Error fetching effect params for task type:', error)\n                this.$message.error('获取效果参数失败')\n                this.availableEffectParamsForTaskType = []\n            }\n        },\n        handleEffectParamsSelectChange(selectedCodes) {\n            this.newTaskTypeForm.recommended_effect_param_codes = JSON.stringify(selectedCodes)\n        },\n        async addNewTaskType() {\n            this.$refs.taskTypeForm.validate(async valid => {\n                if (valid) {\n                    try {\n                        this.addingTaskType = true\n                        await this.$http.post('', {\n                            api: '/api/taskType/add',\n                            data: this.newTaskTypeForm\n                        })\n                        this.$message.success('新增任务类型成功')\n                        this.addTaskTypeDialogVisible = false\n                        await this.fetchTaskTypes()\n                    } catch (error) {\n                        this.$message.error('新增任务类型失败')\n                        console.error('Error adding new task type:', error)\n                    } finally {\n                        this.addingTaskType = false\n                    }\n                }\n            })\n        }\n    },\n    async created() {\n        await this.fetchPlatforms({ page: 1, pageSize: 100 })\n        await this.fetchProjectTeams()\n        await this.fetchTaskTypes()\n        await this.fetchSceneTypes()\n        await this.fetchSceneDetail()\n        await this.fetchModalityOptions()\n        await this.fetchModalityTypes()\n        await this.fetchPublishFormTypes()\n    }\n}\n</script>\n\n<style scoped>\n.edit-scene {\n    padding: 20px;\n}\n\n.mt-20 {\n    margin-top: 20px;\n}\n\n.el-steps {\n    margin-bottom: 30px;\n}\n\n.platform-configs {\n    margin-top: 20px;\n    margin-bottom: 20px;\n}\n\n.platform-configs h3 {\n    margin-bottom: 16px;\n    color: #303133;\n    font-size: 16px;\n}\n\n.platform-card {\n    margin-bottom: 16px;\n}\n\n.platform-card:last-child {\n    margin-bottom: 0;\n}\n\n.data-options-container {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n}\n\n.checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.platform-selection-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-group {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.platform-checkbox-item {\n    margin-right: 0;\n    margin-bottom: 0;\n    white-space: nowrap;\n}\n\n.edit-breadcrumb {\n    font-size: 14px;\n    margin-bottom: 18px;\n}\n\n.edit-actions {\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n    margin-bottom: 18px;\n}\n\n.navigation-buttons {\n    margin-top: 30px;\n}\n\n.platform-selection-tip {\n    margin-bottom: 16px;\n}\n\n.no-platforms-tip {\n    margin-top: 16px;\n}\n\n.loading-tip {\n    margin-top: 16px;\n}\n\n.effect-params-container {\n    margin-top: 16px;\n}\n\n.effect-param-checkbox {\n    margin-right: 16px;\n    margin-bottom: 8px;\n}\n\n.effect-params-table {\n    margin-top: 16px;\n}\n\n.effect-params-table h4 {\n    margin-bottom: 12px;\n    color: #303133;\n    font-size: 14px;\n}\n\n\n\n.table-header-with-tooltip {\n    cursor: help;\n    display: flex;\n    align-items: center;\n    gap: 4px;\n}\n\n.table-header-with-tooltip .el-icon-question {\n    color: #609399;\n    font-size: 14px;\n}\n\n/* 新增样式：多模态和发布形态选择 */\n.modality-selection-container,\n.publish-form-selection-container {\n    width: 100%;\n}\n\n.selection-row {\n    display: flex;\n    align-items: flex-start;\n    gap: 15px;\n    flex-wrap: wrap;\n}\n\n.selected-tags {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 8px;\n    min-height: 32px;\n    align-items: center;\n}\n</style>"]}]}