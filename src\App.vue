<template>
    <div id="app">
        <el-container>
            <el-aside width="200px">
                <div class="logo">
                    <img v-if="logoImage" :src="logoImage" alt="Logo" class="logo-image" />
                    <i v-else class="el-icon-s-data logo-icon"></i>
                    <h3>AI自适应系统</h3>
                </div>
                <el-menu mode="vertical" router :default-active="$route.path" background-color="#304156"
                    text-color="#bfcbd9" active-text-color="#409EFF">
                    <el-menu-item index="/">
                        <i class="el-icon-pie-chart"></i>
                        <span slot="title">总览</span>
                    </el-menu-item>
                    <el-menu-item index="/scene/new">
                        <i class="el-icon-plus"></i>
                        <span slot="title">新建场景</span>
                    </el-menu-item>
                    <el-menu-item index="/scene/manage">
                        <i class="el-icon-setting"></i>
                        <span slot="title">场景管理</span>
                    </el-menu-item>
                </el-menu>
            </el-aside>
            <el-main>
                <router-view></router-view>
            </el-main>
        </el-container>
    </div>
</template>

<script>
export default {
    name: 'App',
    data() {
        return {
            logoImage: null
        }
    }
}
</script>

<style>
#app {
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
        'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #2c3e50;
}

.el-container {
    height: 100vh;
}

.el-aside {
    background-color: #304156;
    color: white;
}

.logo {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid #434a50;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.logo-image {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
    border-radius: 4px;
}

.logo-icon {
    font-size: 32px;
    color: #409EFF;
    margin-bottom: 10px;
}

.logo h3 {
    color: #bfcbd9;
    margin: 0;
    font-size: 16px;
    font-weight: 400;
}

.el-menu {
    border-right: none;
}

.el-main {
    padding: 20px;
    background-color: #f5f7fa;
    overflow-y: auto;
}
</style>