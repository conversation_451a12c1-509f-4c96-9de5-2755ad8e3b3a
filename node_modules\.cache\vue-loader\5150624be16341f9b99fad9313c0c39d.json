{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\App.vue?vue&type=template&id=7ba5bd90", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\App.vue", "mtime": 1754017590000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754032187340}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgaWQ9ImFwcCI+CiAgICA8ZWwtY29udGFpbmVyPgogICAgICAgIDxlbC1hc2lkZSB3aWR0aD0iMjAwcHgiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJsb2dvIj4KICAgICAgICAgICAgICAgIDxpbWcgdi1pZj0ibG9nb0ltYWdlIiA6c3JjPSJsb2dvSW1hZ2UiIGFsdD0iTG9nbyIgY2xhc3M9ImxvZ28taW1hZ2UiIC8+CiAgICAgICAgICAgICAgICA8aSB2LWVsc2UgY2xhc3M9ImVsLWljb24tcy1kYXRhIGxvZ28taWNvbiI+PC9pPgogICAgICAgICAgICAgICAgPGgzPkFJ6Ieq6YCC5bqU57O757ufPC9oMz4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxlbC1tZW51IG1vZGU9InZlcnRpY2FsIiByb3V0ZXIgOmRlZmF1bHQtYWN0aXZlPSIkcm91dGUucGF0aCIgYmFja2dyb3VuZC1jb2xvcj0iIzMwNDE1NiIKICAgICAgICAgICAgICAgIHRleHQtY29sb3I9IiNiZmNiZDkiIGFjdGl2ZS10ZXh0LWNvbG9yPSIjNDA5RUZGIj4KICAgICAgICAgICAgICAgIDxlbC1tZW51LWl0ZW0gaW5kZXg9Ii8iPgogICAgICAgICAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLXBpZS1jaGFydCI+PC9pPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIHNsb3Q9InRpdGxlIj7mgLvop4g8L3NwYW4+CiAgICAgICAgICAgICAgICA8L2VsLW1lbnUtaXRlbT4KICAgICAgICAgICAgICAgIDxlbC1tZW51LWl0ZW0gaW5kZXg9Ii9zY2VuZS9uZXciPgogICAgICAgICAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLXBsdXMiPjwvaT4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzbG90PSJ0aXRsZSI+5paw5bu65Zy65pmvPC9zcGFuPgogICAgICAgICAgICAgICAgPC9lbC1tZW51LWl0ZW0+CiAgICAgICAgICAgICAgICA8ZWwtbWVudS1pdGVtIGluZGV4PSIvc2NlbmUvbWFuYWdlIj4KICAgICAgICAgICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1zZXR0aW5nIj48L2k+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc2xvdD0idGl0bGUiPuWcuuaZr+euoeeQhjwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZWwtbWVudS1pdGVtPgogICAgICAgICAgICA8L2VsLW1lbnU+CiAgICAgICAgPC9lbC1hc2lkZT4KICAgICAgICA8ZWwtbWFpbj4KICAgICAgICAgICAgPHJvdXRlci12aWV3Pjwvcm91dGVyLXZpZXc+CiAgICAgICAgPC9lbC1tYWluPgogICAgPC9lbC1jb250YWluZXI+CjwvZGl2Pgo="}, null]}