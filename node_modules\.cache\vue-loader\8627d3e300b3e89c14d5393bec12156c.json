{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js!E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\Overview.vue?vue&type=template&id=4bc3c99a&scoped=true", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\Overview.vue", "mtime": 1754016998000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754032187340}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uIHJlbmRlcigpIHsKICB2YXIgX3ZtID0gdGhpcywKICAgIF9jID0gX3ZtLl9zZWxmLl9jOwogIHJldHVybiBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJvdmVydmlldyIKICB9LCBbX2MoImVsLXJvdyIsIHsKICAgIHN0YXRpY0NsYXNzOiAibXQtMjAiLAogICAgYXR0cnM6IHsKICAgICAgZ3V0dGVyOiAyMAogICAgfQogIH0sIFtfYygiZWwtY29sIiwgewogICAgYXR0cnM6IHsKICAgICAgc3BhbjogMjQKICAgIH0KICB9LCBbX2MoImRpdiIsIHsKICAgIHN0YXRpY0NsYXNzOiAic2VjdGlvbi1oZWFkZXIiCiAgfSwgW19jKCJoMiIsIFtfdm0uX3YoIuacgOi/kei/kOihjOWcuuaZryIpXSksIF9jKCJlbC1idXR0b24iLCB7CiAgICBhdHRyczogewogICAgICB0eXBlOiAicHJpbWFyeSIKICAgIH0sCiAgICBvbjogewogICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgIHJldHVybiBfdm0uJHJvdXRlci5wdXNoKCIvc2NlbmUvbmV3Iik7CiAgICAgIH0KICAgIH0KICB9LCBbX3ZtLl92KCIg5paw5bu65Zy65pmvICIpXSldLCAxKV0pXSwgMSksIF9jKCJlbC1yb3ciLCB7CiAgICBhdHRyczogewogICAgICBndXR0ZXI6IDIwCiAgICB9CiAgfSwgW19jKCJlbC1jb2wiLCB7CiAgICBhdHRyczogewogICAgICBzcGFuOiAyNAogICAgfQogIH0sIFtfYygiZWwtY2FyZCIsIFtfYygiZWwtdGFibGUiLCB7CiAgICBkaXJlY3RpdmVzOiBbewogICAgICBuYW1lOiAibG9hZGluZyIsCiAgICAgIHJhd05hbWU6ICJ2LWxvYWRpbmciLAogICAgICB2YWx1ZTogX3ZtLmxvYWRpbmcsCiAgICAgIGV4cHJlc3Npb246ICJsb2FkaW5nIgogICAgfV0sCiAgICBzdGF0aWNTdHlsZTogewogICAgICB3aWR0aDogIjEwMCUiCiAgICB9LAogICAgYXR0cnM6IHsKICAgICAgZGF0YTogX3ZtLnNjZW5lcwogICAgfQogIH0sIFtfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgYXR0cnM6IHsKICAgICAgcHJvcDogInNjZW5lX25hbWUiLAogICAgICBsYWJlbDogIuWcuuaZr+W<PERSON>jeensCIKICAgIH0KICB9KSwgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgIGF0dHJzOiB7CiAgICAgIGxhYmVsOiAi6L+Q6KGM54q25oCBIgogICAgfSwKICAgIHNjb3BlZFNsb3RzOiBfdm0uX3UoW3sKICAgICAga2V5OiAiZGVmYXVsdCIsCiAgICAgIGZuOiBmdW5jdGlvbiAoewogICAgICAgIHJvdwogICAgICB9KSB7CiAgICAgICAgcmV0dXJuIFtfYygiZWwtdGFnIiwgewogICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgdHlwZTogX3ZtLmdldFN0YXRlVHlwZShyb3cuc3RhdGUpCiAgICAgICAgICB9CiAgICAgICAgfSwgW192bS5fdigiICIgKyBfdm0uX3MoX3ZtLmdldFN0YXRlVGV4dChyb3cuc3RhdGUpKSArICIgIildKV07CiAgICAgIH0KICAgIH1dKQogIH0pLCBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgYXR0cnM6IHsKICAgICAgcHJvcDogImZpbmFsX3J1bm5pbmdfdGltZSIsCiAgICAgIGxhYmVsOiAi5pyA6L+R6L+Q6KGM5pe26Ze0IgogICAgfQogIH0pLCBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgYXR0cnM6IHsKICAgICAgbGFiZWw6ICLmk43kvZwiLAogICAgICB3aWR0aDogIjEwMCIKICAgIH0sCiAgICBzY29wZWRTbG90czogX3ZtLl91KFt7CiAgICAgIGtleTogImRlZmF1bHQiLAogICAgICBmbjogZnVuY3Rpb24gKHsKICAgICAgICByb3cKICAgICAgfSkgewogICAgICAgIHJldHVybiBbX2MoImVsLWJ1dHRvbiIsIHsKICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgIHR5cGU6ICJ0ZXh0IiwKICAgICAgICAgICAgc2l6ZTogInNtYWxsIgogICAgICAgICAgfSwKICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgIGNsaWNrOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgICAgcmV0dXJuIF92bS5lZGl0U2NlbmUocm93LnNjZW5lX2lkKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIFtfdm0uX3YoIiDnvJbovpEgIildKV07CiAgICAgIH0KICAgIH1dKQogIH0pXSwgMSldLCAxKV0sIDEpXSwgMSldLCAxKTsKfTsKdmFyIHN0YXRpY1JlbmRlckZucyA9IFtdOwpyZW5kZXIuX3dpdGhTdHJpcHBlZCA9IHRydWU7CmV4cG9ydCB7IHJlbmRlciwgc3RhdGljUmVuZGVyRm5zIH07"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "gutter", "span", "_v", "type", "on", "click", "$event", "$router", "push", "directives", "name", "rawName", "value", "loading", "expression", "staticStyle", "width", "data", "scenes", "prop", "label", "scopedSlots", "_u", "key", "fn", "row", "getStateType", "state", "_s", "getStateText", "size", "editScene", "scene_id", "staticRenderFns", "_withStripped"], "sources": ["E:/aaaaaaaaa/kh/src/views/Overview.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"overview\" },\n    [\n      _c(\n        \"el-row\",\n        { staticClass: \"mt-20\", attrs: { gutter: 20 } },\n        [\n          _c(\"el-col\", { attrs: { span: 24 } }, [\n            _c(\n              \"div\",\n              { staticClass: \"section-header\" },\n              [\n                _c(\"h2\", [_vm._v(\"最近运行场景\")]),\n                _c(\n                  \"el-button\",\n                  {\n                    attrs: { type: \"primary\" },\n                    on: {\n                      click: function ($event) {\n                        return _vm.$router.push(\"/scene/new\")\n                      },\n                    },\n                  },\n                  [_vm._v(\" 新建场景 \")]\n                ),\n              ],\n              1\n            ),\n          ]),\n        ],\n        1\n      ),\n      _c(\n        \"el-row\",\n        { attrs: { gutter: 20 } },\n        [\n          _c(\n            \"el-col\",\n            { attrs: { span: 24 } },\n            [\n              _c(\n                \"el-card\",\n                [\n                  _c(\n                    \"el-table\",\n                    {\n                      directives: [\n                        {\n                          name: \"loading\",\n                          rawName: \"v-loading\",\n                          value: _vm.loading,\n                          expression: \"loading\",\n                        },\n                      ],\n                      staticStyle: { width: \"100%\" },\n                      attrs: { data: _vm.scenes },\n                    },\n                    [\n                      _c(\"el-table-column\", {\n                        attrs: { prop: \"scene_name\", label: \"场景名称\" },\n                      }),\n                      _c(\"el-table-column\", {\n                        attrs: { label: \"运行状态\" },\n                        scopedSlots: _vm._u([\n                          {\n                            key: \"default\",\n                            fn: function ({ row }) {\n                              return [\n                                _c(\n                                  \"el-tag\",\n                                  {\n                                    attrs: {\n                                      type: _vm.getStateType(row.state),\n                                    },\n                                  },\n                                  [\n                                    _vm._v(\n                                      \" \" +\n                                        _vm._s(_vm.getStateText(row.state)) +\n                                        \" \"\n                                    ),\n                                  ]\n                                ),\n                              ]\n                            },\n                          },\n                        ]),\n                      }),\n                      _c(\"el-table-column\", {\n                        attrs: {\n                          prop: \"final_running_time\",\n                          label: \"最近运行时间\",\n                        },\n                      }),\n                      _c(\"el-table-column\", {\n                        attrs: { label: \"操作\", width: \"100\" },\n                        scopedSlots: _vm._u([\n                          {\n                            key: \"default\",\n                            fn: function ({ row }) {\n                              return [\n                                _c(\n                                  \"el-button\",\n                                  {\n                                    attrs: { type: \"text\", size: \"small\" },\n                                    on: {\n                                      click: function ($event) {\n                                        return _vm.editScene(row.scene_id)\n                                      },\n                                    },\n                                  },\n                                  [_vm._v(\" 编辑 \")]\n                                ),\n                              ]\n                            },\n                          },\n                        ]),\n                      }),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAW,CAAC,EAC3B,CACEF,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,OAAO;IAAEC,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAG;EAAE,CAAC,EAC/C,CACEJ,EAAE,CAAC,QAAQ,EAAE;IAAEG,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAG;EAAE,CAAC,EAAE,CACpCL,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACO,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BN,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MAAEI,IAAI,EAAE;IAAU,CAAC;IAC1BC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOX,GAAG,CAACY,OAAO,CAACC,IAAI,CAAC,YAAY,CAAC;MACvC;IACF;EACF,CAAC,EACD,CAACb,GAAG,CAACO,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,EACDN,EAAE,CACA,QAAQ,EACR;IAAEG,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAG;EAAE,CAAC,EACzB,CACEJ,EAAE,CACA,QAAQ,EACR;IAAEG,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAG;EAAE,CAAC,EACvB,CACEL,EAAE,CACA,SAAS,EACT,CACEA,EAAE,CACA,UAAU,EACV;IACEa,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,KAAK,EAAEjB,GAAG,CAACkB,OAAO;MAClBC,UAAU,EAAE;IACd,CAAC,CACF;IACDC,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC9BjB,KAAK,EAAE;MAAEkB,IAAI,EAAEtB,GAAG,CAACuB;IAAO;EAC5B,CAAC,EACD,CACEtB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEoB,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAO;EAC7C,CAAC,CAAC,EACFxB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEqB,KAAK,EAAE;IAAO,CAAC;IACxBC,WAAW,EAAE1B,GAAG,CAAC2B,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAAA,CAAU;QAAEC;MAAI,CAAC,EAAE;QACrB,OAAO,CACL7B,EAAE,CACA,QAAQ,EACR;UACEG,KAAK,EAAE;YACLI,IAAI,EAAER,GAAG,CAAC+B,YAAY,CAACD,GAAG,CAACE,KAAK;UAClC;QACF,CAAC,EACD,CACEhC,GAAG,CAACO,EAAE,CACJ,GAAG,GACDP,GAAG,CAACiC,EAAE,CAACjC,GAAG,CAACkC,YAAY,CAACJ,GAAG,CAACE,KAAK,CAAC,CAAC,GACnC,GACJ,CAAC,CAEL,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACF/B,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MACLoB,IAAI,EAAE,oBAAoB;MAC1BC,KAAK,EAAE;IACT;EACF,CAAC,CAAC,EACFxB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEqB,KAAK,EAAE,IAAI;MAAEJ,KAAK,EAAE;IAAM,CAAC;IACpCK,WAAW,EAAE1B,GAAG,CAAC2B,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAAA,CAAU;QAAEC;MAAI,CAAC,EAAE;QACrB,OAAO,CACL7B,EAAE,CACA,WAAW,EACX;UACEG,KAAK,EAAE;YAAEI,IAAI,EAAE,MAAM;YAAE2B,IAAI,EAAE;UAAQ,CAAC;UACtC1B,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;cACvB,OAAOX,GAAG,CAACoC,SAAS,CAACN,GAAG,CAACO,QAAQ,CAAC;YACpC;UACF;QACF,CAAC,EACD,CAACrC,GAAG,CAACO,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAI+B,eAAe,GAAG,EAAE;AACxBvC,MAAM,CAACwC,aAAa,GAAG,IAAI;AAE3B,SAASxC,MAAM,EAAEuC,eAAe", "ignoreList": []}]}