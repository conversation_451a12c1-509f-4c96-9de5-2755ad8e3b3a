{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js!E:\\aaaaaaaaa\\kh\\node_modules\\eslint-loader\\index.js??ref--14-0!E:\\aaaaaaaaa\\kh\\src\\router\\index.js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\router\\index.js", "mtime": 1753887158000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\eslint-loader\\index.js", "mtime": 1754032203320}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgVnVlUm91dGVyIGZyb20gJ3Z1ZS1yb3V0ZXInOwpWdWUudXNlKFZ1ZVJvdXRlcik7CmNvbnN0IHJvdXRlcyA9IFt7CiAgcGF0aDogJy8nLAogIG5hbWU6ICdPdmVydmlldycsCiAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoJy4uL3ZpZXdzL092ZXJ2aWV3LnZ1ZScpCn0sIHsKICBwYXRoOiAnL3NjZW5lL25ldycsCiAgbmFtZTogJ05ld1NjZW5lJywKICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgnLi4vdmlld3Mvc2NlbmUvTmV3U2NlbmUudnVlJykKfSwgewogIHBhdGg6ICcvc2NlbmUvZWRpdC86aWQnLAogIG5hbWU6ICdFZGl0U2NlbmUnLAogIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCcuLi92aWV3cy9zY2VuZS9FZGl0U2NlbmUudnVlJykKfSwgewogIHBhdGg6ICcvc2NlbmUvbWFuYWdlJywKICBuYW1lOiAnTWFuYWdlU2NlbmUnLAogIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCcuLi92aWV3cy9zY2VuZS9NYW5hZ2VTY2VuZS52dWUnKQp9LCB7CiAgcGF0aDogJy9zY2VuZS9oaXN0b3J5LzppZCcsCiAgbmFtZTogJ1NjZW5lSGlzdG9yeScsCiAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoJy4uL3ZpZXdzL3NjZW5lL1NjZW5lSGlzdG9yeS52dWUnKQp9XTsKY29uc3Qgcm91dGVyID0gbmV3IFZ1ZVJvdXRlcih7CiAgbW9kZTogJ2hpc3RvcnknLAogIGJhc2U6IHByb2Nlc3MuZW52LkJBU0VfVVJMLAogIHJvdXRlcwp9KTsKZXhwb3J0IGRlZmF1bHQgcm91dGVyOw=="}, {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use", "routes", "path", "name", "component", "router", "mode", "base", "process", "env", "BASE_URL"], "sources": ["E:/aaaaaaaaa/kh/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\n\nVue.use(VueRouter)\n\nconst routes = [\n    {\n        path: '/',\n        name: 'Overview',\n        component: () => import('../views/Overview.vue')\n    },\n    {\n        path: '/scene/new',\n        name: 'NewScene',\n        component: () => import('../views/scene/NewScene.vue')\n    },\n    {\n        path: '/scene/edit/:id',\n        name: 'EditScene',\n        component: () => import('../views/scene/EditScene.vue')\n    },\n    {\n        path: '/scene/manage',\n        name: 'ManageScene',\n        component: () => import('../views/scene/ManageScene.vue')\n    },\n    {\n        path: '/scene/history/:id',\n        name: 'SceneHistory',\n        component: () => import('../views/scene/SceneHistory.vue')\n    }\n]\n\nconst router = new VueRouter({\n    mode: 'history',\n    base: process.env.BASE_URL,\n    routes\n})\n\nexport default router"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAElCD,GAAG,CAACE,GAAG,CAACD,SAAS,CAAC;AAElB,MAAME,MAAM,GAAG,CACX;EACIC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB;AACnD,CAAC,EACD;EACIF,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B;AACzD,CAAC,EACD;EACIF,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B;AAC1D,CAAC,EACD;EACIF,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC;AAC5D,CAAC,EACD;EACIF,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC;AAC7D,CAAC,CACJ;AAED,MAAMC,MAAM,GAAG,IAAIN,SAAS,CAAC;EACzBO,IAAI,EAAE,SAAS;EACfC,IAAI,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC1BT;AACJ,CAAC,CAAC;AAEF,eAAeI,MAAM", "ignoreList": []}]}