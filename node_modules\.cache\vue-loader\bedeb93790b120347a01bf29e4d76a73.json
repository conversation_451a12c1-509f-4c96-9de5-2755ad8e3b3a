{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\views\\scene\\ManageScene.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\views\\scene\\ManageScene.vue", "mtime": 1754017556000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754032186551}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ManageScene.vue"], "names": [], "mappings": ";AA4EA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "ManageScene.vue", "sourceRoot": "src/views/scene", "sourcesContent": ["<template>\n    <div class=\"manage-scene\">\n        <el-card class=\"scene-list\">\n            <div slot=\"header\" class=\"header-with-search\">\n                <el-breadcrumb separator=\"/\">\n                    <el-breadcrumb-item :to=\"{ path: '/scene/manage' }\">场景管理</el-breadcrumb-item>\n                    <el-breadcrumb-item>场景列表</el-breadcrumb-item>\n                </el-breadcrumb>\n                <div class=\"search-box\">\n                    <el-input\n                        v-model=\"searchSceneName\"\n                        placeholder=\"请输入场景名称搜索\"\n                        style=\"width: 200px;\"\n                        clearable\n                        @clear=\"handleSearch\"\n                        @keyup.enter.native=\"handleSearch\"\n                    >\n                        <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handleSearch\"></el-button>\n                    </el-input>\n                </div>\n            </div>\n\n            <el-table :data=\"scenes\" v-loading=\"loading\" style=\"width: 100%\">\n                <el-table-column prop=\"scene_name\" label=\"场景名称\" min-width=\"150\"/>\n                <!-- <el-table-column prop=\"scene_description\" label=\"场景描述\" /> -->\n                <el-table-column label=\"输入平台\" min-width=\"120\">\n                    <template slot-scope=\"{ row }\">\n                        <el-tooltip :content=\"getPlatformNames(row.input_platforms)\" placement=\"top\" :disabled=\"!row.input_platforms || !row.input_platforms.length\">\n                            <span>{{ formatPlatforms(row.input_platforms) }}</span>\n                        </el-tooltip>\n                    </template>\n                </el-table-column>\n                <el-table-column label=\"输出平台\" min-width=\"120\">\n                    <template slot-scope=\"{ row }\">\n                        <el-tooltip :content=\"getPlatformNames(row.output_platforms)\" placement=\"top\" :disabled=\"!row.output_platforms || !row.output_platforms.length\">\n                            <span>{{ formatPlatforms(row.output_platforms) }}</span>\n                        </el-tooltip>\n                    </template>\n                </el-table-column>\n                <el-table-column label=\"运行频率\" min-width=\"100\">\n                    <template slot-scope=\"{ row }\">\n                        {{ formatFrequency(row.scene_running_frequency) }}\n                    </template>\n                </el-table-column>\n                <el-table-column prop=\"final_running_time\" label=\"最近运行时间\"/>\n                <el-table-column label=\"状态\" width=\"100\">\n                    <template slot-scope=\"{ row }\">\n                        <el-tag :type=\"row.state === 1 ? 'success' : 'info'\">\n                            {{ row.state === 1 ? '运行中' : (row.state === 2 ? '已暂停' : '已删除') }}\n                        </el-tag>\n                    </template>\n                </el-table-column>\n                <el-table-column label=\"操作\" width=\"350\">\n                    <template slot-scope=\"{ row }\">\n                        <el-button v-if=\"row.state !== 3\" :type=\"row.state === 1 ? 'danger' : 'success'\" @click=\"handleStateChange(row)\">\n                            {{ row.state === 1 ? '暂停' : '运行' }}\n                        </el-button>\n                        <el-button v-if=\"row.state === 2\" type=\"danger\" @click=\"handleDelete(row)\">\n                            删除\n                        </el-button>\n                        <el-button type=\"text\" @click=\"handleShowHistory(row)\">运行记录</el-button>\n                        <el-button v-if=\"row.state !== 3\" type=\"text\" @click=\"handleEditScene(row)\">场景设置</el-button>\n                    </template>\n                </el-table-column>\n            </el-table>\n\n            <div class=\"pagination\">\n                <el-pagination @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\"\n                    :current-page=\"currentPage\" :page-sizes=\"[10, 15, 20, 30]\" :page-size=\"pageSize\"\n                    layout=\"total, sizes, prev, pager, next\" :total=\"total\" />\n            </div>\n        </el-card>\n    </div>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\n\nexport default {\n    name: 'ManageScene',\n    data() {\n        return {\n            scenes: [],\n            loading: false,\n            currentPage: 1,\n            pageSize: 15,\n            total: 0,\n            searchSceneName: ''\n        }\n    },\n    computed: {\n        ...mapState(['platforms'])\n    },\n    methods: {\n        ...mapActions(['fetchPlatforms']),\n        getPlatformNames(platformIds) {\n            if (!platformIds || !platformIds.length) return ''\n            return platformIds.map(id => {\n                const platform = this.platforms.find(p => p.platform_id === id)\n                return platform ? platform.platform_name : id\n            }).join('、')\n        },\n        formatPlatforms(platformIds) {\n            if (!platformIds || !platformIds.length) return '-'\n            const platformNames = platformIds.map(id => {\n                const platform = this.platforms.find(p => p.platform_id === id)\n                return platform ? platform.platform_name : id\n            })\n            if (platformNames.length > 3) {\n                return platformNames.slice(0, 3).join('、') + '...'\n            }\n            return platformNames.join('、')\n        },\n        formatFrequency(minutes) {\n            if (!minutes || minutes <= 0) return '-'\n            \n            if (minutes >= 1440 && minutes % 1440 === 0) {\n                const days = minutes / 1440\n                return `每${days}天运行一次`\n            }\n            \n            if (minutes >= 60 && minutes % 60 === 0) {\n                const hours = minutes / 60\n                return `每${hours}小时运行一次`\n            }\n            \n            return `每${minutes}分钟运行一次`\n        },\n        async handleStateChange(scene) {\n            try {\n                await this.$http.post('', {\n                    api: '/api/scene/updateState',\n                    param: {\n                        scene_id: scene.scene_id,\n                        state: scene.state === 1 ? 2 : 1\n                    }\n                })\n                this.$message.success('状态更新成功')\n                this.fetchAllScenes();\n            } catch (error) {\n                this.$message.error('状态更新失败')\n                console.error('Error updating scene state:', error)\n            }\n        },\n        handleShowHistory(scene) {\n            this.$router.push({ \n                path: `/scene/history/${scene.scene_id}`,\n                query: { name: scene.scene_name }\n            })\n        },\n        handleEditScene(scene) {\n            this.$router.push({ path: `/scene/edit/${scene.scene_id}` })\n        },\n        async fetchAllScenes() {\n            this.loading = true\n            try {\n                const response = await this.$http.post('', {\n                    api: '/api/scene/getList',\n                    param: {\n                        page: this.currentPage,\n                        pageSize: this.pageSize,\n                        scene_name: this.searchSceneName\n                    }\n                })\n                this.scenes = response.data.data || []\n                this.scenes.forEach(scene => {\n                    const accounts = scene.accounts || []\n                    scene.input_platforms = accounts.filter(account => account.operate_type === 1).map(account => account.platform_id) || []\n                    scene.output_platforms = accounts.filter(account => account.operate_type === 2).map(account => account.platform_id) || []\n                })\n                this.total = response.data.total || 0\n            } catch (error) {\n                this.$message.error('获取场景列表失败')\n                console.error('Error fetching all scenes:', error)\n            } finally {\n                this.loading = false\n            }\n        },\n\n        handleSizeChange(val) {\n            this.pageSize = val\n            this.fetchAllScenes()\n        },\n        handleCurrentChange(val) {\n            this.currentPage = val\n            this.fetchAllScenes()\n        },\n\n        handleSearch() {\n            this.currentPage = 1\n            this.fetchAllScenes()\n        },\n        async handleDelete(scene) {\n            try {\n                await this.$confirm('确认删除该场景吗？', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                })\n                \n                await this.$http.post('', {\n                    api: '/api/scene/delete',\n                    param: {\n                        scene_id: scene.scene_id\n                    }\n                })\n                \n                this.$message.success('删除成功')\n                this.fetchAllScenes()\n            } catch (error) {\n                if (error !== 'cancel') {\n                    this.$message.error('删除失败')\n                    console.error('Error deleting scene:', error)\n                }\n            }\n        },\n\n    },\n    created() {\n        this.fetchPlatforms({ page: 1, pageSize: 1000 })\n        this.fetchAllScenes()\n    }\n}\n</script>\n\n<style scoped>\n.manage-scene {\n    padding: 20px;\n}\n\n.scene-list {\n    margin-bottom: 20px;\n}\n\n.pagination {\n    margin-top: 20px;\n    text-align: right;\n}\n\n.header-with-search {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0;\n}\n\n.search-box {\n    display: flex;\n    align-items: center;\n}\n\n.el-breadcrumb {\n    line-height: 1;\n}\n\n\n</style>"]}]}