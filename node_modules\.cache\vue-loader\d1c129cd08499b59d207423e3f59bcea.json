{"remainingRequest": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\aaaaaaaaa\\kh\\src\\App.vue?vue&type=style&index=0&id=7ba5bd90&lang=css", "dependencies": [{"path": "E:\\aaaaaaaaa\\kh\\src\\App.vue", "mtime": 1754017590000}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754032202777}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754032187306}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754032201397}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754032205007}, {"path": "E:\\aaaaaaaaa\\kh\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754032186917}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiNhcHAgewogICAgZm9udC1mYW1pbHk6ICdIZWx2ZXRpY2EgTmV1ZScsIEhlbHZldGljYSwgJ1BpbmdGYW5nIFNDJywgJ0hpcmFnaW5vIFNhbnMgR0InLAogICAgICAgICdNaWNyb3NvZnQgWWFIZWknLCAn5b6u6L2v6ZuF6buRJywgQXJpYWwsIHNhbnMtc2VyaWY7CiAgICAtd2Via2l0LWZvbnQtc21vb3RoaW5nOiBhbnRpYWxpYXNlZDsKICAgIC1tb3otb3N4LWZvbnQtc21vb3RoaW5nOiBncmF5c2NhbGU7CiAgICBjb2xvcjogIzJjM2U1MDsKfQoKLmVsLWNvbnRhaW5lciB7CiAgICBoZWlnaHQ6IDEwMHZoOwp9CgouZWwtYXNpZGUgewogICAgYmFja2dyb3VuZC1jb2xvcjogIzMwNDE1NjsKICAgIGNvbG9yOiB3aGl0ZTsKfQoKLmxvZ28gewogICAgcGFkZGluZzogMjBweDsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjNDM0YTUwOwogICAgZGlzcGxheTogZmxleDsKICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwp9CgoubG9nby1pbWFnZSB7CiAgICB3aWR0aDogNDBweDsKICAgIGhlaWdodDogNDBweDsKICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICBib3JkZXItcmFkaXVzOiA0cHg7Cn0KCi5sb2dvLWljb24gewogICAgZm9udC1zaXplOiAzMnB4OwogICAgY29sb3I6ICM0MDlFRkY7CiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Owp9CgoubG9nbyBoMyB7CiAgICBjb2xvcjogI2JmY2JkOTsKICAgIG1hcmdpbjogMDsKICAgIGZvbnQtc2l6ZTogMTZweDsKICAgIGZvbnQtd2VpZ2h0OiA0MDA7Cn0KCi5lbC1tZW51IHsKICAgIGJvcmRlci1yaWdodDogbm9uZTsKfQoKLmVsLW1haW4gewogICAgcGFkZGluZzogMjBweDsKICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7CiAgICBvdmVyZmxvdy15OiBhdXRvOwp9Cg=="}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AA4CA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n    <div id=\"app\">\n        <el-container>\n            <el-aside width=\"200px\">\n                <div class=\"logo\">\n                    <img v-if=\"logoImage\" :src=\"logoImage\" alt=\"Logo\" class=\"logo-image\" />\n                    <i v-else class=\"el-icon-s-data logo-icon\"></i>\n                    <h3>AI自适应系统</h3>\n                </div>\n                <el-menu mode=\"vertical\" router :default-active=\"$route.path\" background-color=\"#304156\"\n                    text-color=\"#bfcbd9\" active-text-color=\"#409EFF\">\n                    <el-menu-item index=\"/\">\n                        <i class=\"el-icon-pie-chart\"></i>\n                        <span slot=\"title\">总览</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/scene/new\">\n                        <i class=\"el-icon-plus\"></i>\n                        <span slot=\"title\">新建场景</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/scene/manage\">\n                        <i class=\"el-icon-setting\"></i>\n                        <span slot=\"title\">场景管理</span>\n                    </el-menu-item>\n                </el-menu>\n            </el-aside>\n            <el-main>\n                <router-view></router-view>\n            </el-main>\n        </el-container>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'App',\n    data() {\n        return {\n            logoImage: null\n        }\n    }\n}\n</script>\n\n<style>\n#app {\n    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',\n        'Microsoft YaHei', '微软雅黑', Arial, sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    color: #2c3e50;\n}\n\n.el-container {\n    height: 100vh;\n}\n\n.el-aside {\n    background-color: #304156;\n    color: white;\n}\n\n.logo {\n    padding: 20px;\n    text-align: center;\n    border-bottom: 1px solid #434a50;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n}\n\n.logo-image {\n    width: 40px;\n    height: 40px;\n    margin-bottom: 10px;\n    border-radius: 4px;\n}\n\n.logo-icon {\n    font-size: 32px;\n    color: #409EFF;\n    margin-bottom: 10px;\n}\n\n.logo h3 {\n    color: #bfcbd9;\n    margin: 0;\n    font-size: 16px;\n    font-weight: 400;\n}\n\n.el-menu {\n    border-right: none;\n}\n\n.el-main {\n    padding: 20px;\n    background-color: #f5f7fa;\n    overflow-y: auto;\n}\n</style>"]}]}